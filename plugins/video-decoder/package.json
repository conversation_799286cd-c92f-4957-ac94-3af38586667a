{"name": "@rtc-plugin/video-decoder", "version": "1.0.0", "description": "Video decoder plugin for TRTC", "main": "dist/index.umd.js", "types": "dist/index.d.ts", "scripts": {}, "dependencies": {"fastrx": "^3.3.1", "jv4-decoder": "^1.1.5", "trtc-js-sdk-core": "workspace:*"}, "peerDependencies": {"trtc-js-sdk-core": "*"}, "devDependencies": {"@types/node": "^16.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.0", "rimraf": "^3.0.2", "typescript": "^4.9.0"}}