!function(e){function t(t){for(var n,i,c=t[0],u=t[1],s=t[2],d=0,f=[];d<c.length;d++)i=c[d],Object.prototype.hasOwnProperty.call(a,i)&&a[i]&&f.push(a[i][0]),a[i]=0;for(n in u)Object.prototype.hasOwnProperty.call(u,n)&&(e[n]=u[n]);for(l&&l(t);f.length;)f.shift()();return o.push.apply(o,s||[]),r()}function r(){for(var e,t=0;t<o.length;t++){for(var r=o[t],n=!0,c=1;c<r.length;c++){var u=r[c];0!==a[u]&&(n=!1)}n&&(o.splice(t--,1),e=i(i.s=r[0]))}return e}var n={},a={0:0},o=[];function i(t){if(n[t])return n[t].exports;var r=n[t]={i:t,l:!1,exports:{}};return e[t].call(r.exports,r,r.exports,i),r.l=!0,r.exports}i.m=e,i.c=n,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(r,n,function(t){return e[t]}.bind(null,n));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="";var c=window.webpackJsonp=window.webpackJsonp||[],u=c.push.bind(c);c.push=t,c=c.slice();for(var s=0;s<c.length;s++)t(c[s]);var l=u;o.push([262,1,2]),r()}({104:function(e,t,r){"use strict";var n=r(28);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(105),o=n(r(45)),i=(0,a.styled)(o.default)({background:"linear-gradient(45deg, #2196f3 30%, #2196f3 90%)",border:0,borderRadius:3,boxShadow:"0 2px 2px 2px rgba(33, 150, 243, 0.15)",color:"white",height:36,padding:"0 15px"});t.default=i},142:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NETWORK_QUALITY=void 0,t.getLanguage=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"trtc-detect-language",r=n("lang")||localStorage.getItem(t)||(null===(e=window.navigator.language)||void 0===e?void 0:e.toLowerCase());return r=r.indexOf("zh")>-1?"zh":"en",console.warn(r),r},t.getUrlParam=n,t.isUndefined=t.isBoolean=void 0;t.isUndefined=function(e){return void 0===e};t.isBoolean=function(e){return"boolean"==typeof e};function n(e){var t=decodeURI(window.location.href.replace(/^[^?]*\?/,"")),r=new RegExp("(^|&)".concat(e,"=([^&#]*)(&|$|)"),"i"),n=t.match(r);return n?n[2]:null}t.NETWORK_QUALITY={0:"未知",1:"极佳",2:"较好",3:"一般",4:"差",5:"极差",6:"断开"}},148:function(e,t,r){"use strict";var n=r(28);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return a.default.createElement("div",{className:"loading"},a.default.createElement("div",{className:"clock-loader"}))};var a=n(r(0));r(330)},262:function(e,t,r){"use strict";var n=r(28),a=n(r(0)),o=n(r(12)),i=n(r(267));o.default.render(a.default.createElement(i.default,null),document.getElementById("root"))},267:function(e,t,r){"use strict";var n=r(28),a=r(25);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.IS_WX=void 0;var o=n(r(31)),i=v(r(0)),c=n(r(271)),u=n(r(286));r(334);var s=n(r(84));r(336);var l=v(r(339)),d=r(105),f=n(r(45)),p=r(50);function m(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(m=function(e){return e?r:t})(e)}function v(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=m(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=o?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(n,i,c):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}var h=navigator.userAgent,g=/MicroMessenger/i.test(h);t.IS_WX=g;var y=function(){l.toPng(document.getElementById("capture")).then((function(e){var t=document.createElement("a");t.download="rtc-detect",t.href=e,t.click()}))},b=(0,d.styled)(f.default)({background:"linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)",border:0,borderRadius:3,boxShadow:"0 3px 5px 2px rgba(255, 105, 135, .3)",color:"white",height:36,padding:"0 15px"});var w=function(){var e=(0,p.useTranslation)().t,t=(0,i.useState)(!1),r=(0,o.default)(t,2),n=r[0],a=r[1];return i.default.createElement(s.default,{container:!0,justifyContent:"center"},i.default.createElement(s.default,{item:!0,xl:4,lg:6,md:11,sm:11,xs:11},i.default.createElement("div",{id:"capture"},i.default.createElement("div",{className:"headline"},i.default.createElement(c.default,null),!g&&n&&i.default.createElement("div",{className:"bottom"},i.default.createElement("div",{className:"downloadIcon",onClick:y},i.default.createElement("svg",{t:"1679388965666",className:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"4957",width:"200",height:"200"},i.default.createElement("path",{d:"M531.36384 796.86144a30.72 30.72 0 0 1-43.84768 0l-266.0096-271.02208C202.43456 506.40384 216.20224 473.6 243.4304 473.6h532.0192c27.22816 0 40.99584 32.80384 21.92384 52.23936l-266.0096 271.02208zM153.6 921.6a30.72 30.72 0 0 1 30.72-30.72h655.36a30.72 30.72 0 0 1 30.72 30.72v20.48a30.72 30.72 0 0 1-30.72 30.72H184.32a30.72 30.72 0 0 1-30.72-30.72v-20.48z",fill:"#2196f3","p-id":"4958"}),i.default.createElement("path",{d:"M583.68 51.2a30.72 30.72 0 0 1 30.72 30.72v532.48a30.72 30.72 0 0 1-30.72 30.72H440.32a30.72 30.72 0 0 1-30.72-30.72V81.92a30.72 30.72 0 0 1 30.72-30.72h143.36z",fill:"#2196f3","p-id":"4959"}))))),i.default.createElement(u.default,{handleLoaded:function(){a(!0)}})),!g&&n&&i.default.createElement("div",{className:"bottom"},i.default.createElement(b,{onClick:y},e("生成报告图")))))};t.default=w},271:function(e,t,r){"use strict";var n=r(28),a=r(25);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(31)),i=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=f(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=o?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(n,i,c):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(0));r(272);var c=n(r(84)),u=n(r(85)),s=r(50),l=n(r(278)),d=r(142);function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(f=function(e){return e?r:t})(e)}var p=function(){var e=(0,i.useState)("en"),t=(0,o.default)(e,2),r=t[0],n=t[1],a=i.default.useState(null),f=(0,o.default)(a,2),p=f[0],m=f[1],v=(0,s.useTranslation)().i18n;(0,i.useEffect)((function(){h((0,d.getLanguage)())}),[]);var h=function(e){v.changeLanguage(e),localStorage.setItem("trtc-detect-language",e),document.title="zh"===e?"TRTC 能力检测":"TRTC Capability Detection",n(e)},g=Boolean(p),y=g?"simple-popover":void 0;return i.default.createElement(c.default,{container:!0,justifyContent:"space-between"},i.default.createElement(c.default,{item:!0},i.default.createElement("div",{className:"header"},"zh"===r&&i.default.createElement("img",{src:"https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/trtc-logo-cn-b.png"}),"en"===r&&i.default.createElement("img",{src:"https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/trtc-logo-en-b.png"}))),i.default.createElement(c.default,{item:!0},i.default.createElement("div",{className:"header"},window.innerWidth>385&&i.default.createElement("div",{className:"qrcode",onClick:function(e){m(e.currentTarget)},"aria-describedby":y},i.default.createElement("svg",{t:"1646118025166",className:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1245",width:"24",height:"24"},i.default.createElement("path",{d:"M491.925333 532.074667V853.333333H170.666667V532.074667h321.258666zM597.333333 789.333333v64h-64v-64h64z m256-85.333333v149.333333h-128v-64h64v-85.333333h64z m-425.408-107.925333H234.666667V789.333333h193.258666v-193.258666zM661.333333 597.333333v128h64v64h-128v-192h64z m-277.333333 42.666667v106.666667h-106.666667v-106.666667h106.666667z m405.333333 0v64h-64v-64h64z m64-106.666667v106.666667h-64v-42.666667h-64v-64h128z m-256 0v64h-64v-64h64zM491.925333 170.666667v321.258666H170.666667V170.666667h321.258666zM853.333333 170.666667v321.258666H532.074667V170.666667H853.333333z m-425.408 64H234.666667v193.258666h193.258666V234.666667zM789.333333 234.666667h-193.258666v193.258666H789.333333V234.666667z m-405.333333 42.666666v106.666667h-106.666667v-106.666667h106.666667z m362.666667 0v106.666667h-106.666667v-106.666667h106.666667z","p-id":"1246",fill:"#b9b9b9"}))),"zh"===r&&i.default.createElement("div",{className:"icon-lang",onClick:function(){return h("en")}},i.default.createElement("img",{src:"https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png",height:"24px"})),"en"===r&&i.default.createElement("div",{className:"icon-lang",onClick:function(){return h("zh")}},i.default.createElement("img",{src:"https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/en.png",height:"24px"}))),i.default.createElement(u.default,{id:y,open:g,anchorEl:p,onClose:function(){m(null)},anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"}},i.default.createElement("div",{className:"phone"},i.default.createElement(l.default,{value:window.location.href,size:200,fgColor:"#000000"})))))};t.default=p},272:function(e,t,r){var n=r(273);"string"==typeof n&&(n=[[e.i,n,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};r(65)(n,a);n.locals&&(e.exports=n.locals)},273:function(e,t,r){(t=r(64)(!1)).push([e.i,".header{height:80px;display:flex;align-items:center;justify-content:space-between;user-select:none}.header>img{height:36px}@media(max-width: 400px){.header>img{height:28px}}.header .title{padding:0 40px;line-height:64px;overflow:hidden;width:100%}.header .qrcode{cursor:pointer;user-select:none;-webkit-user-select:none}.header .button-wrapper{display:flex;justify-content:center;align-items:center;height:100%;padding:0 40px}.header .icon-lang{padding:10px;cursor:pointer;user-select:none}.phone{padding:6px}",""]),e.exports=t},286:function(e,t,r){"use strict";var n=r(28),a=r(25);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,s.useState)(!1),r=(0,c.default)(t,2),n=r[0],a=r[1],f=(0,s.useState)({}),m=(0,c.default)(f,2),v=m[0],h=m[1];return(0,s.useEffect)((function(){(function(){var e=(0,i.default)(o.default.mark((function e(){var t,r;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=u.default.createStream({video:!0,audio:!1}),e.prev=1,e.next=4,t.initialize();case 4:v.video=!0,h(v),t&&t.close(),e.next=22;break;case 9:e.prev=9,e.t0=e.catch(1),v.video=!1,h(v),e.t1=e.t0.name,e.next="NotReadableError"===e.t1?16:"NotAllowedError"===e.t1?18:21;break;case 16:return console.log(e.t0.name),e.abrupt("break",22);case 18:return e.t0.message.includes("Permission denied by system")||console.log("You refused to share the screen"),console.log("Permission denied by system"),e.abrupt("break",22);case 21:return e.abrupt("break",22);case 22:return r=u.default.createStream({video:!1,audio:!0}),e.prev=23,e.next=26,r.initialize();case 26:v.audio=!0,h(v),r&&r.close(),e.next=44;break;case 31:e.prev=31,e.t2=e.catch(23),v.audio=!1,h(v),e.t3=e.t2.name,e.next="NotReadableError"===e.t3?38:"NotAllowedError"===e.t3?40:43;break;case 38:return console.log(e.t2.name),e.abrupt("break",44);case 40:return e.t2.message.includes("Permission denied by system")||console.log("You refused to share the screen"),console.log("Permission denied by system"),e.abrupt("break",44);case 43:return e.abrupt("break",44);case 44:p(v.video)&&p(v.audio)&&a(!0);case 45:case"end":return e.stop()}}),e,null,[[1,9],[23,31]])})));return function(){return e.apply(this,arguments)}})()()}),[]),s.default.createElement("div",{className:"block"},n?s.default.createElement(l.default,{mark:v,handleLoaded:e.handleLoaded}):s.default.createElement(d.default,null))};var o=n(r(66)),i=n(r(67)),c=n(r(31)),u=n(r(99)),s=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=f(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=o?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(n,i,c):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(0)),l=n(r(288)),d=n(r(148));function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(f=function(e){return e?r:t})(e)}var p=function(e){return"boolean"==typeof e}},288:function(e,t,r){"use strict";var n=r(28),a=r(25);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t,r,n,a,s,p,b,C,T=S(),j=k(),P=E(),I=(0,u.useState)({}),N=(0,c.default)(I,2),R=N[0],M=N[1],A=(0,u.useState)({}),_=(0,c.default)(A,2),D=_[0],L=_[1],z=(0,u.useState)({}),W=(0,c.default)(z,2),F=W[0],V=W[1],B=(0,u.useState)(0),H=(0,c.default)(B,2),U=H[0],G=H[1],Q=(0,u.useState)(0),q=(0,c.default)(Q,2),Y=q[0],$=q[1],K=(0,u.useState)(),X=(0,c.default)(K,2),J=X[0],Z=X[1],ee=(0,u.useState)(),te=(0,c.default)(ee,2),re=te[0],ne=te[1],ae=(0,u.useState)(),oe=(0,c.default)(ae,2),ie=oe[0],ce=oe[1],ue=(0,u.useState)(),se=(0,c.default)(ue,2),le=se[0],de=se[1],fe=(0,u.useState)(),pe=(0,c.default)(fe,2),me=pe[0],ve=pe[1],he=(0,u.useState)(),ge=(0,c.default)(he,2),ye=ge[0],be=ge[1],we=(0,u.useState)(),xe=(0,c.default)(we,2),Se=xe[0],Ee=xe[1],ke=(0,u.useState)(!0),Oe=(0,c.default)(ke,2),Ce=Oe[0],Te=Oe[1],je=(0,u.useState)(!1),Pe=(0,c.default)(je,2),Ie=Pe[0],Ne=Pe[1],Re=(0,h.useTranslation)().t,Me={basic:{title:Re("基础环境"),params:R},api:{title:Re("API 支持"),params:F},codec:{title:Re("编码支持"),params:D}};(0,u.useEffect)((function(){(function(e){for(var t=window.location.search.substring(1).split("&"),r=0;r<t.length;r++){var n=t[r].split("=");if(n[0]===e)return n[1]}return!1})("network")&&Ne(!0),function(){var t=(0,i.default)(o.default.mark((function t(){var r,n,a,i,c,u,s,l,d,f,p,m,v,h,g,y,b,S,E,k,O,C,T,j,P,I,N,R,A,_,D,z,W;return o.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,x.isTRTCSupported();case 2:if(r=t.sent,Ee(r),n=x.report){t.next=7;break}return t.abrupt("return");case 7:Te(!1),e.handleLoaded(),a=n.system,i=n.APISupported,c=n.codecsSupported,u=n.devices,w.infoAll(n),s=c.isH264DecodeSupported,l=c.isH264EncodeSupported,d=c.isVp8DecodeSupported,f=c.isVp8EncodeSupported,L({h264Encode:{title:"H264 编码",desc:"浏览器支持 H264 编码，则可以支持推本地流",result:l},h264Decode:{title:"H264 解码",desc:"浏览器支持 H264 解码，则可以支持拉远端流",result:s},vp8Encode:{title:"VP8 编码",result:f},vp8Decode:{title:"VP8 解码",result:d}}),p=a.UA,m=a.OS,v=a.browser,h=v.name,g=v.version,y=a.displayResolution,b=y.width,S=y.height,E=a.hardwareConcurrency,k={OS:{title:"操作系统",result:m},browser:{title:"浏览器",result:"".concat(h,"/").concat(g||"")},UA:{title:"UA",result:p},resolution:{title:"屏幕分辨率",result:"".concat(b," x ").concat(S)}},E&&(k.hardwareConcurrency={title:"逻辑处理器数量",result:"".concat(E)}),M(k),O=i.isUserMediaSupported,C=i.isWebRTCSupported,T=i.isWebSocketSupported,j=i.isWebAudioSupported,P=i.isScreenCaptureAPISupported,I=i.isCanvasCapturingSupported,N=i.isVideoCapturingSupported,R=i.isApplyConstraintsSupported,V({isUserMediaSupported:{title:"是否支持获取媒体设备及媒体流",desc:"是否可以从摄像头和麦克风采集视频和音频",result:O},isScreenCaptureAPISupported:{title:"是否支持屏幕分享",result:P},isWebRTCSupported:{title:"是否支持 WebRTC",result:C},isWebAudioSupported:{title:"是否支持 WebAudio",result:j},isWebSocketSupported:{title:"是否支持 WebSocket",result:T},isCanvasCapturingSupported:{title:"是否支持从 Canvas 获取数据流",result:I},isVideoCapturingSupported:{title:"是否支持从 Video 获取数据流",result:N},isApplyConstraintsSupported:{title:"是否支持 applyConstraints",desc:"MediaStreamTrack 是否有 applyConstraints 方法",result:R}}),A=u.hasCameraPermission,_=u.hasMicrophonePermission,D=u.cameras,z=u.microphones,W=u.speakers,be({cam:{title:"是否允许使用摄像头",desc:"是否允许使用摄像头",result:A||!1},microphone:{title:"是否允许使用麦克风",desc:"是否允许使用麦克风",result:_||!1}}),Z(z),ne(W),ce(D),de(A),ve(_);case 29:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}()()}),[]);var Ae=(null==F||null===(t=F.isWebRTCSupported)||void 0===t?void 0:t.result)&&(null==F||null===(r=F.isWebSocketSupported)||void 0===r?void 0:r.result),_e=(null==F||null===(n=F.isCanvasCapturingSupported)||void 0===n?void 0:n.result)||(null==F||null===(a=F.isVideoCapturingSupported)||void 0===a?void 0:a.result)||(null==ye||null===(s=ye.cam)||void 0===s?void 0:s.result)||(null==ye||null===(p=ye.cam)||void 0===p?void 0:p.result),De=Ae&&(null==D||null===(b=D.h264Encode)||void 0===b?void 0:b.result)&&_e,Le=Ae&&(null==D||null===(C=D.h264Decode)||void 0===C?void 0:C.result);return u.default.createElement("div",{className:"block"},Ce&&u.default.createElement("div",{className:"cover"},u.default.createElement(g.default,null)),u.default.createElement("div",{className:"panel-item"},u.default.createElement(l.default,{expanded:!0,square:!0,classes:T},u.default.createElement(d.default,{"aria-controls":"panel1a-content",id:"panel1a-header",classes:j},u.default.createElement("div",{className:"key"},Re("检测结果概览"))),u.default.createElement(f.default,{classes:P},u.default.createElement("div",null,u.default.createElement("div",{className:"key",style:{margin:"1em 0",color:"#F56C6C"}},Re("http"),u.default.createElement("a",{style:{textDecoration:"none",color:"#2196f3"},target:"_blank",href:"https://cloud.tencent.com/document/product/647/32398#url-.E5.9F.9F.E5.90.8D.E5.8D.8F.E8.AE.AE.E9.99.90.E5.88.B6",rel:"noreferrer"},Re("refer"))),u.default.createElement("div",{className:"key-item"},u.default.createElement("div",{className:"key"},Re("是否支持 TRTC"),"：",Se&&Se.result?u.default.createElement("span",{className:"support"},Re("是")):Ae?u.default.createElement("span",{className:"support-half"},Re("部分支持")):u.default.createElement("span",{className:"support-failed"},Re("否")))),Se&&!Se.result&&u.default.createElement("span",{className:"support-failed"},Se.reason),u.default.createElement("div",{className:"key-item",key:"join"},u.default.createElement("div",{className:"key"},Re("是否支持进房【 检测项 3 && 5 】"),"：",Ae?u.default.createElement("span",{className:"support"},Re("是")):u.default.createElement("span",{className:"support-failed"},Re("否")))),u.default.createElement("div",{className:"key-item",key:"publish"},u.default.createElement("div",{className:"key"},Re("是否支持推流【 检测项 3 && 5 && 11 && (6 || 7 || 15 || 16) 】"),"：",De?u.default.createElement("span",{className:"support"},Re("是")):u.default.createElement("span",{className:"support-failed"},Re("否")))),u.default.createElement("div",{className:"publish-desc"},Re("如果 15 或 16 支持，您可以从媒体设备获取数据流。如果 6 或 7 支持，您可以从 Video 或者 Canvas 采集数据流。")),u.default.createElement("div",{className:"key-item",key:"subscribe"},u.default.createElement("div",{className:"key"},Re("是否支持拉流【 检测项 3 && 5 && 12 】"),"：",Le?u.default.createElement("span",{className:"support"},Re("是")):u.default.createElement("span",{className:"support-failed"},Re("否")))))))),Object.keys(Me).map((function(e){return u.default.createElement("div",{key:e,className:"panel-item"},u.default.createElement(l.default,{expanded:!0,square:!0,classes:T},u.default.createElement(d.default,{"aria-controls":"panel1a-content",id:"panel1a-header",classes:j},u.default.createElement("div",{className:"key"},Me[e].title)),u.default.createElement(f.default,{classes:P},"basic"===e&&O(Me[e].params),"api"===e&&O(Me[e].params,0),"codec"===e&&O(Me[e].params,10))))})),u.default.createElement("div",{className:"panel-item"},u.default.createElement(l.default,{expanded:!0,square:!0,classes:T},u.default.createElement(d.default,{"aria-controls":"panel1a-content",id:"panel1a-header",classes:j},u.default.createElement("div",{className:"key"},Re("设备详情"))),u.default.createElement(f.default,{classes:P},u.default.createElement("div",{className:"device-info"},u.default.createElement("div",{className:"key-item"},u.default.createElement("div",{className:le?"id":"id-failed"},"15"),u.default.createElement("div",{className:"key"},Re("是否允许使用摄像头"),"：",le?u.default.createElement("span",{className:"support"},Re("是")):u.default.createElement("span",{className:"support-failed"},Re("否")))),u.default.createElement("div",{className:"key-item"},u.default.createElement("div",{className:me?"id":"id-failed"},"16"),u.default.createElement("div",{className:"key"},Re("是否允许使用麦克风"),"：",me?u.default.createElement("span",{className:"support"},Re("是")):u.default.createElement("span",{className:"support-failed"},Re("否")))),J&&J.length>0&&u.default.createElement("div",{className:"device-list"},u.default.createElement("div",{className:"device-card-title"},Re("麦克风设备列表")),u.default.createElement("div",{className:"device-item"},null==J?void 0:J.map((function(e,t){return u.default.createElement("div",{key:e.deviceId+t},e.label)})))),ie&&ie.length>0&&u.default.createElement("div",{className:"device-list"},u.default.createElement("div",{className:"device-card-title"},Re("摄像头设备列表")),u.default.createElement("div",{className:"device-item"},null==ie?void 0:ie.map((function(e,t){return u.default.createElement("div",{key:e.deviceId+t},e.label,"    ",e.resolution&&e.resolution.maxWidth?"".concat(Re("最大分辨率"),"：").concat(e.resolution.maxWidth," x ").concat(e.resolution.maxHeight):"","    ",e.resolution&&e.resolution.maxFrameRate?"".concat(Re("最大帧率"),"：").concat(e.resolution.maxFrameRate.toFixed(0)):"")})))),re&&re.length>0&&u.default.createElement("div",{className:"device-list"},u.default.createElement("div",{className:"device-card-title"},Re("扬声器设备列表")),u.default.createElement("div",{className:"device-item"},null==re?void 0:re.map((function(e,t){return u.default.createElement("div",{key:e.deviceId+t},e.label)})))))))),u.default.createElement("div",{className:"panel-item"},u.default.createElement(l.default,{expanded:!0,square:!0,classes:T},u.default.createElement(d.default,{"aria-controls":"panel1a-content",id:"panel1a-header",classes:j},u.default.createElement("div",{className:"key"},Re("设备支持度检测"))),u.default.createElement(f.default,{classes:P},0===U&&u.default.createElement("div",{className:"start-detect"},u.default.createElement(y.default,{onClick:function(){return G(1)}},Re("开始检测"))),1===U&&u.default.createElement(m.default,{mark:e.mark})))),Ie&&u.default.createElement("div",{className:"panel-item"},u.default.createElement(l.default,{expanded:!0,square:!0,classes:T},u.default.createElement(d.default,{"aria-controls":"panel1a-content",id:"panel1a-header",classes:j},u.default.createElement("div",{className:"key"},Re("网络检测"))),u.default.createElement(f.default,{classes:P},0===Y?u.default.createElement("div",{className:"start-detect"},u.default.createElement(y.default,{onClick:function(){return $(1)}},Re("开始检测")),u.default.createElement("div",{className:"tip"},Re("检测网络前需要先登录，如果您没有登录的话会自动跳转登录"))):u.default.createElement(v.default,{handleReport:function(e){w.infoAll(e)}})))))};var o=n(r(66)),i=n(r(67)),c=n(r(31)),u=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=b(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=o?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(n,i,c):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(0));r(289);var s=n(r(291)),l=n(r(100)),d=n(r(101)),f=n(r(102)),p=r(105),m=n(r(293)),v=n(r(308)),h=r(50),g=n(r(148)),y=n(r(104));function b(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(b=function(e){return e?r:t})(e)}var w=new window.Aegis({id:"CUURTHMqVFbioiGGSC",reportApiSpeed:!0,reportAssetSpeed:!0,hostUrl:"https://tamaegis.com",pagePerformance:!0,spa:!0}),x=new s.default,S=(0,p.makeStyles)((function(){return{root:{width:"100%",boxShadow:"none",border:"1px solid #ddd",backgroundImage:"url(https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/mark.png)"}}})),E=(0,p.makeStyles)((function(){return{root:{padding:"8px 16px"}}})),k=(0,p.makeStyles)((function(){return{root:{color:"white",backgroundColor:"#2196f3",minHeight:"0 !important",cursor:"default !important",lineHeight:"22px"},content:{margin:"10px 0 !important"},expanded:{margin:"0 !important:",minHeight:"0px"}}}));function O(e,t){var r=(0,h.useTranslation)().t,n=t,a=Object.keys(e);return u.default.createElement("div",null,a.map((function(t){return n+=1,u.default.createElement(u.default.Fragment,{key:"".concat(t,"wrapper")},"string"==typeof e[t].result?u.default.createElement("div",{key:t,className:"key-item"},n?u.default.createElement("div",{className:"id"},n):null,u.default.createElement("div",{className:"key"},r(e[t].title),"："),r(e[t].result)):u.default.createElement("div",{key:t,className:"key-item"},n?u.default.createElement("div",{className:e[t].result?"id":"id-failed"},n):null,u.default.createElement("div",{className:"key"},r(e[t].title),"：",e[t].result?u.default.createElement("span",{className:"support"},r("是")):u.default.createElement("span",{className:"support-failed"},r("否")))),e[t].desc&&u.default.createElement("div",{className:"desc"},r(e[t].desc)))})))}},289:function(e,t,r){var n=r(290);"string"==typeof n&&(n=[[e.i,n,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};r(65)(n,a);n.locals&&(e.exports=n.locals)},290:function(e,t,r){(t=r(64)(!1)).push([e.i,".id{background-color:#4caf50;color:#fff;width:20px;height:20px;display:flex;justify-content:center;align-items:center;border-radius:50%;margin-right:10px}.id-failed{background-color:#f44336;color:#fff;width:20px;height:20px;display:flex;justify-content:center;align-items:center;border-radius:50%;margin-right:10px}.block{padding:0;position:relative}.panel-item{padding-bottom:20px}.key-item{font-size:14px;font-weight:350;line-height:20px;margin:1em 0;display:flex}.key{font-weight:700}.desc{font-weight:400;font-size:12px;color:rgba(0,0,0,.7);margin-bottom:20px;padding-left:30px}.publish-desc{font-weight:400;font-size:12px;color:rgba(0,0,0,.7);margin-bottom:20px}.start-detect{display:flex;flex-direction:column;justify-content:space-around;align-items:center;height:120px;width:100%}.tip{font-size:14px;color:rgba(0,0,0,.8)}.cover{background:#fff;position:absolute;width:100%;height:100%;z-index:999}.description{background-color:#e6f7ff;padding:10px 16px;margin-bottom:20px;font-size:16px}",""]),e.exports=t},291:function(e,t,r){(function(t){e.exports=function(){function e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function r(t){for(var r=1;r<arguments.length;r++){var n=null!=arguments[r]?arguments[r]:{};r%2?e(Object(n),!0).forEach((function(e){u(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):e(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function n(){n=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",i=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var a=t&&t.prototype instanceof f?t:f,o=Object.create(a.prototype),i=new k(n||[]);return o._invoke=function(e,t,r){var n="suspendedStart";return function(a,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===a)throw o;return{value:void 0,done:!0}}for(r.method=a,r.arg=o;;){var i=r.delegate;if(i){var c=x(i,r);if(c){if(c===d)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=l(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===d)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(e,r,i),o}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function f(){}function p(){}function m(){}var v={};u(v,o,(function(){return this}));var h=Object.getPrototypeOf,g=h&&h(h(O([])));g&&g!==t&&r.call(g,o)&&(v=g);var y=m.prototype=f.prototype=Object.create(v);function b(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function n(a,o,i,c){var u=l(e[a],e,o);if("throw"!==u.type){var s=u.arg,d=s.value;return d&&"object"==typeof d&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,i,c)}),(function(e){n("throw",e,i,c)})):t.resolve(d).then((function(e){s.value=e,i(s)}),(function(e){return n("throw",e,i,c)}))}c(u.arg)}var a;this._invoke=function(e,r){function o(){return new t((function(t,a){n(e,r,t,a)}))}return a=a?a.then(o,o):o()}}function x(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,x(e,t),"throw"===t.method))return d;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return d}var n=l(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,d;var a=n.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function O(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:C}}function C(){return{value:void 0,done:!0}}return p.prototype=m,u(y,"constructor",m),u(m,"constructor",p),p.displayName=u(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,u(e,c,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),u(w.prototype,i,(function(){return this})),e.AsyncIterator=w,e.async=function(t,r,n,a,o){void 0===o&&(o=Promise);var i=new w(s(t,r,n,a),o);return e.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},b(y),u(y,c,"Generator"),u(y,o,(function(){return this})),u(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=O,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return i.type="throw",i.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],i=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),u=r.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,d):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;E(r)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:O(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),d}},e}function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t,r,n,a,o,i){try{var c=e[o](i),u=c.value}catch(e){return void r(e)}c.done?t(u):Promise.resolve(u).then(n,a)}function i(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function c(e){o(i,n,a,c,u,"next",e)}function u(e){o(i,n,a,c,u,"throw",e)}c(void 0)}))}}function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function u(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==t?t:"undefined"!=typeof self?self:{},d=function(e){return e&&e.Math==Math&&e},f=d("object"==typeof globalThis&&globalThis)||d("object"==typeof window&&window)||d("object"==typeof self&&self)||d("object"==typeof l&&l)||function(){return this}()||Function("return this")(),p={},m=function(e){try{return!!e()}catch(e){return!0}},v=!m((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),h=!m((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),g=h,y=Function.prototype.call,b=g?y.bind(y):function(){return y.apply(y,arguments)},w={},x={}.propertyIsEnumerable,S=Object.getOwnPropertyDescriptor,E=S&&!x.call({1:2},1);w.f=E?function(e){var t=S(this,e);return!!t&&t.enumerable}:x;var k,O,C=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},T=h,j=Function.prototype,P=j.call,I=T&&j.bind.bind(P,P),N=function(e){return T?I(e):function(){return P.apply(e,arguments)}},R=N,M=R({}.toString),A=R("".slice),_=function(e){return A(M(e),8,-1)},D=_,L=N,z=function(e){if("Function"===D(e))return L(e)},W=m,F=_,V=Object,B=z("".split),H=W((function(){return!V("z").propertyIsEnumerable(0)}))?function(e){return"String"==F(e)?B(e,""):V(e)}:V,U=function(e){return null==e},G=U,Q=TypeError,q=function(e){if(G(e))throw Q("Can't call method on "+e);return e},Y=H,$=q,K=function(e){return Y($(e))},X="object"==typeof document&&document.all,J={all:X,IS_HTMLDDA:void 0===X&&void 0!==X},Z=J.all,ee=J.IS_HTMLDDA?function(e){return"function"==typeof e||e===Z}:function(e){return"function"==typeof e},te=ee,re=J.all,ne=J.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:te(e)||e===re}:function(e){return"object"==typeof e?null!==e:te(e)},ae=f,oe=ee,ie=function(e){return oe(e)?e:void 0},ce=function(e,t){return arguments.length<2?ie(ae[e]):ae[e]&&ae[e][t]},ue=z({}.isPrototypeOf),se=ce("navigator","userAgent")||"",le=f,de=se,fe=le.process,pe=le.Deno,me=fe&&fe.versions||pe&&pe.version,ve=me&&me.v8;ve&&(O=(k=ve.split("."))[0]>0&&k[0]<4?1:+(k[0]+k[1])),!O&&de&&(!(k=de.match(/Edge\/(\d+)/))||k[1]>=74)&&(k=de.match(/Chrome\/(\d+)/))&&(O=+k[1]);var he=O,ge=he,ye=m,be=!!Object.getOwnPropertySymbols&&!ye((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&ge&&ge<41})),we=be&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,xe=ce,Se=ee,Ee=ue,ke=Object,Oe=we?function(e){return"symbol"==typeof e}:function(e){var t=xe("Symbol");return Se(t)&&Ee(t.prototype,ke(e))},Ce=String,Te=function(e){try{return Ce(e)}catch(e){return"Object"}},je=ee,Pe=Te,Ie=TypeError,Ne=function(e){if(je(e))return e;throw Ie(Pe(e)+" is not a function")},Re=Ne,Me=U,Ae=function(e,t){var r=e[t];return Me(r)?void 0:Re(r)},_e=b,De=ee,Le=ne,ze=TypeError,We={exports:{}},Fe=f,Ve=Object.defineProperty,Be=function(e,t){try{Ve(Fe,e,{value:t,configurable:!0,writable:!0})}catch(r){Fe[e]=t}return t},He=Be,Ue="__core-js_shared__",Ge=f[Ue]||He(Ue,{}),Qe=Ge;(We.exports=function(e,t){return Qe[e]||(Qe[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.25.5",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.5/LICENSE",source:"https://github.com/zloirock/core-js"});var qe=q,Ye=Object,$e=function(e){return Ye(qe(e))},Ke=$e,Xe=z({}.hasOwnProperty),Je=Object.hasOwn||function(e,t){return Xe(Ke(e),t)},Ze=z,et=0,tt=Math.random(),rt=Ze(1..toString),nt=function(e){return"Symbol("+(void 0===e?"":e)+")_"+rt(++et+tt,36)},at=f,ot=We.exports,it=Je,ct=nt,ut=be,st=we,lt=ot("wks"),dt=at.Symbol,ft=dt&&dt.for,pt=st?dt:dt&&dt.withoutSetter||ct,mt=function(e){if(!it(lt,e)||!ut&&"string"!=typeof lt[e]){var t="Symbol."+e;ut&&it(dt,e)?lt[e]=dt[e]:lt[e]=st&&ft?ft(t):pt(t)}return lt[e]},vt=b,ht=ne,gt=Oe,yt=Ae,bt=TypeError,wt=mt("toPrimitive"),xt=function(e,t){if(!ht(e)||gt(e))return e;var r,n=yt(e,wt);if(n){if(void 0===t&&(t="default"),r=vt(n,e,t),!ht(r)||gt(r))return r;throw bt("Can't convert object to primitive value")}return void 0===t&&(t="number"),function(e,t){var r,n;if("string"===t&&De(r=e.toString)&&!Le(n=_e(r,e)))return n;if(De(r=e.valueOf)&&!Le(n=_e(r,e)))return n;if("string"!==t&&De(r=e.toString)&&!Le(n=_e(r,e)))return n;throw ze("Can't convert object to primitive value")}(e,t)},St=Oe,Et=function(e){var t=xt(e,"string");return St(t)?t:t+""},kt=ne,Ot=f.document,Ct=kt(Ot)&&kt(Ot.createElement),Tt=function(e){return Ct?Ot.createElement(e):{}},jt=Tt,Pt=!v&&!m((function(){return 7!=Object.defineProperty(jt("div"),"a",{get:function(){return 7}}).a})),It=v,Nt=b,Rt=w,Mt=C,At=K,_t=Et,Dt=Je,Lt=Pt,zt=Object.getOwnPropertyDescriptor;p.f=It?zt:function(e,t){if(e=At(e),t=_t(t),Lt)try{return zt(e,t)}catch(e){}if(Dt(e,t))return Mt(!Nt(Rt.f,e,t),e[t])};var Wt={},Ft=v&&m((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Vt=ne,Bt=String,Ht=TypeError,Ut=function(e){if(Vt(e))return e;throw Ht(Bt(e)+" is not an object")},Gt=v,Qt=Pt,qt=Ft,Yt=Ut,$t=Et,Kt=TypeError,Xt=Object.defineProperty,Jt=Object.getOwnPropertyDescriptor;Wt.f=Gt?qt?function(e,t,r){if(Yt(e),t=$t(t),Yt(r),"function"==typeof e&&"prototype"===t&&"value"in r&&"writable"in r&&!r.writable){var n=Jt(e,t);n&&n.writable&&(e[t]=r.value,r={configurable:"configurable"in r?r.configurable:n.configurable,enumerable:"enumerable"in r?r.enumerable:n.enumerable,writable:!1})}return Xt(e,t,r)}:Xt:function(e,t,r){if(Yt(e),t=$t(t),Yt(r),Qt)try{return Xt(e,t,r)}catch(e){}if("get"in r||"set"in r)throw Kt("Accessors not supported");return"value"in r&&(e[t]=r.value),e};var Zt=Wt,er=C,tr=v?function(e,t,r){return Zt.f(e,t,er(1,r))}:function(e,t,r){return e[t]=r,e},rr={exports:{}},nr=v,ar=Je,or=Function.prototype,ir=nr&&Object.getOwnPropertyDescriptor,cr=ar(or,"name"),ur={EXISTS:cr,PROPER:cr&&"something"===function(){}.name,CONFIGURABLE:cr&&(!nr||nr&&ir(or,"name").configurable)},sr=ee,lr=Ge,dr=z(Function.toString);sr(lr.inspectSource)||(lr.inspectSource=function(e){return dr(e)});var fr,pr,mr,vr=lr.inspectSource,hr=ee,gr=f.WeakMap,yr=hr(gr)&&/native code/.test(String(gr)),br=We.exports,wr=nt,xr=br("keys"),Sr=function(e){return xr[e]||(xr[e]=wr(e))},Er={},kr=yr,Or=f,Cr=ne,Tr=tr,jr=Je,Pr=Ge,Ir=Sr,Nr=Er,Rr="Object already initialized",Mr=Or.TypeError,Ar=Or.WeakMap;if(kr||Pr.state){var _r=Pr.state||(Pr.state=new Ar);_r.get=_r.get,_r.has=_r.has,_r.set=_r.set,fr=function(e,t){if(_r.has(e))throw Mr(Rr);return t.facade=e,_r.set(e,t),t},pr=function(e){return _r.get(e)||{}},mr=function(e){return _r.has(e)}}else{var Dr=Ir("state");Nr[Dr]=!0,fr=function(e,t){if(jr(e,Dr))throw Mr(Rr);return t.facade=e,Tr(e,Dr,t),t},pr=function(e){return jr(e,Dr)?e[Dr]:{}},mr=function(e){return jr(e,Dr)}}var Lr={set:fr,get:pr,has:mr,enforce:function(e){return mr(e)?pr(e):fr(e,{})},getterFor:function(e){return function(t){var r;if(!Cr(t)||(r=pr(t)).type!==e)throw Mr("Incompatible receiver, "+e+" required");return r}}},zr=m,Wr=ee,Fr=Je,Vr=v,Br=ur.CONFIGURABLE,Hr=vr,Ur=Lr.enforce,Gr=Lr.get,Qr=Object.defineProperty,qr=Vr&&!zr((function(){return 8!==Qr((function(){}),"length",{value:8}).length})),Yr=String(String).split("String"),$r=rr.exports=function(e,t,r){"Symbol("===String(t).slice(0,7)&&(t="["+String(t).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!Fr(e,"name")||Br&&e.name!==t)&&(Vr?Qr(e,"name",{value:t,configurable:!0}):e.name=t),qr&&r&&Fr(r,"arity")&&e.length!==r.arity&&Qr(e,"length",{value:r.arity});try{r&&Fr(r,"constructor")&&r.constructor?Vr&&Qr(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=Ur(e);return Fr(n,"source")||(n.source=Yr.join("string"==typeof t?t:"")),e};Function.prototype.toString=$r((function(){return Wr(this)&&Gr(this).source||Hr(this)}),"toString");var Kr=ee,Xr=Wt,Jr=rr.exports,Zr=Be,en=function(e,t,r,n){n||(n={});var a=n.enumerable,o=void 0!==n.name?n.name:t;if(Kr(r)&&Jr(r,o,n),n.global)a?e[t]=r:Zr(t,r);else{try{n.unsafe?e[t]&&(a=!0):delete e[t]}catch(e){}a?e[t]=r:Xr.f(e,t,{value:r,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return e},tn={},rn=Math.ceil,nn=Math.floor,an=Math.trunc||function(e){var t=+e;return(t>0?nn:rn)(t)},on=function(e){var t=+e;return t!=t||0===t?0:an(t)},cn=on,un=Math.max,sn=Math.min,ln=function(e,t){var r=cn(e);return r<0?un(r+t,0):sn(r,t)},dn=on,fn=Math.min,pn=function(e){return e>0?fn(dn(e),9007199254740991):0},mn=pn,vn=function(e){return mn(e.length)},hn=K,gn=ln,yn=vn,bn=function(e){return function(t,r,n){var a,o=hn(t),i=yn(o),c=gn(n,i);if(e&&r!=r){for(;i>c;)if((a=o[c++])!=a)return!0}else for(;i>c;c++)if((e||c in o)&&o[c]===r)return e||c||0;return!e&&-1}},wn={includes:bn(!0),indexOf:bn(!1)},xn=Je,Sn=K,En=wn.indexOf,kn=Er,On=z([].push),Cn=function(e,t){var r,n=Sn(e),a=0,o=[];for(r in n)!xn(kn,r)&&xn(n,r)&&On(o,r);for(;t.length>a;)xn(n,r=t[a++])&&(~En(o,r)||On(o,r));return o},Tn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],jn=Cn,Pn=Tn.concat("length","prototype");tn.f=Object.getOwnPropertyNames||function(e){return jn(e,Pn)};var In={};In.f=Object.getOwnPropertySymbols;var Nn=ce,Rn=tn,Mn=In,An=Ut,_n=z([].concat),Dn=Nn("Reflect","ownKeys")||function(e){var t=Rn.f(An(e)),r=Mn.f;return r?_n(t,r(e)):t},Ln=Je,zn=Dn,Wn=p,Fn=Wt,Vn=m,Bn=ee,Hn=/#|\.prototype\./,Un=function(e,t){var r=Qn[Gn(e)];return r==Yn||r!=qn&&(Bn(t)?Vn(t):!!t)},Gn=Un.normalize=function(e){return String(e).replace(Hn,".").toLowerCase()},Qn=Un.data={},qn=Un.NATIVE="N",Yn=Un.POLYFILL="P",$n=Un,Kn=f,Xn=p.f,Jn=tr,Zn=en,ea=Be,ta=function(e,t,r){for(var n=zn(t),a=Fn.f,o=Wn.f,i=0;i<n.length;i++){var c=n[i];Ln(e,c)||r&&Ln(r,c)||a(e,c,o(t,c))}},ra=$n,na=function(e,t){var r,n,a,o,i,c=e.target,u=e.global,s=e.stat;if(r=u?Kn:s?Kn[c]||ea(c,{}):(Kn[c]||{}).prototype)for(n in t){if(o=t[n],a=e.dontCallGetSet?(i=Xn(r,n))&&i.value:r[n],!ra(u?n:c+(s?".":"#")+n,e.forced)&&void 0!==a){if(typeof o==typeof a)continue;ta(o,a)}(e.sham||a&&a.sham)&&Jn(o,"sham",!0),Zn(r,n,o,e)}},aa=_,oa=Array.isArray||function(e){return"Array"==aa(e)},ia=TypeError,ca=Et,ua=Wt,sa=C,la=function(e,t,r){var n=ca(t);n in e?ua.f(e,n,sa(0,r)):e[n]=r},da={};da[mt("toStringTag")]="z";var fa="[object z]"===String(da),pa=fa,ma=ee,va=_,ha=mt("toStringTag"),ga=Object,ya="Arguments"==va(function(){return arguments}()),ba=pa?va:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=ga(e),ha))?r:ya?va(t):"Object"==(n=va(t))&&ma(t.callee)?"Arguments":n},wa=z,xa=m,Sa=ee,Ea=ba,ka=vr,Oa=function(){},Ca=[],Ta=ce("Reflect","construct"),ja=/^\s*(?:class|function)\b/,Pa=wa(ja.exec),Ia=!ja.exec(Oa),Na=function(e){if(!Sa(e))return!1;try{return Ta(Oa,Ca,e),!0}catch(e){return!1}},Ra=function(e){if(!Sa(e))return!1;switch(Ea(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Ia||!!Pa(ja,ka(e))}catch(e){return!0}};Ra.sham=!0;var Ma=!Ta||xa((function(){var e;return Na(Na.call)||!Na(Object)||!Na((function(){e=!0}))||e}))?Ra:Na,Aa=oa,_a=Ma,Da=ne,La=mt("species"),za=Array,Wa=function(e,t){return new(function(e){var t;return Aa(e)&&(t=e.constructor,(_a(t)&&(t===za||Aa(t.prototype))||Da(t)&&null===(t=t[La]))&&(t=void 0)),void 0===t?za:t}(e))(0===t?0:t)},Fa=m,Va=he,Ba=mt("species"),Ha=function(e){return Va>=51||!Fa((function(){var t=[];return(t.constructor={})[Ba]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},Ua=na,Ga=m,Qa=oa,qa=ne,Ya=$e,$a=vn,Ka=function(e){if(e>9007199254740991)throw ia("Maximum allowed index exceeded");return e},Xa=la,Ja=Wa,Za=Ha,eo=he,to=mt("isConcatSpreadable"),ro=eo>=51||!Ga((function(){var e=[];return e[to]=!1,e.concat()[0]!==e})),no=Za("concat"),ao=function(e){if(!qa(e))return!1;var t=e[to];return void 0!==t?!!t:Qa(e)};Ua({target:"Array",proto:!0,arity:1,forced:!ro||!no},{concat:function(e){var t,r,n,a,o,i=Ya(this),c=Ja(i,0),u=0;for(t=-1,n=arguments.length;t<n;t++)if(ao(o=-1===t?i:arguments[t]))for(a=$a(o),Ka(u+a),r=0;r<a;r++,u++)r in o&&Xa(c,u,o[r]);else Ka(u+1),Xa(c,u++,o);return c.length=u,c}});var oo={},io=Cn,co=Tn,uo=Object.keys||function(e){return io(e,co)},so=v,lo=Ft,fo=Wt,po=Ut,mo=K,vo=uo;oo.f=so&&!lo?Object.defineProperties:function(e,t){po(e);for(var r,n=mo(t),a=vo(t),o=a.length,i=0;o>i;)fo.f(e,r=a[i++],n[r]);return e};var ho,go=ce("document","documentElement"),yo=Ut,bo=oo,wo=Tn,xo=Er,So=go,Eo=Tt,ko=Sr("IE_PROTO"),Oo=function(){},Co=function(e){return"<script>"+e+"<\/script>"},To=function(e){e.write(Co("")),e.close();var t=e.parentWindow.Object;return e=null,t},jo=function(){try{ho=new ActiveXObject("htmlfile")}catch(e){}var e,t;jo="undefined"!=typeof document?document.domain&&ho?To(ho):((t=Eo("iframe")).style.display="none",So.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(Co("document.F=Object")),e.close(),e.F):To(ho);for(var r=wo.length;r--;)delete jo.prototype[wo[r]];return jo()};xo[ko]=!0;var Po=Object.create||function(e,t){var r;return null!==e?(Oo.prototype=yo(e),r=new Oo,Oo.prototype=null,r[ko]=e):r=jo(),void 0===t?r:bo.f(r,t)},Io=mt,No=Po,Ro=Wt.f,Mo=Io("unscopables"),Ao=Array.prototype;null==Ao[Mo]&&Ro(Ao,Mo,{configurable:!0,value:No(null)});var _o,Do,Lo,zo=function(e){Ao[Mo][e]=!0},Wo={},Fo=!m((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),Vo=Je,Bo=ee,Ho=$e,Uo=Fo,Go=Sr("IE_PROTO"),Qo=Object,qo=Qo.prototype,Yo=Uo?Qo.getPrototypeOf:function(e){var t=Ho(e);if(Vo(t,Go))return t[Go];var r=t.constructor;return Bo(r)&&t instanceof r?r.prototype:t instanceof Qo?qo:null},$o=m,Ko=ee,Xo=ne,Jo=Yo,Zo=en,ei=mt("iterator"),ti=!1;[].keys&&("next"in(Lo=[].keys())?(Do=Jo(Jo(Lo)))!==Object.prototype&&(_o=Do):ti=!0),(!Xo(_o)||$o((function(){var e={};return _o[ei].call(e)!==e})))&&(_o={}),Ko(_o[ei])||Zo(_o,ei,(function(){return this}));var ri={IteratorPrototype:_o,BUGGY_SAFARI_ITERATORS:ti},ni=Wt.f,ai=Je,oi=mt("toStringTag"),ii=function(e,t,r){e&&!r&&(e=e.prototype),e&&!ai(e,oi)&&ni(e,oi,{configurable:!0,value:t})},ci=ri.IteratorPrototype,ui=Po,si=C,li=ii,di=Wo,fi=function(){return this},pi=ee,mi=String,vi=TypeError,hi=z,gi=Ut,yi=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=hi(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(r,[]),t=r instanceof Array}catch(e){}return function(r,n){return gi(r),function(e){if("object"==typeof e||pi(e))return e;throw vi("Can't set "+mi(e)+" as a prototype")}(n),t?e(r,n):r.__proto__=n,r}}():void 0),bi=na,wi=b,xi=ee,Si=Yo,Ei=yi,ki=ii,Oi=tr,Ci=en,Ti=Wo,ji=ur.PROPER,Pi=ur.CONFIGURABLE,Ii=ri.IteratorPrototype,Ni=ri.BUGGY_SAFARI_ITERATORS,Ri=mt("iterator"),Mi="values",Ai="entries",_i=function(){return this},Di=function(e,t,r,n,a,o,i){!function(e,t,r,n){var a=t+" Iterator";e.prototype=ui(ci,{next:si(+!n,r)}),li(e,a,!1),di[a]=fi}(r,t,n);var c,u,s,l=function(e){if(e===a&&v)return v;if(!Ni&&e in p)return p[e];switch(e){case"keys":case Mi:case Ai:return function(){return new r(this,e)}}return function(){return new r(this)}},d=t+" Iterator",f=!1,p=e.prototype,m=p[Ri]||p["@@iterator"]||a&&p[a],v=!Ni&&m||l(a),h="Array"==t&&p.entries||m;if(h&&(c=Si(h.call(new e)))!==Object.prototype&&c.next&&(Si(c)!==Ii&&(Ei?Ei(c,Ii):xi(c[Ri])||Ci(c,Ri,_i)),ki(c,d,!0)),ji&&a==Mi&&m&&m.name!==Mi&&(Pi?Oi(p,"name",Mi):(f=!0,v=function(){return wi(m,this)})),a)if(u={values:l(Mi),keys:o?v:l("keys"),entries:l(Ai)},i)for(s in u)(Ni||f||!(s in p))&&Ci(p,s,u[s]);else bi({target:t,proto:!0,forced:Ni||f},u);return p[Ri]!==v&&Ci(p,Ri,v,{name:a}),Ti[t]=v,u},Li=function(e,t){return{value:e,done:t}},zi=K,Wi=zo,Fi=Wo,Vi=Lr,Bi=Wt.f,Hi=Di,Ui=Li,Gi=v,Qi="Array Iterator",qi=Vi.set,Yi=Vi.getterFor(Qi),$i=Hi(Array,"Array",(function(e,t){qi(this,{type:Qi,target:zi(e),index:0,kind:t})}),(function(){var e=Yi(this),t=e.target,r=e.kind,n=e.index++;return!t||n>=t.length?(e.target=void 0,Ui(void 0,!0)):Ui("keys"==r?n:"values"==r?t[n]:[n,t[n]],!1)}),"values"),Ki=Fi.Arguments=Fi.Array;if(Wi("keys"),Wi("values"),Wi("entries"),Gi&&"values"!==Ki.name)try{Bi(Ki,"name",{value:"values"})}catch(e){}var Xi={exports:{}},Ji={},Zi=ln,ec=vn,tc=la,rc=Array,nc=Math.max,ac=_,oc=K,ic=tn.f,cc="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];Ji.f=function(e){return cc&&"Window"==ac(e)?function(e){try{return ic(e)}catch(e){return function(e,t,r){for(var n=ec(e),a=Zi(t,n),o=Zi(void 0===r?n:r,n),i=rc(nc(o-a,0)),c=0;a<o;a++,c++)tc(i,c,e[a]);return i.length=c,i}(cc)}}(e):ic(oc(e))};var uc=m((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}})),sc=m,lc=ne,dc=_,fc=uc,pc=Object.isExtensible,mc=sc((function(){pc(1)}))||fc?function(e){return!!lc(e)&&(!fc||"ArrayBuffer"!=dc(e))&&(!pc||pc(e))}:pc,vc=!m((function(){return Object.isExtensible(Object.preventExtensions({}))})),hc=na,gc=z,yc=Er,bc=ne,wc=Je,xc=Wt.f,Sc=tn,Ec=Ji,kc=mc,Oc=vc,Cc=!1,Tc=nt("meta"),jc=0,Pc=function(e){xc(e,Tc,{value:{objectID:"O"+jc++,weakData:{}}})},Ic=Xi.exports={enable:function(){Ic.enable=function(){},Cc=!0;var e=Sc.f,t=gc([].splice),r={};r[Tc]=1,e(r).length&&(Sc.f=function(r){for(var n=e(r),a=0,o=n.length;a<o;a++)if(n[a]===Tc){t(n,a,1);break}return n},hc({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Ec.f}))},fastKey:function(e,t){if(!bc(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!wc(e,Tc)){if(!kc(e))return"F";if(!t)return"E";Pc(e)}return e[Tc].objectID},getWeakData:function(e,t){if(!wc(e,Tc)){if(!kc(e))return!0;if(!t)return!1;Pc(e)}return e[Tc].weakData},onFreeze:function(e){return Oc&&Cc&&kc(e)&&!wc(e,Tc)&&Pc(e),e}};yc[Tc]=!0;var Nc=Ne,Rc=h,Mc=z(z.bind),Ac=function(e,t){return Nc(e),void 0===t?e:Rc?Mc(e,t):function(){return e.apply(t,arguments)}},_c=Wo,Dc=mt("iterator"),Lc=Array.prototype,zc=ba,Wc=Ae,Fc=U,Vc=Wo,Bc=mt("iterator"),Hc=function(e){if(!Fc(e))return Wc(e,Bc)||Wc(e,"@@iterator")||Vc[zc(e)]},Uc=b,Gc=Ne,Qc=Ut,qc=Te,Yc=Hc,$c=TypeError,Kc=b,Xc=Ut,Jc=Ae,Zc=Ac,eu=b,tu=Ut,ru=Te,nu=vn,au=ue,ou=Hc,iu=function(e,t,r){var n,a;Xc(e);try{if(!(n=Jc(e,"return"))){if("throw"===t)throw r;return r}n=Kc(n,e)}catch(e){a=!0,n=e}if("throw"===t)throw r;if(a)throw n;return Xc(n),r},cu=TypeError,uu=function(e,t){this.stopped=e,this.result=t},su=uu.prototype,lu=function(e,t,r){var n,a,o,i,c,u,s,l=r&&r.that,d=!(!r||!r.AS_ENTRIES),f=!(!r||!r.IS_RECORD),p=!(!r||!r.IS_ITERATOR),m=!(!r||!r.INTERRUPTED),v=Zc(t,l),h=function(e){return n&&iu(n,"normal",e),new uu(!0,e)},g=function(e){return d?(tu(e),m?v(e[0],e[1],h):v(e[0],e[1])):m?v(e,h):v(e)};if(f)n=e.iterator;else if(p)n=e;else{if(!(a=ou(e)))throw cu(ru(e)+" is not iterable");if(function(e){return void 0!==e&&(_c.Array===e||Lc[Dc]===e)}(a)){for(o=0,i=nu(e);i>o;o++)if((c=g(e[o]))&&au(su,c))return c;return new uu(!1)}n=function(e,t){var r=arguments.length<2?Yc(e):t;if(Gc(r))return Qc(Uc(r,e));throw $c(qc(e)+" is not iterable")}(e,a)}for(u=f?e.next:n.next;!(s=eu(u,n)).done;){try{c=g(s.value)}catch(e){iu(n,"throw",e)}if("object"==typeof c&&c&&au(su,c))return c}return new uu(!1)},du=ue,fu=TypeError,pu=function(e,t){if(du(t,e))return e;throw fu("Incorrect invocation")},mu=mt("iterator"),vu=!1;try{var hu=0,gu={next:function(){return{done:!!hu++}},return:function(){vu=!0}};gu[mu]=function(){return this},Array.from(gu,(function(){throw 2}))}catch(e){}var yu=function(e,t){if(!t&&!vu)return!1;var r=!1;try{var n={};n[mu]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch(e){}return r},bu=ee,wu=ne,xu=yi,Su=na,Eu=f,ku=z,Ou=$n,Cu=en,Tu=Xi.exports,ju=lu,Pu=pu,Iu=ee,Nu=U,Ru=ne,Mu=m,Au=yu,_u=ii,Du=en,Lu=ce,zu=Wt,Wu=v,Fu=mt("species"),Vu=function(e){var t=Lu(e),r=zu.f;Wu&&t&&!t[Fu]&&r(t,Fu,{configurable:!0,get:function(){return this}})},Bu=Wt.f,Hu=Po,Uu=function(e,t,r){for(var n in t)Du(e,n,t[n],r);return e},Gu=Ac,Qu=pu,qu=U,Yu=lu,$u=Di,Ku=Li,Xu=Vu,Ju=v,Zu=Xi.exports.fastKey,es=Lr.set,ts=Lr.getterFor;!function(e,t,r){var n=-1!==e.indexOf("Map"),a=-1!==e.indexOf("Weak"),o=n?"set":"add",i=Eu.Map,c=i&&i.prototype,u=i,s={},l=function(e){var t=ku(c[e]);Cu(c,e,"add"==e?function(e){return t(this,0===e?0:e),this}:"delete"==e?function(e){return!(a&&!Ru(e))&&t(this,0===e?0:e)}:"get"==e?function(e){return a&&!Ru(e)?void 0:t(this,0===e?0:e)}:"has"==e?function(e){return!(a&&!Ru(e))&&t(this,0===e?0:e)}:function(e,r){return t(this,0===e?0:e,r),this})};if(Ou(e,!Iu(i)||!(a||c.forEach&&!Mu((function(){(new i).entries().next()})))))u=r.getConstructor(t,e,n,o),Tu.enable();else if(Ou(e,!0)){var d=new u,f=d[o](a?{}:-0,1)!=d,p=Mu((function(){d.has(1)})),m=Au((function(e){new i(e)})),v=!a&&Mu((function(){for(var e=new i,t=5;t--;)e[o](t,t);return!e.has(-0)}));m||((u=t((function(e,t){Pu(e,c);var r=function(e,t,r){var n,a;return xu&&bu(n=t.constructor)&&n!==r&&wu(a=n.prototype)&&a!==r.prototype&&xu(e,a),e}(new i,e,u);return Nu(t)||ju(t,r[o],{that:r,AS_ENTRIES:n}),r}))).prototype=c,c.constructor=u),(p||v)&&(l("delete"),l("has"),n&&l("get")),(v||f)&&l(o),a&&c.clear&&delete c.clear}s.Map=u,Su({global:!0,constructor:!0,forced:u!=i},s),_u(u,e),a||r.setStrong(u,e,n)}("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),{getConstructor:function(e,t,r,n){var a=e((function(e,a){Qu(e,o),es(e,{type:t,index:Hu(null),first:void 0,last:void 0,size:0}),Ju||(e.size=0),qu(a)||Yu(a,e[n],{that:e,AS_ENTRIES:r})})),o=a.prototype,i=ts(t),c=function(e,t,r){var n,a,o=i(e),c=u(e,t);return c?c.value=r:(o.last=c={index:a=Zu(t,!0),key:t,value:r,previous:n=o.last,next:void 0,removed:!1},o.first||(o.first=c),n&&(n.next=c),Ju?o.size++:e.size++,"F"!==a&&(o.index[a]=c)),e},u=function(e,t){var r,n=i(e),a=Zu(t);if("F"!==a)return n.index[a];for(r=n.first;r;r=r.next)if(r.key==t)return r};return Uu(o,{clear:function(){for(var e=i(this),t=e.index,r=e.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete t[r.index],r=r.next;e.first=e.last=void 0,Ju?e.size=0:this.size=0},delete:function(e){var t=this,r=i(t),n=u(t,e);if(n){var a=n.next,o=n.previous;delete r.index[n.index],n.removed=!0,o&&(o.next=a),a&&(a.previous=o),r.first==n&&(r.first=a),r.last==n&&(r.last=o),Ju?r.size--:t.size--}return!!n},forEach:function(e){for(var t,r=i(this),n=Gu(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:r.first;)for(n(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!u(this,e)}}),Uu(o,r?{get:function(e){var t=u(this,e);return t&&t.value},set:function(e,t){return c(this,0===e?0:e,t)}}:{add:function(e){return c(this,e=0===e?0:e,e)}}),Ju&&Bu(o,"size",{get:function(){return i(this).size}}),a},setStrong:function(e,t,r){var n=t+" Iterator",a=ts(t),o=ts(n);$u(e,t,(function(e,t){es(this,{type:n,target:e,state:a(e),kind:t,last:void 0})}),(function(){for(var e=o(this),t=e.kind,r=e.last;r&&r.removed;)r=r.previous;return e.target&&(e.last=r=r?r.next:e.state.first)?Ku("keys"==t?r.key:"values"==t?r.value:[r.key,r.value],!1):(e.target=void 0,Ku(void 0,!0))}),r?"entries":"values",!r,!0),Xu(t)}});var rs=ba,ns=fa?{}.toString:function(){return"[object "+rs(this)+"]"};fa||en(Object.prototype,"toString",ns,{unsafe:!0});var as=ba,os=String,is=function(e){if("Symbol"===as(e))throw TypeError("Cannot convert a Symbol value to a string");return os(e)},cs=z,us=on,ss=is,ls=q,ds=cs("".charAt),fs=cs("".charCodeAt),ps=cs("".slice),ms=function(e){return function(t,r){var n,a,o=ss(ls(t)),i=us(r),c=o.length;return i<0||i>=c?e?"":void 0:(n=fs(o,i))<55296||n>56319||i+1===c||(a=fs(o,i+1))<56320||a>57343?e?ds(o,i):n:e?ps(o,i,i+2):a-56320+(n-55296<<10)+65536}},vs={codeAt:ms(!1),charAt:ms(!0)},hs=vs.charAt,gs=is,ys=Lr,bs=Di,ws=Li,xs="String Iterator",Ss=ys.set,Es=ys.getterFor(xs);bs(String,"String",(function(e){Ss(this,{type:xs,string:gs(e),index:0})}),(function(){var e,t=Es(this),r=t.string,n=t.index;return n>=r.length?ws(void 0,!0):(e=hs(r,n),t.index+=e.length,ws(e,!1))}));var ks={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Os=Tt("span").classList,Cs=Os&&Os.constructor&&Os.constructor.prototype,Ts=Cs===Object.prototype?void 0:Cs,js=f,Ps=ks,Is=Ts,Ns=$i,Rs=tr,Ms=mt,As=Ms("iterator"),_s=Ms("toStringTag"),Ds=Ns.values,Ls=function(e,t){if(e){if(e[As]!==Ds)try{Rs(e,As,Ds)}catch(t){e[As]=Ds}if(e[_s]||Rs(e,_s,t),Ps[t])for(var r in Ns)if(e[r]!==Ns[r])try{Rs(e,r,Ns[r])}catch(t){e[r]=Ns[r]}}};for(var zs in Ps)Ls(js[zs]&&js[zs].prototype,zs);Ls(Is,"DOMTokenList");var Ws,Fs,Vs=Ut,Bs=m,Hs=f.RegExp,Us=Bs((function(){var e=Hs("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),Gs=Us||Bs((function(){return!Hs("a","y").sticky})),Qs={BROKEN_CARET:Us||Bs((function(){var e=Hs("^r","gy");return e.lastIndex=2,null!=e.exec("str")})),MISSED_STICKY:Gs,UNSUPPORTED_Y:Us},qs=m,Ys=f.RegExp,$s=qs((function(){var e=Ys(".","s");return!(e.dotAll&&e.exec("\n")&&"s"===e.flags)})),Ks=m,Xs=f.RegExp,Js=Ks((function(){var e=Xs("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")})),Zs=b,el=z,tl=is,rl=function(){var e=Vs(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t},nl=Qs,al=We.exports,ol=Po,il=Lr.get,cl=$s,ul=Js,sl=al("native-string-replace",String.prototype.replace),ll=RegExp.prototype.exec,dl=ll,fl=el("".charAt),pl=el("".indexOf),ml=el("".replace),vl=el("".slice),hl=(Fs=/b*/g,Zs(ll,Ws=/a/,"a"),Zs(ll,Fs,"a"),0!==Ws.lastIndex||0!==Fs.lastIndex),gl=nl.BROKEN_CARET,yl=void 0!==/()??/.exec("")[1];(hl||yl||gl||cl||ul)&&(dl=function(e){var t,r,n,a,o,i,c,u=this,s=il(u),l=tl(e),d=s.raw;if(d)return d.lastIndex=u.lastIndex,t=Zs(dl,d,l),u.lastIndex=d.lastIndex,t;var f=s.groups,p=gl&&u.sticky,m=Zs(rl,u),v=u.source,h=0,g=l;if(p&&(m=ml(m,"y",""),-1===pl(m,"g")&&(m+="g"),g=vl(l,u.lastIndex),u.lastIndex>0&&(!u.multiline||u.multiline&&"\n"!==fl(l,u.lastIndex-1))&&(v="(?: "+v+")",g=" "+g,h++),r=new RegExp("^(?:"+v+")",m)),yl&&(r=new RegExp("^"+v+"$(?!\\s)",m)),hl&&(n=u.lastIndex),a=Zs(ll,p?r:u,g),p?a?(a.input=vl(a.input,h),a[0]=vl(a[0],h),a.index=u.lastIndex,u.lastIndex+=a[0].length):u.lastIndex=0:hl&&a&&(u.lastIndex=u.global?a.index+a[0].length:n),yl&&a&&a.length>1&&Zs(sl,a[0],r,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(a[o]=void 0)})),a&&f)for(a.groups=i=ol(null),o=0;o<f.length;o++)i[(c=f[o])[0]]=a[c[1]];return a});var bl=dl;na({target:"RegExp",proto:!0,forced:/./.exec!==bl},{exec:bl});var wl=q,xl=is,Sl=z("".replace),El="[\t\n\v\f\r                　\u2028\u2029\ufeff]",kl=RegExp("^"+El+El+"*"),Ol=RegExp(El+El+"*$"),Cl=function(e){return function(t){var r=xl(wl(t));return 1&e&&(r=Sl(r,kl,"")),2&e&&(r=Sl(r,Ol,"")),r}},Tl={start:Cl(1),end:Cl(2),trim:Cl(3)},jl=f,Pl=m,Il=is,Nl=Tl.trim,Rl=z("".charAt),Ml=jl.parseFloat,Al=jl.Symbol,_l=Al&&Al.iterator,Dl=1/Ml("\t\n\v\f\r                　\u2028\u2029\ufeff-0")!=-1/0||_l&&!Pl((function(){Ml(Object(_l))}))?function(e){var t=Nl(Il(e)),r=Ml(t);return 0===r&&"-"==Rl(t,0)?-0:r}:Ml;na({global:!0,forced:parseFloat!=Dl},{parseFloat:Dl});var Ll=z,zl=en,Wl=bl,Fl=m,Vl=mt,Bl=tr,Hl=Vl("species"),Ul=RegExp.prototype,Gl=vs.charAt,Ql=b,ql=Ut,Yl=ee,$l=_,Kl=bl,Xl=TypeError,Jl=b,Zl=Ut,ed=U,td=pn,rd=is,nd=q,ad=Ae,od=function(e,t,r){return t+(r?Gl(e,t).length:1)},id=function(e,t){var r=e.exec;if(Yl(r)){var n=Ql(r,e,t);return null!==n&&ql(n),n}if("RegExp"===$l(e))return Ql(Kl,e,t);throw Xl("RegExp#exec called on incompatible receiver")};!function(e,t,r,n){var a=Vl(e),o=!Fl((function(){var t={};return t[a]=function(){return 7},7!=""[e](t)})),i=o&&!Fl((function(){var t=!1,r=/a/;return"split"===e&&((r={}).constructor={},r.constructor[Hl]=function(){return r},r.flags="",r[a]=/./[a]),r.exec=function(){return t=!0,null},r[a](""),!t}));if(!o||!i||r){var c=Ll(/./[a]),u=t(a,""[e],(function(e,t,r,n,a){var i=Ll(e),u=t.exec;return u===Wl||u===Ul.exec?o&&!a?{done:!0,value:c(t,r,n)}:{done:!0,value:i(r,t,n)}:{done:!1}}));zl(String.prototype,e,u[0]),zl(Ul,a,u[1])}n&&Bl(Ul[a],"sham",!0)}("match",(function(e,t,r){return[function(t){var r=nd(this),n=ed(t)?void 0:ad(t,e);return n?Jl(n,t,r):new RegExp(t)[e](rd(r))},function(e){var n=Zl(this),a=rd(e),o=r(t,n,a);if(o.done)return o.value;if(!n.global)return id(n,a);var i=n.unicode;n.lastIndex=0;for(var c,u=[],s=0;null!==(c=id(n,a));){var l=rd(c[0]);u[s]=l,""===l&&(n.lastIndex=od(a,td(n.lastIndex),i)),s++}return 0===s?null:u}]}));var cd=window.navigator&&window.navigator.userAgent||"",ud=/AppleWebKit\/([\d.]+)/i.exec(cd);ud&&parseFloat(ud.pop());var sd=/iPad/i.test(cd),ld=/iPhone/i.test(cd)&&!sd,dd=/iPod/i.test(cd),fd=ld||sd||dd;fd&&function(){var e=cd.match(/OS (\d+)_/i);e&&e[1]&&e[1]}();var pd=/Android/i.test(cd);pd&&function(){var e=cd.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),r=e[2]&&parseFloat(e[2]);t&&r&&parseFloat("".concat(e[1],".").concat(e[2]))}(),pd&&/webkit/i.test(cd);var md=/Firefox/i.test(cd),vd=md&&function(){var e=cd.match(/Firefox\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),hd=/Edge\//i.test(cd),gd=hd&&function(){var e=cd.match(/Edge\/(\d+)/i);if(e&&e[1])return e[1]}(),yd=/Edg\//i.test(cd),bd=yd&&function(){var e=cd.match(/Edg\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),wd=/SogouMobileBrowser\//i.test(cd),xd=wd&&function(){var e=cd.match(/SogouMobileBrowser\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),Sd=/MetaSr\s/i.test(cd),Ed=Sd&&function(){var e=cd.match(/MetaSr(\s\d+(\.\d+)+)/);return e&&e[1]?parseFloat(e[1]):null}(),kd=/TBS\/\d+/i.test(cd),Od=kd&&function(){var e=cd.match(/TBS\/(\d+)/i);if(e&&e[1])return e[1]}(),Cd=/XWEB\/\d+/i.test(cd),Td=Cd&&function(){var e=cd.match(/XWEB\/(\d+)/i);if(e&&e[1])return e[1]}();/MSIE\s8\.0/.test(cd),/MSIE\/\d+/i.test(cd)&&function(){var e=/MSIE\s(\d+)\.\d/.exec(cd),t=e&&parseFloat(e[1]);!t&&/Trident\/7.0/i.test(cd)&&/rv:11.0/.test(cd)&&(t=11)}(),/windowswechat/i.test(cd);var jd=/(micromessenger|webbrowser)/i.test(cd),Pd=jd&&function(){var e=cd.match(/MicroMessenger\/(\d+)/i);if(e&&e[1])return e[1]}(),Id=!kd&&/MQQBrowser\/\d+/i.test(cd)&&/COVC\/\d+/i.test(cd),Nd=!kd&&/MQQBrowser\/\d+/i.test(cd)&&!/COVC\/\d+/i.test(cd),Rd=(Nd||Id)&&function(){var e=cd.match(/ MQQBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Md=!kd&&/ QQBrowser\/\d+/i.test(cd),Ad=Md&&function(){var e=cd.match(/ QQBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),_d=!kd&&/QQBrowserLite\/\d+/i.test(cd),Dd=_d&&function(){var e=cd.match(/QQBrowserLite\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Ld=!kd&&/MQBHD\/\d+/i.test(cd),zd=Ld&&function(){var e=cd.match(/MQBHD\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Wd=/Windows/i.test(cd),Fd=!fd&&/MAC OS X/i.test(cd),Vd=!pd&&/Linux/i.test(cd);/MicroMessenger/i.test(cd);var Bd=/UCBrowser/i.test(cd);/Electron/i.test(cd);var Hd=/MiuiBrowser/i.test(cd),Ud=Hd&&function(){var e=cd.match(/MiuiBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Gd=/HuaweiBrowser/i.test(cd),Qd=Gd&&function(){var e=cd.match(/HuaweiBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),qd=/SamsungBrowser/i.test(cd),Yd=qd&&function(){var e=cd.match(/SamsungBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),$d=/HeyTapBrowser/i.test(cd),Kd=$d&&function(){var e=cd.match(/HeyTapBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Xd=/VivoBrowser/i.test(cd),Jd=Xd&&function(){var e=cd.match(/VivoBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),Zd=/Chrome/i.test(cd),ef=!hd&&!Sd&&!wd&&!kd&&!Cd&&!yd&&!Md&&!Hd&&!Gd&&!qd&&!$d&&!Xd&&/Chrome/i.test(cd);ef&&function(){var e=cd.match(/Chrome\/(\d+)/);e&&e[1]&&parseFloat(e[1])}();var tf=ef&&function(){var e=cd.match(/Chrome\/([\d.]+)/);return e&&e[1]?e[1]:null}(),rf=!Zd&&!Nd&&!Id&&!_d&&!Ld&&/Safari/i.test(cd),nf=rf&&function(){var e=cd.match(/Version\/([\d.]+)/);return e&&e[1]?e[1]:null}(),af="file:"===location.protocol||"localhost"===location.hostname||/^\d+\.\d+\.\d+\.\d+$/.test(location.hostname),of=new Map([[pd,"Android"],[fd,"iOS"],[Wd,"Windows"],[Fd,"MacOS"],[Vd,"Linux"]]),cf=new Map([[md,["Firefox",vd]],[yd,["Edg",bd]],[ef,["Chrome",tf]],[rf,["Safari",nf]],[kd,["TBS",Od]],[Cd,["XWEB",Td]],[jd&&ld,["WeChat",Pd]],[Md,["QQ(Win)",Ad]],[Nd,["QQ(Mobile)",Rd]],[Id,["QQ(Mobile X5)",Rd]],[_d,["QQ(Mac)",Dd]],[Ld,["QQ(iPad)",zd]],[Hd,["MI",Ud]],[Gd,["HW",Qd]],[qd,["Samsung",Yd]],[$d,["OPPO",Kd]],[Xd,["VIVO",Jd]],[hd,["EDGE",gd]],[wd,["SogouMobile",xd]],[Sd,["Sogou",Ed]]]),uf=function(){var e="unknown",t="unknown";if(cf.get(!0)){var r=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o=[],i=!0,c=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);i=!0);}catch(e){c=!0,a=e}finally{try{i||null==r.return||r.return()}finally{if(c)throw a}}return o}}(e,t)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(cf.get(!0),2);e=r[0],t=r[1]}return{name:e,version:t}},sf=navigator.userAgent,lf=function(){var e=void 0;return screen.width&&(e={width:screen.width?screen.width*window.devicePixelRatio:"",height:screen.height?screen.height*window.devicePixelRatio:""}),e},df=Ac,ff=H,pf=$e,mf=vn,vf=Wa,hf=z([].push),gf=function(e){var t=1==e,r=2==e,n=3==e,a=4==e,o=6==e,i=7==e,c=5==e||o;return function(u,s,l,d){for(var f,p,m=pf(u),v=ff(m),h=df(s,l),g=mf(v),y=0,b=d||vf,w=t?b(u,g):r||i?b(u,0):void 0;g>y;y++)if((c||y in v)&&(p=h(f=v[y],y,m),e))if(t)w[y]=p;else if(p)switch(e){case 3:return!0;case 5:return f;case 6:return y;case 2:hf(w,f)}else switch(e){case 4:return!1;case 7:hf(w,f)}return o?-1:n||a?a:w}},yf={forEach:gf(0),map:gf(1),filter:gf(2),some:gf(3),every:gf(4),find:gf(5),findIndex:gf(6),filterReject:gf(7)},bf=yf.filter;na({target:"Array",proto:!0,forced:!Ha("filter")},{filter:function(e){return bf(this,e,arguments.length>1?arguments[1]:void 0)}});var wf=m,xf=function(e,t){var r=[][e];return!!r&&wf((function(){r.call(null,t||function(){return 1},1)}))},Sf=yf.forEach,Ef=xf("forEach")?[].forEach:function(e){return Sf(this,e,arguments.length>1?arguments[1]:void 0)};na({target:"Array",proto:!0,forced:[].forEach!=Ef},{forEach:Ef});var kf=function(){for(var e={isSupported:!1},t=["AudioContext","webkitAudioContext","mozAudioContext","msAudioContext"],r=0;r<t.length;r++)if(t[r]in window){e.isSupported=!0;break}return e.isSupported},Of=function(){var e=!1;return md&&void 0!==window.mozRTCPeerConnection?"getSenders"in window.mozRTCPeerConnection.prototype&&(e=!0):ef&&void 0!==window.webkitRTCPeerConnection&&"getSenders"in window.webkitRTCPeerConnection.prototype&&(e=!0),e},Cf=f,Tf=ks,jf=Ts,Pf=Ef,If=tr,Nf=function(e){if(e&&e.forEach!==Pf)try{If(e,"forEach",Pf)}catch(t){e.forEach=Pf}};for(var Rf in Tf)Tf[Rf]&&Nf(Cf[Rf]&&Cf[Rf].prototype);Nf(jf);var Mf=na,Af=wn.indexOf,_f=xf,Df=z([].indexOf),Lf=!!Df&&1/Df([1],1,-0)<0,zf=_f("indexOf");Mf({target:"Array",proto:!0,forced:Lf||!zf},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return Lf?Df(this,e,t)||0:Af(this,e,t)}});var Wf=yf.map;na({target:"Array",proto:!0,forced:!Ha("map")},{map:function(e){return Wf(this,e,arguments.length>1?arguments[1]:void 0)}});var Ff=$e,Vf=Yo,Bf=Fo;na({target:"Object",stat:!0,forced:m((function(){Vf(1)})),sham:!Bf},{getPrototypeOf:function(e){return Vf(Ff(e))}}),na({target:"Array",stat:!0},{isArray:oa});var Hf=Object.prototype.hasOwnProperty;function Uf(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(function(e){if(!e||"object"!==a(e)||"[object Object]"!=Object.prototype.toString.call(e))return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var r=Object.prototype.hasOwnProperty.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&Function.prototype.toString.call(r)===Function.prototype.toString.call(Object)}(e))switch(Object.prototype.toString.call(e)){case"[object File]":case"[object Map]":case"[object Set]":return 0===e.size;case"[object Object]":for(var t in e)if(Hf.call(e,t))return!1;return!0}return!1}var Gf=function(e){return void 0===e},Qf=function(){var e=i(n().mark((function e(t){var r;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!ep()){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,qf(t);case 4:if(!(r=e.sent).audio){e.next=11;break}return e.next=8,op();case 8:if(0!==e.sent.length){e.next=11;break}throw new Error({message:"no microphone detected, but you are trying to get audio stream, please check your microphone and the configuration on TRTC.createStream."});case 11:if(!r.video){e.next=17;break}return e.next=14,ap();case 14:if(0!==e.sent.length){e.next=17;break}throw new Error({message:"no camera detected, but you are trying to get video stream, please check your camera and the configuration on TRTC.createStream."});case 17:return e.next=19,navigator.mediaDevices.getUserMedia(r);case 19:return e.abrupt("return",e.sent);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();function qf(e){return Yf.apply(this,arguments)}function Yf(){return(Yf=i(n().mark((function e(t){var a,o,i,c;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a={echoCancellation:t.echoCancellation,autoGainControl:t.autoGainControl,noiseSuppression:t.noiseSuppression},t.audio){e.next=5;break}a=!1,e.next=15;break;case 5:if(Uf(t.microphoneId)){e.next=9;break}a=r({deviceId:{exact:t.microphoneId},sampleRate:t.sampleRate,channelCount:t.channelCount},a),e.next=15;break;case 9:return a=r({sampleRate:t.sampleRate,channelCount:t.channelCount},a),e.next=12,op();case 12:o=e.sent,(i=o.filter((function(e){var t=e.deviceId;return t.length>0&&"default"!==t}))).length>0&&(a.deviceId={exact:i[0].deviceId});case 15:return c=!Gf(t.facingMode)&&t.video?{facingMode:t.facingMode,width:t.width,height:t.height,frameRate:t.frameRate}:!Uf(t.cameraId)&&t.video?{deviceId:{exact:t.cameraId},width:t.width,height:t.height,frameRate:t.frameRate}:!!t.video&&(!!Gf(t.width)||{width:t.width,height:t.height,frameRate:t.frameRate}),e.abrupt("return",{audio:a,video:c});case 18:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var $f,Kf,Xf,Jf,Zf=function(){var e=i(n().mark((function e(t){var r,a,o,i,c,u,s,l,d,f,p;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=[7680,4320],e.prev=1,e.next=4,Qf({cameraId:t,video:!0,audio:!1,width:a[0],height:a[1]});case 4:if(r=e.sent,!(o=r.getTracks()[0])||!o.getCapabilities){e.next=14;break}if(s=o.getCapabilities(),l=null==s||null===(i=s.width)||void 0===i?void 0:i.max,d=null==s||null===(c=s.height)||void 0===c?void 0:c.max,f=null==s||null===(u=s.frameRate)||void 0===u?void 0:u.max,!(l&&d&&f)){e.next=14;break}return o.stop(),e.abrupt("return",{maxWidth:l,maxHeight:d,maxFrameRate:f});case 14:return p=o.getSettings(),o.stop(),e.abrupt("return",{maxWidth:p.width,maxHeight:p.height});case 19:e.prev=19,e.t0=e.catch(1),console.error(e.t0);case 22:case"end":return e.stop()}}),e,null,[[1,19]])})));return function(t){return e.apply(this,arguments)}}(),ep=function(){return!!Gf(navigator.mediaDevices)&&(console.error("navigator.mediaDevices is not supported on your browser"),!0)},tp=function(){return"http:"===location.protocol&&!af&&(console.error("you should use https"),!0)},rp=function(){if(!navigator.mediaDevices)return!1;var e=["getUserMedia","enumerateDevices"];return e.filter((function(e){return e in navigator.mediaDevices})).length===e.length},np=function(){var e=i(n().mark((function e(){var t,r,a,o,i,c,u,s,l;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!tp()&&!ep()){e.next=2;break}return e.abrupt("return",[]);case 2:return t=!1,r=!1,a=[],o=[],i=[],c={},e.next=13,navigator.mediaDevices.enumerateDevices();case 13:if(e.sent.forEach((function(e){var n=JSON.parse(JSON.stringify(e));if(!c[n.deviceId+n.label+n.kind]){if("audio"===n.kind&&(n.kind="audioinput"),"video"===n.kind&&(n.kind="videoinput"),n.deviceId||(n.deviceId=n.id),n.label?("videoinput"!==n.kind||t||(t=!0),"audioinput"!==n.kind||r||(r=!0)):"videoinput"===n.kind?n.label="Camera ".concat(i.length+1):"audioinput"===n.kind?n.label="Microphone ".concat(a.length+1):"audiooutput"===n.kind?n.label="Speaker ".concat(o.length+1):n.label="Please invoke getUserMedia once.","audioinput"===n.kind&&-1===a.indexOf(n)&&a.push(n),"audiooutput"===n.kind&&-1===o.indexOf(n)&&o.push(n),"videoinput"===n.kind){if(e.getCapabilities&&e.deviceId){var u,s,l,d=e.getCapabilities();n.resolution={maxWidth:null==d||null===(u=d.width)||void 0===u?void 0:u.max,maxHeight:null==d||null===(s=d.height)||void 0===s?void 0:s.max,maxFrameRate:null==d||null===(l=d.frameRate)||void 0===l?void 0:l.max}}-1===i.indexOf(n)&&i.push(n)}c[n.deviceId+n.label+n.kind]=n}})),!t&&i.length>1&&(t=!0),!r&&a.length>1&&(r=!0),!t){e.next=30;break}u=0;case 19:if(!(u<i.length)){e.next=30;break}if(!(s=i[u]).resolution){e.next=23;break}return e.abrupt("break",30);case 23:return e.next=25,Zf(s.deviceId);case 25:l=e.sent,s.resolution=l;case 27:u++,e.next=19;break;case 30:return e.abrupt("return",{hasCameraPermission:t,hasMicrophonePermission:r,cameras:i,speakers:o,microphones:a});case 31:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ap=function(){var e=i(n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!tp()&&!ep()){e.next=2;break}return e.abrupt("return",[]);case 2:return e.next=4,navigator.mediaDevices.enumerateDevices();case 4:return t=e.sent,e.abrupt("return",t.filter((function(e){return"videoinput"===e.kind})).map((function(e,t){var r=e.label;e.label||(r="camera_".concat(t));var n={label:r,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(n.groupId=e.groupId),n})));case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),op=function(){var e=i(n().mark((function e(){var t;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!tp()&&!ep()){e.next=2;break}return e.abrupt("return",[]);case 2:return e.next=4,navigator.mediaDevices.enumerateDevices();case 4:return t=e.sent,e.abrupt("return",t.filter((function(e){return"audioinput"===e.kind})).map((function(e,t){var r=e.label;e.label||(r="microphone_".concat(t));var n={label:r,deviceId:e.deviceId,kind:e.kind};return e.groupId&&(n.groupId=e.groupId),n})));case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ip="process"==_(f.process),cp=Ma,up=Te,sp=TypeError,lp=Ut,dp=U,fp=mt("species"),pp=h,mp=Function.prototype,vp=mp.apply,hp=mp.call,gp="object"==typeof Reflect&&Reflect.apply||(pp?hp.bind(vp):function(){return hp.apply(vp,arguments)}),yp=z([].slice),bp=TypeError,wp=function(e,t){if(e<t)throw bp("Not enough arguments");return e},xp=/(?:ipad|iphone|ipod).*applewebkit/i.test(se),Sp=f,Ep=gp,kp=Ac,Op=ee,Cp=Je,Tp=m,jp=go,Pp=yp,Ip=Tt,Np=wp,Rp=xp,Mp=ip,Ap=Sp.setImmediate,_p=Sp.clearImmediate,Dp=Sp.process,Lp=Sp.Dispatch,zp=Sp.Function,Wp=Sp.MessageChannel,Fp=Sp.String,Vp=0,Bp={};try{$f=Sp.location}catch(e){}var Hp=function(e){if(Cp(Bp,e)){var t=Bp[e];delete Bp[e],t()}},Up=function(e){return function(){Hp(e)}},Gp=function(e){Hp(e.data)},Qp=function(e){Sp.postMessage(Fp(e),$f.protocol+"//"+$f.host)};Ap&&_p||(Ap=function(e){Np(arguments.length,1);var t=Op(e)?e:zp(e),r=Pp(arguments,1);return Bp[++Vp]=function(){Ep(t,void 0,r)},Kf(Vp),Vp},_p=function(e){delete Bp[e]},Mp?Kf=function(e){Dp.nextTick(Up(e))}:Lp&&Lp.now?Kf=function(e){Lp.now(Up(e))}:Wp&&!Rp?(Jf=(Xf=new Wp).port2,Xf.port1.onmessage=Gp,Kf=kp(Jf.postMessage,Jf)):Sp.addEventListener&&Op(Sp.postMessage)&&!Sp.importScripts&&$f&&"file:"!==$f.protocol&&!Tp(Qp)?(Kf=Qp,Sp.addEventListener("message",Gp,!1)):Kf="onreadystatechange"in Ip("script")?function(e){jp.appendChild(Ip("script")).onreadystatechange=function(){jp.removeChild(this),Hp(e)}}:function(e){setTimeout(Up(e),0)});var qp,Yp,$p,Kp,Xp,Jp,Zp,em,tm={set:Ap,clear:_p},rm=f,nm=/ipad|iphone|ipod/i.test(se)&&void 0!==rm.Pebble,am=/web0s(?!.*chrome)/i.test(se),om=f,im=Ac,cm=p.f,um=tm.set,sm=xp,lm=nm,dm=am,fm=ip,pm=om.MutationObserver||om.WebKitMutationObserver,mm=om.document,vm=om.process,hm=om.Promise,gm=cm(om,"queueMicrotask"),ym=gm&&gm.value;ym||(qp=function(){var e,t;for(fm&&(e=vm.domain)&&e.exit();Yp;){t=Yp.fn,Yp=Yp.next;try{t()}catch(e){throw Yp?Kp():$p=void 0,e}}$p=void 0,e&&e.enter()},sm||fm||dm||!pm||!mm?!lm&&hm&&hm.resolve?((Zp=hm.resolve(void 0)).constructor=hm,em=im(Zp.then,Zp),Kp=function(){em(qp)}):fm?Kp=function(){vm.nextTick(qp)}:(um=im(um,om),Kp=function(){um(qp)}):(Xp=!0,Jp=mm.createTextNode(""),new pm(qp).observe(Jp,{characterData:!0}),Kp=function(){Jp.data=Xp=!Xp}));var bm=ym||function(e){var t={fn:e,next:void 0};$p&&($p.next=t),Yp||(Yp=t,Kp()),$p=t},wm=f,xm=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}},Sm=function(){this.head=null,this.tail=null};Sm.prototype={add:function(e){var t={item:e,next:null};this.head?this.tail.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return this.head=e.next,this.tail===e&&(this.tail=null),e.item}};var Em=Sm,km=f.Promise,Om="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,Cm=!Om&&!ip&&"object"==typeof window&&"object"==typeof document,Tm=f,jm=km,Pm=ee,Im=$n,Nm=vr,Rm=mt,Mm=Cm,Am=Om,_m=he;jm&&jm.prototype;var Dm=Rm("species"),Lm=!1,zm=Pm(Tm.PromiseRejectionEvent),Wm={CONSTRUCTOR:Im("Promise",(function(){var e=Nm(jm),t=e!==String(jm);if(!t&&66===_m)return!0;if(!_m||_m<51||!/native code/.test(e)){var r=new jm((function(e){e(1)})),n=function(e){e((function(){}),(function(){}))};if((r.constructor={})[Dm]=n,!(Lm=r.then((function(){}))instanceof n))return!0}return!t&&(Mm||Am)&&!zm})),REJECTION_EVENT:zm,SUBCLASSING:Lm},Fm={},Vm=Ne,Bm=TypeError,Hm=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw Bm("Bad Promise constructor");t=e,r=n})),this.resolve=Vm(t),this.reject=Vm(r)};Fm.f=function(e){return new Hm(e)};var Um,Gm,Qm,qm=na,Ym=ip,$m=f,Km=b,Xm=en,Jm=yi,Zm=ii,ev=Vu,tv=Ne,rv=ee,nv=ne,av=pu,ov=function(e,t){var r,n=lp(e).constructor;return void 0===n||dp(r=lp(n)[fp])?t:function(e){if(cp(e))return e;throw sp(up(e)+" is not a constructor")}(r)},iv=tm.set,cv=bm,uv=xm,sv=Em,lv=Lr,dv=km,fv=Fm,pv="Promise",mv=Wm.CONSTRUCTOR,vv=Wm.REJECTION_EVENT,hv=Wm.SUBCLASSING,gv=lv.getterFor(pv),yv=lv.set,bv=dv&&dv.prototype,wv=dv,xv=bv,Sv=$m.TypeError,Ev=$m.document,kv=$m.process,Ov=fv.f,Cv=Ov,Tv=!!(Ev&&Ev.createEvent&&$m.dispatchEvent),jv="unhandledrejection",Pv=function(e){var t;return!(!nv(e)||!rv(t=e.then))&&t},Iv=function(e,t){var r,n,a,o=t.value,i=1==t.state,c=i?e.ok:e.fail,u=e.resolve,s=e.reject,l=e.domain;try{c?(i||(2===t.rejection&&_v(t),t.rejection=1),!0===c?r=o:(l&&l.enter(),r=c(o),l&&(l.exit(),a=!0)),r===e.promise?s(Sv("Promise-chain cycle")):(n=Pv(r))?Km(n,r,u,s):u(r)):s(o)}catch(e){l&&!a&&l.exit(),s(e)}},Nv=function(e,t){e.notified||(e.notified=!0,cv((function(){for(var r,n=e.reactions;r=n.get();)Iv(r,e);e.notified=!1,t&&!e.rejection&&Mv(e)})))},Rv=function(e,t,r){var n,a;Tv?((n=Ev.createEvent("Event")).promise=t,n.reason=r,n.initEvent(e,!1,!0),$m.dispatchEvent(n)):n={promise:t,reason:r},!vv&&(a=$m["on"+e])?a(n):e===jv&&function(e,t){var r=wm.console;r&&r.error&&(1==arguments.length?r.error(e):r.error(e,t))}("Unhandled promise rejection",r)},Mv=function(e){Km(iv,$m,(function(){var t,r=e.facade,n=e.value;if(Av(e)&&(t=uv((function(){Ym?kv.emit("unhandledRejection",n,r):Rv(jv,r,n)})),e.rejection=Ym||Av(e)?2:1,t.error))throw t.value}))},Av=function(e){return 1!==e.rejection&&!e.parent},_v=function(e){Km(iv,$m,(function(){var t=e.facade;Ym?kv.emit("rejectionHandled",t):Rv("rejectionhandled",t,e.value)}))},Dv=function(e,t,r){return function(n){e(t,n,r)}},Lv=function(e,t,r){e.done||(e.done=!0,r&&(e=r),e.value=t,e.state=2,Nv(e,!0))},zv=function(e,t,r){if(!e.done){e.done=!0,r&&(e=r);try{if(e.facade===t)throw Sv("Promise can't be resolved itself");var n=Pv(t);n?cv((function(){var r={done:!1};try{Km(n,t,Dv(zv,r,e),Dv(Lv,r,e))}catch(t){Lv(r,t,e)}})):(e.value=t,e.state=1,Nv(e,!1))}catch(t){Lv({done:!1},t,e)}}};if(mv&&(xv=(wv=function(e){av(this,xv),tv(e),Km(Um,this);var t=gv(this);try{e(Dv(zv,t),Dv(Lv,t))}catch(e){Lv(t,e)}}).prototype,(Um=function(e){yv(this,{type:pv,done:!1,notified:!1,parent:!1,reactions:new sv,rejection:!1,state:0,value:void 0})}).prototype=Xm(xv,"then",(function(e,t){var r=gv(this),n=Ov(ov(this,wv));return r.parent=!0,n.ok=!rv(e)||e,n.fail=rv(t)&&t,n.domain=Ym?kv.domain:void 0,0==r.state?r.reactions.add(n):cv((function(){Iv(n,r)})),n.promise})),Gm=function(){var e=new Um,t=gv(e);this.promise=e,this.resolve=Dv(zv,t),this.reject=Dv(Lv,t)},fv.f=Ov=function(e){return e===wv||void 0===e?new Gm(e):Cv(e)},rv(dv)&&bv!==Object.prototype)){Qm=bv.then,hv||Xm(bv,"then",(function(e,t){var r=this;return new wv((function(e,t){Km(Qm,r,e,t)})).then(e,t)}),{unsafe:!0});try{delete bv.constructor}catch(e){}Jm&&Jm(bv,xv)}qm({global:!0,constructor:!0,wrap:!0,forced:mv},{Promise:wv}),Zm(wv,pv,!1),ev(pv);var Wv=km,Fv=Wm.CONSTRUCTOR||!yu((function(e){Wv.all(e).then(void 0,(function(){}))})),Vv=b,Bv=Ne,Hv=Fm,Uv=xm,Gv=lu;na({target:"Promise",stat:!0,forced:Fv},{all:function(e){var t=this,r=Hv.f(t),n=r.resolve,a=r.reject,o=Uv((function(){var r=Bv(t.resolve),o=[],i=0,c=1;Gv(e,(function(e){var u=i++,s=!1;c++,Vv(r,t,e).then((function(e){s||(s=!0,o[u]=e,--c||n(o))}),a)})),--c||n(o)}));return o.error&&a(o.value),r.promise}});var Qv=na,qv=Wm.CONSTRUCTOR,Yv=km,$v=ce,Kv=ee,Xv=en,Jv=Yv&&Yv.prototype;if(Qv({target:"Promise",proto:!0,forced:qv,real:!0},{catch:function(e){return this.then(void 0,e)}}),Kv(Yv)){var Zv=$v("Promise").prototype.catch;Jv.catch!==Zv&&Xv(Jv,"catch",Zv,{unsafe:!0})}var eh=b,th=Ne,rh=Fm,nh=xm,ah=lu;na({target:"Promise",stat:!0,forced:Fv},{race:function(e){var t=this,r=rh.f(t),n=r.reject,a=nh((function(){var a=th(t.resolve);ah(e,(function(e){eh(a,t,e).then(r.resolve,n)}))}));return a.error&&n(a.value),r.promise}});var oh=b,ih=Fm;na({target:"Promise",stat:!0,forced:Wm.CONSTRUCTOR},{reject:function(e){var t=ih.f(this);return oh(t.reject,void 0,e),t.promise}});var ch=Ut,uh=ne,sh=Fm,lh=na,dh=Wm.CONSTRUCTOR;ce("Promise"),lh({target:"Promise",stat:!0,forced:dh},{resolve:function(e){return function(e,t){if(ch(e),uh(t)&&t.constructor===e)return t;var r=sh.f(e);return(0,r.resolve)(t),r.promise}(this,e)}});var fh=f,ph=gp,mh=ee,vh=yp,hh=wp,gh=/MSIE .\./.test(se),yh=fh.Function,bh=function(e){return gh?function(t,r){var n=hh(arguments.length,1)>2,a=mh(t)?t:yh(t),o=n?vh(arguments,2):void 0;return e(n?function(){ph(a,this,o)}:a,r)}:e},wh={setTimeout:bh(fh.setTimeout),setInterval:bh(fh.setInterval)},xh=wh.setInterval;na({global:!0,bind:!0,forced:f.setInterval!==xh},{setInterval:xh});var Sh=wh.setTimeout;na({global:!0,bind:!0,forced:f.setTimeout!==Sh},{setTimeout:Sh});var Eh=na,kh=yf.findIndex,Oh=zo,Ch="findIndex",Th=!0;Ch in[]&&Array(1).findIndex((function(){Th=!1})),Eh({target:"Array",proto:!0,forced:Th},{findIndex:function(e){return kh(this,e,arguments.length>1?arguments[1]:void 0)}}),Oh(Ch);var jh={},Ph={},Ih={exports:{}},Nh=Ih.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),(t+=null!=e["network-id"]?" network-id %d":"%v")+(null!=e["network-cost"]?" network-cost %d":"%v")}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(null!=e.clksrcExt?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var t="mediaclk:";return t+=null!=e.id?"id=%s %s":"%v%s",t+=null!=e.mediaClockValue?"=%s":"",(t+=null!=e.rateNumerator?" rate=%s":"")+(null!=e.rateDenominator?"/%s":"")}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};Object.keys(Nh).forEach((function(e){Nh[e].forEach((function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")}))})),function(e){var t=function(e){return String(Number(e))===e?Number(e):e},r=function(e,r,n){var a=e.name&&e.names;e.push&&!r[e.push]?r[e.push]=[]:a&&!r[e.name]&&(r[e.name]={});var o=e.push?{}:a?r[e.name]:r;!function(e,r,n,a){if(a&&!n)r[a]=t(e[1]);else for(var o=0;o<n.length;o+=1)null!=e[o+1]&&(r[n[o]]=t(e[o+1]))}(n.match(e.reg),o,e.names,e.name),e.push&&r[e.push].push(o)},n=Ih.exports,a=RegExp.prototype.test.bind(/^([a-z])=(.*)/);e.parse=function(e){var t={},o=[],i=t;return e.split(/(\r\n|\r|\n)/).filter(a).forEach((function(e){var t=e[0],a=e.slice(2);"m"===t&&(o.push({rtp:[],fmtp:[]}),i=o[o.length-1]);for(var c=0;c<(n[t]||[]).length;c+=1){var u=n[t][c];if(u.reg.test(a))return r(u,i,a)}})),t.media=o,t};var o=function(e,r){var n=r.split(/=(.+)/,2);return 2===n.length?e[n[0]]=t(n[1]):1===n.length&&r.length>1&&(e[n[0]]=void 0),e};e.parseParams=function(e){return e.split(/;\s?/).reduce(o,{})},e.parseFmtpConfig=e.parseParams,e.parsePayloads=function(e){return e.toString().split(" ").map(Number)},e.parseRemoteCandidates=function(e){for(var r=[],n=e.split(" ").map(t),a=0;a<n.length;a+=3)r.push({component:n[a],ip:n[a+1],port:n[a+2]});return r},e.parseImageAttributes=function(e){return e.split(" ").map((function(e){return e.substring(1,e.length-1).split(",").reduce(o,{})}))},e.parseSimulcastStreamList=function(e){return e.split(";").map((function(e){return e.split(",").map((function(e){var r,n=!1;return"~"!==e[0]?r=t(e):(r=t(e.substring(1,e.length)),n=!0),{scid:r,paused:n}}))}))}}(Ph);var Rh=Ih.exports,Mh=/%[sdv%]/g,Ah=function(e){var t=1,r=arguments,n=r.length;return e.replace(Mh,(function(e){if(t>=n)return e;var a=r[t];switch(t+=1,e){case"%%":return"%";case"%s":return String(a);case"%d":return Number(a);case"%v":return""}}))},_h=function(e,t,r){var n=[e+"="+(t.format instanceof Function?t.format(t.push?r:r[t.name]):t.format)];if(t.names)for(var a=0;a<t.names.length;a+=1){var o=t.names[a];t.name?n.push(r[t.name][o]):n.push(r[t.names[a]])}else n.push(r[t.name]);return Ah.apply(null,n)},Dh=["v","o","s","i","u","e","p","c","b","t","r","z","a"],Lh=["i","c","b","a"],zh=Ph;jh.write=function(e,t){t=t||{},null==e.version&&(e.version=0),null==e.name&&(e.name=" "),e.media.forEach((function(e){null==e.payloads&&(e.payloads="")}));var r=t.outerOrder||Dh,n=t.innerOrder||Lh,a=[];return r.forEach((function(t){Rh[t].forEach((function(r){r.name in e&&null!=e[r.name]?a.push(_h(t,r,e)):r.push in e&&null!=e[r.push]&&e[r.push].forEach((function(e){a.push(_h(t,r,e))}))}))})),e.media.forEach((function(e){a.push(_h("m",Rh.m[0],e)),n.forEach((function(t){Rh[t].forEach((function(r){r.name in e&&null!=e[r.name]?a.push(_h(t,r,e)):r.push in e&&null!=e[r.push]&&e[r.push].forEach((function(e){a.push(_h(t,r,e))}))}))}))})),a.join("\r\n")+"\r\n"},jh.parse=zh.parse,jh.parseParams=zh.parseParams,jh.parseFmtpConfig=zh.parseFmtpConfig,jh.parsePayloads=zh.parsePayloads,jh.parseRemoteCandidates=zh.parseRemoteCandidates,jh.parseImageAttributes=zh.parseImageAttributes,jh.parseSimulcastStreamList=zh.parseSimulcastStreamList;var Wh=function(){var e=i(n().mark((function e(){var t,r,a,o,i,c;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t="",r=!1,a=!1,e.prev=3,o=new RTCPeerConnection,(i=document.createElement("canvas")).getContext("2d"),c=i.captureStream(0),o.addTrack(c.getVideoTracks()[0],c),e.next=11,o.createOffer();case 11:return-1!==(t=e.sent).sdp.toLowerCase().indexOf("h264")&&(r=!0),-1!==t.sdp.toLowerCase().indexOf("vp8")&&(a=!0),o.close(),e.abrupt("return",{isH264EncodeSupported:r,isVp8EncodeSupported:a});case 18:return e.prev=18,e.t0=e.catch(3),e.abrupt("return",{isH264EncodeSupported:!1,isVp8EncodeSupported:!1});case 21:case"end":return e.stop()}}),e,null,[[3,18]])})));return function(){return e.apply(this,arguments)}}(),Fh=function(){var e=i(n().mark((function e(){var t,r,a,o;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t="",r=!1,a=!1,e.prev=3,o=new RTCPeerConnection,e.next=7,o.createOffer({offerToReceiveAudio:1,offerToReceiveVideo:1});case 7:return-1!==(t=e.sent).sdp.toLowerCase().indexOf("h264")&&(r=!0),-1!==t.sdp.toLowerCase().indexOf("vp8")&&(a=!0),o.close(),e.abrupt("return",{isH264DecodeSupported:r,isVp8DecodeSupported:a});case 14:return e.prev=14,e.t0=e.catch(3),e.abrupt("return",{isH264DecodeSupported:!1,isVp8DecodeSupported:!1});case 17:case"end":return e.stop()}}),e,null,[[3,14]])})));return function(){return e.apply(this,arguments)}}(),Vh=function(){var e=i(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(){var e=i(n().mark((function e(t){var r,a,o,c,u,s,l,d,f,p,m,v,h,g;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r={encode:!1,decode:!1},a=null,e.prev=2,o=document.createElement("canvas"),c=o.getContext("2d"),o.width=640,o.height=480,u=setInterval((function(){c.fillText("test",Math.floor(640*Math.random()),Math.floor(480*Math.random()))}),33),s=-1,l=-1,a=function(){clearInterval(s),clearInterval(u),clearTimeout(l),f.close(),p.close(),d.getTracks().forEach((function(e){return e.stop()}))},l=setTimeout((function(){a(),t(r)}),3e3),d=o.captureStream(),f=new RTCPeerConnection({}),p=new RTCPeerConnection({offerToReceiveAudio:!0,offerToReceiveVideo:!0}),f.addEventListener("icecandidate",(function(e){return p.addIceCandidate(e.candidate)})),p.addEventListener("icecandidate",(function(e){return f.addIceCandidate(e.candidate)})),f.addTrack(d.getVideoTracks()[0],d),e.next=20,f.createOffer();case 20:return m=e.sent,e.next=23,f.setLocalDescription(m);case 23:return e.next=25,p.setRemoteDescription(m);case 25:return e.next=27,p.createAnswer();case 27:return v=e.sent,h=jh.parse(v.sdp),g=h.media[0].rtp.findIndex((function(e){return"H264"===e.codec})),h.media[0].rtp=[h.media[0].rtp[g]],h.media[0].fmtp=h.media[0].fmtp.filter((function(e){return e.payload===h.media[0].rtp[0].payload})),h.media[0].rtcpFb=h.media[0].rtcpFb.filter((function(e){return e.payload===h.media[0].rtp[0].payload})),v.sdp=jh.write(h),e.next=36,p.setLocalDescription(v);case 36:return e.next=38,f.setRemoteDescription(v);case 38:s=setInterval(i(n().mark((function e(){var o,i;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r.encode&&r.decode&&(a(),t(r)),e.next=3,f.getStats();case 3:return o=e.sent,e.next=6,p.getStats();case 6:i=e.sent,r.encode||o.forEach((function(e){"outbound-rtp"===e.type&&"video"===e.mediaType&&e.framesEncoded>0&&(r.encode=!0)})),r.decode||i.forEach((function(e){"inbound-rtp"===e.type&&"video"===e.mediaType&&e.framesDecoded>0&&(r.decode=!0)}));case 9:case"end":return e.stop()}}),e)}))),500),e.next=45;break;case 41:e.prev=41,e.t0=e.catch(2),a(),t(r);case 45:case"end":return e.stop()}}),e,null,[[2,41]])})));return function(t){return e.apply(this,arguments)}}()));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Bh=na,Hh=yf.find,Uh=zo,Gh=!0;function Qh(){return qh.apply(this,arguments)}function qh(){return(qh=i(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(){var e=i(n().mark((function e(t,r){var a,o,c,u,s,l,d,f,p,m,v,h,g,y;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,a=function(){clearInterval(g),clearInterval(u),clearTimeout(y),l.close(),d.close(),s.getTracks().forEach((function(e){return e.stop()}))},o=document.createElement("canvas"),c=o.getContext("2d"),o.width=640,o.height=480,u=setInterval((function(){c.fillText("test",0,0)}),33),s=o.captureStream(15),l=new RTCPeerConnection({}),d=new RTCPeerConnection({}),l.addEventListener("icecandidate",(function(e){d.addIceCandidate(e.candidate)})),d.addEventListener("icecandidate",(function(e){l.addIceCandidate(e.candidate)})),f=RTCRtpSender.getCapabilities("video"),p=f.codecs,m=p.find((function(e){return"video/H264"===e.mimeType})),l.addTransceiver(s.getVideoTracks()[0],{direction:"sendonly",streams:[s],sendEncodings:[{maxBitrate:5e5}]}),d.addTransceiver("video",{direction:"recvonly"}),l.getTransceivers()[0].setCodecPreferences([m]),e.next=19,l.createOffer();case 19:return v=e.sent,e.next=22,l.setLocalDescription(v);case 22:return e.next=24,d.setRemoteDescription(v);case 24:return e.next=26,d.createAnswer();case 26:return h=e.sent,e.next=29,d.setLocalDescription(h);case 29:return e.next=31,l.setRemoteDescription(h);case 31:g=-1,y=-1,y=setTimeout((function(){a(),t(!1)}),3e4),g=setInterval(i(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,l.getStats();case 2:e.sent.forEach((function(e){"outbound-rtp"===e.type&&"video"===e.mediaType&&(void 0===e.encoderImplementation&&(a(),r(new Error("your browser does not support to detect HW acceleration enabled."))),"ExternalEncoder"===e.encoderImplementation&&(a(),t(!0)))}));case 4:case"end":return e.stop()}}),e)}))),500),e.next=40;break;case 37:e.prev=37,e.t0=e.catch(0),r(e.t0);case 40:case"end":return e.stop()}}),e,null,[[0,37]])})));return function(t,r){return e.apply(this,arguments)}}()));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}return"find"in[]&&Array(1).find((function(){Gh=!1})),Bh({target:"Array",proto:!0,forced:Gh},{find:function(e){return Hh(this,e,arguments.length>1?arguments[1]:void 0)}}),Uh("find"),function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.system=null,this.APISupported=null,this.devices=null,this.codecsSupported=null,this.report=null}var t,r,a,o,u,s,l;return t=e,(r=[{key:"isTRTCSupported",value:(l=i(n().mark((function e(){var t,r,a,o,i,c,u,s,l;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getReportAsync();case 2:if(t=this.APISupported,r=t.isWebRTCSupported,a=t.isUserMediaSupported,o=t.isWebSocketSupported,r){e.next=5;break}return e.abrupt("return",{result:!1,reason:"Your browser does not support WebRTC."});case 5:if(o){e.next=7;break}return e.abrupt("return",{result:!1,reason:"Your browser does not support WebSocket."});case 7:if(a){e.next=9;break}return e.abrupt("return",{result:!1,reason:"Your browser does not support getUserMedia."});case 9:if(i=this.codecsSupported,c=i.isH264EncodeSupported,u=i.isVp8EncodeSupported,s=i.isH264DecodeSupported,l=i.isVp8DecodeSupported,c||u){e.next=12;break}return e.abrupt("return",{result:!1,reason:"isH264EncodeSupported: ".concat(c," isVp8EncodeSupported: ").concat(u," ")});case 12:if(s||l){e.next=14;break}return e.abrupt("return",{result:!1,reason:"isH264DecodeSupported: ".concat(s," isVp8DecodeSupported: ").concat(l," ")});case 14:if(!(Bd||hd||yd&&bd<80||md&&vd<56)){e.next=16;break}return e.abrupt("return",{result:!1,reason:"your browser does not qualify, it is recommended to use Chrome and firefox."});case 16:return e.abrupt("return",{result:!0});case 17:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})},{key:"getReportAsync",value:(s=i(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.getSystem(),this.getAPISupported(),e.next=4,this.getCodecAsync();case 4:return e.next=6,this.getDevicesAsync();case 6:return this.report={system:this.system,APISupported:this.APISupported,codecsSupported:this.codecsSupported,devices:this.devices},e.abrupt("return",this.report);case 8:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"getCodecAsync",value:(u=i(n().mark((function e(){var t,r,a,o,i,c,u,s,l;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Wh();case 2:return t=e.sent,r=t.isH264EncodeSupported,a=t.isVp8EncodeSupported,e.next=7,Fh();case 7:if(o=e.sent,i=o.isH264DecodeSupported,c=o.isVp8DecodeSupported,!r||!i||!$d&&!Xd){e.next=18;break}return e.next=13,Vh();case 13:u=e.sent,s=u.encode,l=u.decode,r=s,i=l;case 18:return this.codecsSupported={isH264EncodeSupported:r,isVp8EncodeSupported:a,isH264DecodeSupported:i,isVp8DecodeSupported:c},e.abrupt("return",this.codecsSupported);case 20:case"end":return e.stop()}}),e,this)}))),function(){return u.apply(this,arguments)})},{key:"getDevicesAsync",value:(o=i(n().mark((function e(){var t,r,a,o,i,c;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,np();case 2:return t=e.sent,r=t.hasCameraPermission,a=t.hasMicrophonePermission,o=t.cameras,i=t.microphones,c=t.speakers,this.devices={cameras:o,microphones:i,speakers:c,hasCameraPermission:r,hasMicrophonePermission:a},e.abrupt("return",this.devices);case 10:case"end":return e.stop()}}),e,this)}))),function(){return o.apply(this,arguments)})},{key:"getSystem",value:function(){var e,t;return this.system={UA:sf,OS:(t="unknown",of.get(!0)&&(t=of.get(!0)),t),browser:uf(),displayResolution:lf(),hardwareConcurrency:null===(e=window.navigator)||void 0===e?void 0:e.hardwareConcurrency},this.system}},{key:"getAPISupported",value:function(){var e,t,r;return this.APISupported={isUserMediaSupported:rp(),isWebRTCSupported:["RTCPeerConnection","webkitRTCPeerConnection","RTCIceGatherer"].filter((function(e){return e in window})).length>0,isWebSocketSupported:"WebSocket"in window&&2===window.WebSocket.CLOSING,isWebAudioSupported:kf(),isScreenCaptureAPISupported:!(null===(r=navigator.mediaDevices)||void 0===r||!r.getDisplayMedia),isCanvasCapturingSupported:(t=!1,["captureStream","mozCaptureStream","webkitCaptureStream"].forEach((function(e){"undefined"!=typeof document&&"function"==typeof document.createElement&&!t&&e in document.createElement("canvas")&&(t=!0)})),t),isVideoCapturingSupported:(e=!1,["captureStream","mozCaptureStream","webkitCaptureStream"].forEach((function(t){"undefined"!=typeof document&&"function"==typeof document.createElement&&!e&&t in document.createElement("video")&&(e=!0)})),e),isRTPSenderReplaceTracksSupported:Of(),isApplyConstraintsSupported:"undefined"!=typeof MediaStreamTrack&&"applyConstraints"in MediaStreamTrack.prototype},this.APISupported}},{key:"isHardWareAccelerationEnabled",value:(a=i(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Qh();case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)}))),function(){return a.apply(this,arguments)})}])&&c(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}()}()}).call(this,r(143))},293:function(e,t,r){"use strict";var n=r(28),a=r(25);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,h.useTranslation)().t,r=(0,u.useState)(0),n=(0,c.default)(r,2),a=n[0],v=n[1],y=(0,u.useState)(e.mark.video),w=(0,c.default)(y,2),x=w[0],S=w[1],E=(0,u.useState)(e.mark.audio),k=(0,c.default)(E,2),O=k[0],C=k[1],T=(0,u.useState)(),j=(0,c.default)(T,2),P=j[0],I=j[1],N=b(),R=function(){var e=(0,i.default)(o.default.mark((function e(t){return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(0,g.isBoolean)(t.microphone)&&(C(t.microphone),v(1)),(0,g.isBoolean)(t.camera)&&(S(t.camera),v(2)),(0,g.isBoolean)(t.speaker)&&(I(t.speaker),v(3));case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return u.default.createElement("div",{className:"wrapper"},0===a&&u.default.createElement("div",{className:"three-item"},u.default.createElement(l.default,{handleState:R})),1===a&&u.default.createElement("div",{className:"three-item"},u.default.createElement(s.default,{handleState:R})),2===a&&u.default.createElement("div",{className:"three-item"},u.default.createElement(d.default,{handleState:R})),3===a&&u.default.createElement("div",{className:"three-item"},u.default.createElement("div",{className:"device-result-title"},t("检测完毕")),u.default.createElement("div",{className:"device-result"},u.default.createElement("div",{className:O?"icon-support":"icon-support-failed"},u.default.createElement(p.default,{classes:N})),u.default.createElement("div",{className:x?"icon-support":"icon-support-failed"},u.default.createElement(f.default,{classes:N})),u.default.createElement("div",{className:P?"icon-support":"icon-support-failed"},u.default.createElement(m.default,{classes:N})))))};var o=n(r(66)),i=n(r(67)),c=n(r(31)),u=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=y(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=o?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(n,i,c):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(0));r(294);var s=n(r(296)),l=n(r(303)),d=n(r(304)),f=n(r(305)),p=n(r(306)),m=n(r(307)),v=r(105),h=r(50),g=r(142);function y(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(y=function(e){return e?r:t})(e)}var b=(0,v.makeStyles)((function(){return{root:{fontSize:"2em"}}}))},294:function(e,t,r){var n=r(295);"string"==typeof n&&(n=[[e.i,n,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};r(65)(n,a);n.locals&&(e.exports=n.locals)},295:function(e,t,r){(t=r(64)(!1)).push([e.i,".device-wrapper{padding-top:10px;border-bottom:1px solid #eee;width:100%}.device-card-title{padding:8px 0 0 0;margin-right:12px;color:rgba(0,0,0,.85);font-weight:600;font-size:14px}.device-card-content{font-size:14px;line-height:22px}.device-content{height:120px;color:rgba(0,0,0,.7);width:160px;display:flex;flex-direction:column;justify-content:center}.device-switch{padding-top:20px;width:200px}.volume-wrapper{width:160px;height:10px;background-color:#ccc}.device-list{padding:0 0 10px 0}.volume{height:10px;background-color:#3f51b5;transition:all .2s ease-out}.video{height:100px;width:120px}.device-item{display:flex;flex-direction:column;line-height:1.8}.resolution{padding-right:10px}.bottom-item{padding-bottom:10px}.wrapper{width:100%}.audio{height:30px;width:100%;max-width:300px}.result-success{font-size:20px;font-weight:bold;color:#4caf50}.result-fail{font-size:20px;font-weight:bold;color:#f44336}.card{display:flex;flex-direction:column;justify-content:center;align-items:center;padding:16px;width:100%}.device-container{display:flex;justify-content:center}.speaker-container{height:120px;color:rgba(0,0,0,.7);display:flex;flex-direction:column;justify-content:center;width:230px}.next{height:50px;padding-top:15px}.next>button{margin-right:10px}.device-info{font-size:14px}.three-item{width:100%}.device-result-title{padding:20px 0;display:flex;justify-content:center;font-size:20px;font-weight:bold;color:rgba(0,0,0,.85)}.support{color:#4caf50;font-weight:bold}.support-half{color:#ffc53d;font-weight:bold}.support-failed{color:#f44336;font-weight:bold}.icon-support{color:#4caf50;font-weight:bold;padding:10px}.icon-support-failed{color:#f44336;font-weight:bold;padding:10px}.device-result{display:flex;justify-content:center;padding-bottom:20px}.audio-container{display:none}",""]),e.exports=t},296:function(e,t,r){"use strict";var n=r(28),a=r(25);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,f.useTranslation)().t,r=(0,s.useState)([]),n=(0,u.default)(r,2),a=n[0],v=n[1],h=(0,s.useState)("default"),g=(0,u.default)(h,2),y=g[0],b=g[1];(0,s.useEffect)((function(){(function(){var e=(0,c.default)(i.default.mark((function e(){var t;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d.default.getCameras();case 2:return(t=e.sent).forEach((function(e){e.value=e.deviceId})),v(t),b(t[0]),o=d.default.createStream({video:!0,audio:!1,cameraId:t[0].deviceId}),e.next=9,o.initialize();case 9:o.play("video").then((function(){return console.log("play success")})).catch((function(){return console.log("play error")}));case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[]);var w=function(t){e.handleState({camera:t}),o&&o.close()},x=function(){var e=(0,c.default)(i.default.mark((function e(t){return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:b(t),o.switchDevice("video",t.value).then((function(){return console.log("switchDevice success")})).catch((function(){return console.log("switchDevice error")}));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return s.default.createElement("div",{className:"card"},s.default.createElement("div",{className:"device-card-title"},t("摄像头检测")),s.default.createElement("div",{className:"device-card-content"},t("是否看到视频画面")),s.default.createElement("div",{className:"device-switch"},s.default.createElement(m.default,{value:y,onChange:function(e){return x(e)},options:a,width:"200px",menuColor:"red"})),s.default.createElement("div",{className:"device-content"},s.default.createElement("div",{className:"device-container"},s.default.createElement("div",{id:"video",className:"video"}))),s.default.createElement("div",{className:"next"},s.default.createElement(l.default,{color:"default",size:"small",variant:"contained",onClick:function(){return w(!1)}},t("否")),s.default.createElement(p.default,{color:"default",size:"small",variant:"contained",onClick:function(){return w(!0)}},t("是"))))};var o,i=n(r(66)),c=n(r(67)),u=n(r(31)),s=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=v(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=o?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(n,i,c):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(0)),l=n(r(45)),d=n(r(99)),f=r(50),p=n(r(104)),m=n(r(247));function v(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(v=function(e){return e?r:t})(e)}},303:function(e,t,r){"use strict";var n=r(28),a=r(25);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,d.useTranslation)().t,r=(0,u.useState)(0),n=(0,c.default)(r,2),a=n[0],m=n[1],g=(0,u.useState)([]),y=(0,c.default)(g,2),b=y[0],w=y[1],x=(0,u.useState)("default"),S=(0,c.default)(x,2),E=S[0],k=S[1];(0,u.useEffect)((function(){return function(){var e=(0,i.default)(o.default.mark((function e(){var t;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,l.default.getMicrophones();case 2:return(t=e.sent).forEach((function(e){e.value=e.deviceId})),w(t),k(t[0]),h=l.default.createStream({video:!1,audio:!0,microphoneId:t[0].deviceId}),e.next=9,h.initialize();case 9:h.play("audio-container"),v=setInterval((function(){var e=h.getAudioLevel();m(e)}),200);case 11:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()(),function(){clearInterval(v)}}),[]);var O=function(t){e.handleState({microphone:t}),h&&h.close()},C=function(){var e=(0,i.default)(o.default.mark((function e(t){return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:k(t),h.switchDevice("audio",t.value).then((function(){return console.log("switchDevice success")})).catch((function(){return console.log("switchDevice error")}));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return u.default.createElement("div",{className:"card"},u.default.createElement("div",{className:"device-card-title"},t("麦克风检测")),u.default.createElement("div",{className:"device-card-content"},t("是否看到音量条变化")),u.default.createElement("div",{className:"device-switch"},u.default.createElement(p.default,{value:E,onChange:function(e){return C(e)},options:b,width:"200px",menuColor:"red"})),u.default.createElement("div",{className:"device-content"},u.default.createElement("div",{className:"volume-wrapper"},u.default.createElement("div",{id:"audio-container",className:"audio-container"}),u.default.createElement("div",{className:"volume",style:{width:"".concat(100*a,"%")}}))),u.default.createElement("div",{className:"next"},u.default.createElement(s.default,{color:"default",size:"small",variant:"contained",onClick:function(){return O(!1)}},t("否")),u.default.createElement(f.default,{color:"default",size:"small",variant:"contained",onClick:function(){return O(!0)}},t("是"))))};var o=n(r(66)),i=n(r(67)),c=n(r(31)),u=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=m(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=o?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(n,i,c):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(0)),s=n(r(45)),l=n(r(99)),d=r(50),f=n(r(104)),p=n(r(247));function m(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(m=function(e){return e?r:t})(e)}var v=0,h=null},304:function(e,t,r){"use strict";var n=r(28);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,i.useTranslation)().t;return a.default.createElement("div",{className:"card"},a.default.createElement("div",{className:"device-card-title"},t("扬声器检测")),a.default.createElement("div",{className:"device-card-content"},t("点击播放后能否听到音乐")),a.default.createElement("div",{className:"speaker-container"},a.default.createElement("div",{className:"device-container"},a.default.createElement("audio",{src:"https://web.sdk.qcloud.com/trtc/webrtc/assets/testspeak.mp3",controlsList:"nodownload",controls:!0,loop:!0,crossOrigin:"anonymous",className:"audio"}))),a.default.createElement("div",{className:"next"},a.default.createElement(o.default,{color:"default",size:"small",variant:"contained",onClick:function(){return e.handleState({speaker:!1})}},"否"),a.default.createElement(c.default,{color:"default",size:"small",variant:"contained",onClick:function(){return e.handleState({speaker:!0})}},"是")))};var a=n(r(0)),o=n(r(45)),i=r(50),c=n(r(104))},308:function(e,t,r){"use strict";var n=r(28),a=r(25);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,d.useTranslation)().t,r=(0,u.useState)(0),n=(0,i.default)(r,2),a=n[0],p=n[1],h=(0,u.useState)(),E=(0,i.default)(h,2),k=(E[0],E[1]),O=(0,u.useState)(15),C=(0,i.default)(O,2),T=C[0],j=C[1],P=(0,u.useState)(),I=(0,i.default)(P,2),N=(I[0],I[1]),R=(0,u.useState)(0),M=(0,i.default)(R,2),A=M[0],_=M[1],D=(0,u.useState)(0),L=(0,i.default)(D,2),z=L[0],W=L[1],F=(0,u.useState)(0),V=(0,i.default)(F,2),B=V[0],H=V[1];(0,u.useEffect)((function(){(function(){var e=(0,c.default)(o.default.mark((function e(){var t,r;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("checkToken"),t=s.default.get("trtc-api-example-token"),r=s.default.get("trtc-api-example-phoneNumber"),!t||!r){e.next=11;break}return k(t),N(r),e.next=8,G(r,t);case 8:x<0&&(j(15),Q()),e.next=12;break;case 11:window.location="".concat(window.location.origin,"/trtc/webrtc/demo/api-sample/login.html?from=/trtc/webrtc/demo/detect/index.html");case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[]);var U=function(){var e=(0,c.default)(o.default.mark((function e(){var t,r;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(window.location="".concat(window.location.origin,"/trtc/webrtc/demo/api-sample/login.html?from=/trtc/webrtc/demo/detect/index.html"),t=s.default.get("trtc-token"),r=s.default.get("phoneNumber"),!t||!r){e.next=9;break}return k(t),N(r),e.next=8,G(r,t);case 8:x<0&&(j(15),Q());case 9:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),G=function(){var e=(0,c.default)(o.default.mark((function e(t,r){var n,a,i,u,s;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n="rtc_detect_up".concat(1e8*Math.random()),a="rtc_detect_down".concat(1e8*Math.random()),i="rtc_detect_".concat(1e8*Math.random()),e.next=5,S({roomId:i,userId:n,phone:t,token:r});case 5:return u=e.sent,e.next=8,S({roomId:i,userId:a,phone:t,token:r});case 8:if(s=e.sent,u&&s){e.next=12;break}return p(-1),e.abrupt("return");case 12:return g=m.default.createClient({sdkAppId:1400188366,useStringRoomId:!0,userId:n,userSig:u,mode:"rtc"}),b=m.default.createStream({audio:!0,video:!0}),e.next=16,b.initialize();case 16:return g.on("network-quality",function(){var e=(0,c.default)(o.default.mark((function e(t){var r,n,a;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.uplinkNetworkQuality,e.next=3,g.getTransportStats();case 3:n=e.sent,a=n.rtt,w.uplink.push(r),w.rtt.push(a);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.next=19,g.join({roomId:i});case 19:return e.next=21,g.publish(b);case 21:return(y=m.default.createClient({sdkAppId:1400188366,useStringRoomId:!0,userId:a,userSig:s,mode:"rtc"})).on("stream-added",function(){var e=(0,c.default)(o.default.mark((function e(t){return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,y.subscribe(t.stream,{audio:!0,video:!0});case 2:y.on("network-quality",(function(e){var t=e.downlinkNetworkQuality;w.downlink.push(t)}));case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.next=25,y.join({roomId:i});case 25:case"end":return e.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),Q=function(){x=setInterval((function(){j((function(e){var t=e-1;return 0===t?(q(),clearInterval(x),0):t}))}),1e3)},q=function(){var t=Math.ceil(w.uplink.reduce((function(e,t){return e+t}),0)/w.uplink.length),r=Math.ceil(w.downlink.reduce((function(e,t){return e+t}),0)/w.downlink.length),n=Math.ceil(w.rtt.reduce((function(e,t){return e+t}),0)/w.rtt.length);_(t),W(r),H(n),e.handleReport({uplinkAverage:t,downlinkAverage:r,rttAverage:n}),g&&g.leave(),y&&y.leave(),b.getAudioTrack().stop(),b.getVideoTrack().stop()};return u.default.createElement("div",{className:"wrapper"},a>-1&&(T>0?u.default.createElement("div",{className:"loading-time"},u.default.createElement(v.default,null),u.default.createElement("div",{className:"count-down"},t("剩余检测时间")," ",T," s")):u.default.createElement("div",{className:"wrapper-list"},u.default.createElement("div",{className:"item-container"},u.default.createElement("div",null,t("网络延时")),u.default.createElement("div",null,B,"ms")),u.default.createElement("div",{className:"item-container"},u.default.createElement("div",null,t("上行网络质量")),u.default.createElement("div",null,t(l.NETWORK_QUALITY[A]))),u.default.createElement("div",{className:"item-container"},u.default.createElement("div",null,t("下行网络质量")),u.default.createElement("div",null,t(l.NETWORK_QUALITY[z]))))),-1===a&&u.default.createElement(u.default.Fragment,null,u.default.createElement("div",{className:"login-failed"},t("登录失败")),u.default.createElement("div",{className:"re-login"},u.default.createElement(f.default,{color:"default",variant:"contained",onClick:function(){return U()}},t("重新登录")))))};var o=n(r(66)),i=n(r(31)),c=n(r(67)),u=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};var r=h(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=o?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(n,i,c):n[i]=e[i]}n.default=e,r&&r.set(e,n);return n}(r(0)),s=n(r(309)),l=r(142),d=r(50),f=n(r(45)),p=n(r(310)),m=n(r(99)),v=n(r(148));function h(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(h=function(e){return e?r:t})(e)}r(332);var g=null,y=null,b=null,w={uplink:[],downlink:[],rtt:[]},x=-10,S=function(){var e=(0,c.default)(o.default.mark((function e(t){var r,n,a,i,c,u;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.roomId,n=t.userId,a=t.phone,i=t.token,e.next=3,p.default.post("https://demo-2gfpjobv0b7026e1-1256993030.ap-shanghai.app.tcloudbase.com/userSigService",{pwd:"12345678",appid:1400188366,roomnum:parseInt(r),identifier:n,phone:a,token:i});case 3:if(!(c=e.sent)||0!==c.data.errorCode){e.next=7;break}return u=c.data.data.userSig,e.abrupt("return",u);case 7:console.error("got invalid json:".concat(c));case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},330:function(e,t,r){var n=r(331);"string"==typeof n&&(n=[[e.i,n,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};r(65)(n,a);n.locals&&(e.exports=n.locals)},331:function(e,t,r){(t=r(64)(!1)).push([e.i,'.loading{display:flex;justify-content:center;align-items:center;min-height:200px;margin:0}.clock-loader{--clock-color: #1E88E5;--clock-width: 3rem;--clock-radius: calc(var(--clock-width) / 2);--clock-minute-length: calc(var(--clock-width) * 0.4);--clock-hour-length: calc(var(--clock-width) * 0.2);--clock-thickness: 0.2rem;position:relative;display:flex;justify-content:center;align-items:center;width:var(--clock-width);height:var(--clock-width);border:3px solid var(--clock-color);border-radius:50%}.clock-loader::before,.clock-loader::after{position:absolute;content:"";top:calc(var(--clock-radius)*.25);width:var(--clock-thickness);background:var(--clock-color);border-radius:10px;transform-origin:center calc(100% - var(--clock-thickness)/2);animation:spin infinite linear}.clock-loader::before{height:var(--clock-minute-length);animation-duration:2s}.clock-loader::after{top:calc(var(--clock-radius)*.25 + var(--clock-hour-length));height:var(--clock-hour-length);animation-duration:15s}@keyframes spin{to{transform:rotate(1turn)}}',""]),e.exports=t},332:function(e,t,r){var n=r(333);"string"==typeof n&&(n=[[e.i,n,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};r(65)(n,a);n.locals&&(e.exports=n.locals)},333:function(e,t,r){(t=r(64)(!1)).push([e.i,".loading-time{width:100%;display:flex;flex-direction:column;justify-content:center;align-items:center}.count-down{font-size:14px;color:rgba(0,0,0,.85);display:flex;justify-content:center;padding-bottom:10px}.re-login{width:100%;display:flex;justify-content:center;padding:20px 0}.wrapper-list{width:100%}.wrapper-list .item-container{font-size:14px;width:100%;display:flex;flex-direction:row;padding:10px 0;justify-content:space-between}.login-failed{display:flex;justify-content:center;padding:20px 0;color:#f44336;font-size:14px;font-weight:bold}",""]),e.exports=t},334:function(e,t,r){var n=r(335);"string"==typeof n&&(n=[[e.i,n,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};r(65)(n,a);n.locals&&(e.exports=n.locals)},335:function(e,t,r){(t=r(64)(!1)).push([e.i,"html,body{padding:0;margin:0;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;width:100%;height:100%}*{box-sizing:border-box}#capture{width:100%}.headline{display:flex}.bottom{display:flex;justify-content:center;align-items:center}.downloadIcon{width:24px;height:24px;display:flex;justify-content:center;align-items:center;border:0;border-radius:3px;color:#fff;padding:0 0;cursor:pointer;user-select:none}.downloadIcon>svg{user-select:none}",""]),e.exports=t},336:function(e,t,r){"use strict";var n=r(28);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(340)),o=n(r(337)),i=n(r(338)),c=r(50);a.default.use(c.initReactI18next).init({resources:{zh:{translation:i.default},en:{translation:o.default}},fallbackLng:"en",debug:!1,interpolation:{escapeValue:!1}});var u=a.default;t.default=u},337:function(e){e.exports=JSON.parse('{"检测结果概览":"Overview of results","是否支持 TRTC":"Browser supports TRTC","是否支持进房【 检测项 3 && 5 】":"Browser supports join room【 Item 3 && 5 】","是否支持推流【 检测项 3 && 5 && 11 && (6 || 7 || 15 || 16) 】":"Browser supports publish local stream【 Item  3 && 5 && 11 && (6 || 7 || 15 || 16) 】","是否支持拉流【 检测项 3 && 5 && 12 】":"Browser supports subscribe remote stream【 Item 3 && 5 && 12 】","如果 15 或 16 支持，您可以从媒体设备获取数据流。如果 6 或 7 支持，您可以从 Video 或者 Canvas 采集数据流。":"If 15 or 16 support, you can get the data stream from the media device. if 6 or 7 support, you can get the data stream from Video or Canvas.","浏览器支持 H264 编码，则可以支持推本地流":"Browser supports H264 encoding, you can publish LocalStream","浏览器支持 H264 解码，则可以支持拉远端流":"Browser supports H264 decoding, you can subscribe RemoteStream","是否可以从摄像头和麦克风采集视频和音频":"Can you get media from camera and microphone","是否支持从 Canvas 采集本地流":"Can you capture local stream from Canvas","是否支持从 Video 采集本地流":"Can you capture local stream from Video","MediaStreamTrack 是否有 applyConstraints 方法":"MediaStreamTrack support applyConstraints()","replaceTrack 支持动态操作 MediaStreamTrack":"The replaceTrack method supports dynamic manipulation of MediaStreamTrack","生成报告图":"Generate report","基础环境":"Basic environment","API 支持":"API support","编码支持":"Encoding support","H264 编码":"H264 encode","H264 解码":"H264 decode","VP8 编码":"VP8 encode","VP8 解码":"VP8 decode","设备详情":"Device details","麦克风设备列表":"Microphone list","摄像头设备列表":"Camera list","扬声器设备列表":"Speaker list","设备支持度检测":"Device support detection","操作系统":"Operating system","浏览器":"Browser","UA":"UA","屏幕分辨率":"Display resolutions","逻辑处理器数量":"Number of logical processors","开始检测":"Start testing","最大分辨率":"Maximum resolution","最大帧率":"Maximum frame rate","是":"Yes","否":"No","部分支持":"Partial","是否允许使用摄像头":"Website has camera permissions","是否允许使用麦克风":"Website has microphone permissions","是否看到视频画面":"Do you see the video","是否看到音量条变化":"Can you see the volume bar change","点击播放后能否听到音乐":"Can you hear music after clicking","是否支持获取媒体设备及媒体流":"Browser allows getUserMedia on this page","是否支持屏幕分享":"Browser supports screen sharing","是否支持 WebRTC":"Browser Supports WebRTC","是否支持 WebAudio":"Browser Supports Web Audio API","是否支持 WebSocket":"Browser Supports WebSocket","是否支持从 Canvas 获取数据流":"Browser supports stream capturing from Canvas","是否支持从 Video 获取数据流":"Browser supports stream capturing from video","是否支持 RTCRtpSender.replaceTrack 方法":"Browser supports RTCRtpSender.replaceTrack","是否支持 applyConstraints":"Browser supports applyConstraints","摄像头检测":"Camera detection","麦克风检测":"Microphone detection","扬声器检测":"Speaker detection","当前环境不支持获取":"Browser does not support acquisition","检测完毕":"Detection completed","网络检测":"Network detection","检测网络前需要先登录，如果您没有登录的话会自动跳转登录":"You need to sign in before detecting the network, or it will automatically jump to login page","剩余检测时间":"Remaining detection time","网络延时":"Network delay","上行网络质量":"Upstream network quality","下行网络质量":"Downstream network quality","登录失败":"Login failed","重新登录":"Login again","未知":"UNKNOWN","极佳":"PERFECT","较好":"GOOD","一般":"GENERAL","差":"BAD","极差":"AWFUL","断开":"DISCONNECTED","http":"The full TRTC functionality is only available under the HTTPS protocol.","refer":"Reference URL Domain Protocol Restrictions"}')},338:function(e){e.exports=JSON.parse('{"检测结果概览":"检测结果概览","是否支持 TRTC":"是否支持 TRTC","是否支持进房【 检测项 3 && 5 】":"是否支持进房【 检测项 3 && 5 】","是否支持推流【 检测项 3 && 5 && 11 && (6 || 7 || 15 || 16) 】":"是否支持推流【 检测项 3 && 5 && 11 && (6 || 7 || 15 || 16) 】","如果 15 或 16 支持，您可以从媒体设备获取数据流。如果 6 或 7 支持，您可以从 Video 或者 Canvas 采集数据流。":"如果 15 或 16 支持，您可以从媒体设备获取数据流。如果 6 或 7 支持，您可以从 Video 或者 Canvas 采集数据流。","是否支持拉流【 检测项 3 && 5 && 12 】":"是否支持拉流【 检测项 3 && 5 && 12 】","浏览器支持 H264 编码，则可以支持推本地流":"浏览器支持 H264 编码，则可以支持推本地流","浏览器支持 H264 解码，则可以支持拉远端流":"浏览器支持 H264 解码，则可以支持拉远端流","是否可以从摄像头和麦克风采集视频和音频":"是否可以从摄像头和麦克风采集视频和音频","是否支持从 Canvas 采集本地流":"是否支持从 Canvas 采集本地流","是否支持从 Video 采集本地流":"是否支持从 Video 采集本地流","MediaStreamTrack 是否有 applyConstraints 方法":"MediaStreamTrack 是否有 applyConstraints 方法","是否支持对通话能力无影响":"是否支持对通话能力无影响","生成报告图":"生成报告图","基础环境":"基础环境","API 支持":"API 支持","编码支持":"编码支持","H264 编码":"H264 编码","H264 解码":"H264 解码","VP8 编码":"VP8 编码","VP8 解码":"VP8 解码","设备详情":"设备详情","麦克风设备列表":"麦克风设备列表","摄像头设备列表":"摄像头设备列表","扬声器设备列表":"扬声器设备列表","设备支持度检测":"设备支持度检测","操作系统":"操作系统","浏览器":"浏览器","UA":"UA","屏幕分辨率":"屏幕分辨率","逻辑处理器数量":"逻辑处理器数量","开始检测":"开始检测","最大分辨率":"最大分辨率","是":"是","否":"否","部分支持":"部分支持","是否允许使用摄像头":"是否允许使用摄像头","是否允许使用麦克风":"是否允许使用麦克风","是否看到视频画面":"是否看到视频画面","是否看到音量条变化":"是否看到音量条变化","点击播放后能否听到音乐":"点击播放后能否听到音乐","是否支持获取媒体设备及媒体流":"是否支持获取媒体设备及媒体流","是否支持屏幕分享":"是否支持屏幕分享","是否支持 WebRTC":"是否支持 WebRTC","是否支持 WebAudio":"是否支持 WebAudio","是否支持 WebSocket":"是否支持 WebSocket","是否支持从 Canvas 获取数据流":"是否支持从 Canvas 获取数据流","是否支持从 Video 获取数据流":"是否支持从 Video 获取数据流","replaceTrack 支持动态操作 MediaStreamTrack":"replaceTrack 支持动态操作 MediaStreamTrack","是否支持 RTCRtpSender.replaceTrack 方法":"是否支持 RTCRtpSender.replaceTrack 方法","是否支持 applyConstraints":"是否支持 applyConstraints","摄像头检测":"摄像头检测","麦克风检测":"麦克风检测","扬声器检测":"扬声器检测","当前环境不支持获取":"当前环境不支持获取","检测完毕":"检测完毕","网络检测":"网络检测","检测网络前需要先登录，如果您没有登录的话会自动跳转登录":"检测网络前需要先登录，如果您没有登录的话会自动跳转登录","剩余检测时间":"剩余检测时间","网络延时":"网络延时","上行网络质量":"上行网络质量","下行网络质量":"下行网络质量","登录失败":"登录失败","重新登录":"重新登录","未知":"未知","极佳":"极佳","较好":"较好","一般":"一般","差":"差","极差":"极差","断开":"断开","http":"只有在 HTTPS 协议下才能使用全部的 TRTC 功能。","refer":"参考 URL 域名协议限制"}')}});