<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>TRTC Demo</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="/lib-generate-test-usersig.min.js"></script>
</head>

<body>
  <div id="app"></div>
  <script type="module" src="/src/main.ts"></script>
  <video id="music_A" src="testA.mp4" loop controls style="display: none;"></video>
  <script>
    class StreamGenerator {
      constructor(options) {
        this.id = options.id;
        this.video_ = document.querySelector(this.id);
        this.mediaStream = this.video_.captureStream();
        // this.canvas_ = document.createElement('canvas');
        // this.canvas_.width = 640;
        // this.canvas_.height = 480;
        // this.canvasCtx_ = this.canvas_.getContext('2d');
      }

      generateVideoTrack() {
        return this.mediaStream.getVideoTracks()[0];
        // const stream = this.canvas_.captureStream();
        // this.render();
        // return stream.getVideoTracks()[0];
      }

      generateAudioTrack() {
        return this.mediaStream.getAudioTracks()[0];
      }

      render() {
        if (this.canDrawVideoToCanvas) {
          this.canvasCtx_.drawImage(this.video_, 0, 0, 640, 480);
        }
        requestAnimationFrame(this.render.bind(this));
      }

      get canDrawVideoToCanvas() {
        return this.video_.readyState === this.video_.HAVE_ENOUGH_DATA;
      }
    }
    const streamGenerator = new StreamGenerator({
      id: '#music_A'
    });
  </script>
</body>

</html>

<style>
  body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }
</style>