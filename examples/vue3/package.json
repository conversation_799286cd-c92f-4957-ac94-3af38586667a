{"name": "trtc-demo", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "type-check": "vue-tsc", "preview": "vite preview"}, "dependencies": {"@vicons/ionicons5": "^0.12.0", "@vueuse/core": "^10.6.1", "idb": "^8.0.1", "pinia": "^2.1.7", "sm-crypto": "0.3.13", "trtc-sdk-v5": "workspace:*", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.9.3", "@vitejs/plugin-vue": "^5.0.3", "http-server": "^14.1.1", "naive-ui": "^2.35.0", "puppeteer": "^23.11.1", "typescript": "^5.2.2", "vconsole": "^3.15.1", "vite": "^5.0.11", "vue-tsc": "^1.8.22"}}