import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import initVConsole from './utils/vconsole';
import {
  create,
  NButton,
  NCard,
  NConfigProvider,
  NInput,
  NLayout,
  NLayoutContent,
  NLayoutFooter,
  NLayoutHeader,
  NSelect,
  NSpace,
  NSwitch,
  NTabPane,
  NTabs,
  NTag,
  NH3,
  NList,
  NListItem,
  NEmpty,
  NModal,
  NIcon,
  NTooltip,
  NDivider,
  NCheckbox,
  NInputGroup,
  NRadio,
  NRadioGroup,
  NSlider,
  NNotificationProvider,
  NMessageProvider,
} from 'naive-ui';

const naive = create({
  components: [
    NButton,
    NCard,
    NConfigProvider,
    NInput,
    NLayout,
    NLayoutContent,
    NLayoutFooter,
    NLayoutHeader,
    NSelect,
    NSpace,
    NSwitch,
    NTabPane,
    NTabs,
    NTag,
    NH3,
    NList,
    NListItem,
    NEmpty,
    NModal,
    NIcon,
    NTooltip,
    NDivider,
    NCheckbox,
    NInputGroup,
    NRadio,
    NRadioGroup,
    NSlider,
    NNotificationProvider,
    NMessageProvider,
  ]
});

const app = createApp(App);
const pinia = createPinia();

app.use(router);
app.use(naive);
app.use(pinia);

app.mount('#app');

// Initialize vConsole for mobile debugging
initVConsole();