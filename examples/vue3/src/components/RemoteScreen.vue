# This is a new file
<template>
  <div class="remote-screen" v-if="remoteUser && remoteUser.hasScreenShare">
    <div class="video-container" ref="videoRef"></div>
    <!-- User ID display -->
    <div class="user-id">{{ userId }} 的屏幕分享</div>
    <div class="downgrade">
      <a @click="roomStore.downgradeVideoDecode(userId, 'sub')">解码降级</a>
    </div>
    <!-- Control buttons -->
    <div class="status-indicators">
      <div
        class="status-indicator"
        :class="{ inactive: !remoteUser.hasScreenShare }"
      >
        <n-icon>
          <desktop-outline v-if="remoteUser.hasScreenShare" />
          <desktop-off-outline v-else />
        </n-icon>
      </div>
      <div class="status-indicator stop-video" @click="handleToggleVideo">
        <n-icon>
          <play-circle-outline v-if="!shouldStartVideo" />
          <stop-circle-outline v-else />
        </n-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useRoomStore } from "../stores/room";
import { NIcon } from "naive-ui";
import {
  DesktopOutline,
  StopCircleOutline,
  PlayCircleOutline,
} from "@vicons/ionicons5";
import TRTC, { type TRTCStreamType } from "trtc-sdk-v5";

const props = defineProps<{
  userId: string;
}>();

const roomStore = useRoomStore();
const videoRef = ref<HTMLDivElement>();
const remoteUser = computed(() => roomStore.remoteUsers[props.userId]);

const shouldStartVideo = ref(false);

const streamType: TRTCStreamType = TRTC.TYPE.STREAM_TYPE_SUB;

watch(
  () => remoteUser.value?.hasScreenShare && !!videoRef.value,
  (newVal) => {
    if (newVal) {
      roomStore
        .startRemoteVideo(props.userId, videoRef.value!, streamType)
        .then(() => (shouldStartVideo.value = false));
    }
  },
  { immediate: true }
);

const handleToggleVideo = () => {
  if (shouldStartVideo.value) {
    roomStore
      .startRemoteVideo(props.userId, videoRef.value!, streamType)
      .then(() => (shouldStartVideo.value = false));
  } else {
    roomStore
      .stopRemoteVideo(props.userId, streamType)
      .then(() => (shouldStartVideo.value = true));
  }
};
</script>

<style scoped>
.remote-screen {
  position: relative;
  width: 320px;
  height: 240px;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-container {
  width: 100%;
  height: 100%;
}

.user-id {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}

.downgrade {
  position: absolute;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  bottom: 8px;
  left: 8px;
  z-index: 1;
}

.downgrade a {
  color: white;
  text-decoration: none;
}

.status-indicators {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  z-index: 1;
}

.status-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-indicator.inactive {
  color: #dc3545;
}

.status-indicator.stop-video {
  cursor: pointer;
}

.status-indicator.stop-video:hover {
  background: rgba(255, 0, 0, 0.2);
}
</style>
