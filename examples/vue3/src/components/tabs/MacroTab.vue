<template>
  <div class="macro-tab">
    <n-space vertical>
      <n-space>
        <n-button
          :type="isRecording ? 'error' : 'primary'"
          @click="handleRecordClick"
        >
          {{ isRecording ? "停止录制" : "开始录制" }}
        </n-button>
        <n-select
          v-model:value="selectedAutoRunMacro"
          :options="macroOptions"
          placeholder="选择启动时执行的宏"
          style="min-width: 200px"
        />
      </n-space>

      <n-card title="宏列表">
        <n-empty v-if="macroList.length === 0" description="暂无宏" />
        <n-list v-else>
          <n-list-item v-for="macro in macroList" :key="macro.id">
            <n-space justify="space-between" style="width: 100%">
              <n-space vertical align="start" style="max-width: 70%">
                <n-space>
                  <span>{{ macro.name }}</span>
                  <n-tag>{{ macro.actions.length }} 个动作</n-tag>
                </n-space>
                <n-tooltip trigger="hover" placement="bottom">
                  <template #trigger>
                    <div class="macro-actions">
                      {{ formatActions(macro.actions) }}
                    </div>
                  </template>
                  <div class="macro-actions-tooltip">
                    <div v-for="(action, index) in macro.actions" :key="index">
                      {{ formatAction(action) }}
                    </div>
                  </div>
                </n-tooltip>
              </n-space>
              <n-space>
                <n-button @click="executeMacro(macro)">执行</n-button>
                <n-button type="error" @click="deleteMacro(macro.id)">
                  删除
                </n-button>
              </n-space>
            </n-space>
          </n-list-item>
        </n-list>
      </n-card>
    </n-space>

    <n-modal
      v-model:show="showNameInput"
      preset="dialog"
      title="输入宏名称"
      positive-text="确认"
      negative-text="取消"
      @positive-click="handleConfirmMacroName"
      @negative-click="handleCancelMacroName"
    >
      <n-input v-model:value="macroName" placeholder="请输入宏名称" />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { storeToRefs } from "pinia";
import { useMacroStore } from "@/stores/macro";
import { useRoomStore } from "@/stores/room";
import { useMessage } from "naive-ui";
import type { Macro as MacroBase, MacroAction } from "@/stores/macro";

// 扩展 Macro 类型以包含 actions
type Macro = MacroBase & { actions: MacroAction[] };

const message = useMessage();
const macroStore = useMacroStore();
const roomStore = useRoomStore();

const showNameInput = ref(false);
const macroName = ref("");
const selectedAutoRunMacro = ref<string | null>(null);

const {
  isRecording,
  macros,
  currentMacroName: storeMacroName,
} = storeToRefs(macroStore);

// 添加一个计算属性来处理宏列表
const macroList = computed(() => macros.value);

watch(selectedAutoRunMacro, (newValue: string | null) => {
  macroStore.setAutoRunMacro(newValue);
});

// 在组件挂载时初始化
onMounted(async () => {
  await macroStore.initDB();
  await macroStore.loadMacros();
  selectedAutoRunMacro.value = macroStore.autoRunMacro;
});

const macroOptions = computed(() => [
  { label: "不执行任何宏", value: null },
  ...macroList.value.map((macro) => ({
    label: macro.name,
    value: macro.id,
  })),
]);

function handleRecordClick() {
  if (isRecording.value) {
    console.log(
      "Stopping recording, current recording:",
      macroStore.currentRecording,
      "store name:",
      storeMacroName.value
    );
    if (macroStore.currentRecording.length > 0) {
      if (!storeMacroName.value) {
        console.error("Macro name is empty when stopping recording");
        message.error("宏名称丢失，请重新录制");
        macroStore.stopRecording();
        isRecording.value = false;
        storeMacroName.value = "";
        return;
      }
      finishRecording();
    } else {
      console.log("No recorded actions");
      message.warning("没有录制到任何操作");
      macroStore.stopRecording();
      isRecording.value = false;
      storeMacroName.value = "";
    }
  } else {
    console.log("Starting recording");
    showNameInput.value = true;
  }
}

async function handleConfirmMacroName() {
  if (!macroName.value) {
    message.error("请输入宏名称");
    return false;
  }

  const name = macroName.value;
  macroName.value = "";
  showNameInput.value = false;

  // 设置名称
  storeMacroName.value = name;

  // 确认名称已经设置
  console.log(
    "Confirmed macro name before recording:",
    "store:",
    storeMacroName.value
  );

  // 开始录制
  macroStore.startRecording();
  message.success(`开始录制宏"${name}"`);
}

async function finishRecording() {
  console.log("Finishing recording with name:", storeMacroName.value);
  try {
    // 先停止录制
    macroStore.stopRecording();
    isRecording.value = false;

    // 然后创建宏
    await createMacro();

    // 等待宏列表更新
    await macroStore.loadMacros();

    message.success(`宏"${storeMacroName.value}"创建成功`);

    // 最后清理状态
    storeMacroName.value = "";
  } catch (error) {
    console.error("Failed to finish recording:", error);
    message.error("宏创建失败");
    // 如果失败，也需要清理状态
    macroStore.stopRecording();
    isRecording.value = false;
    storeMacroName.value = "";
  }
}

async function createMacro() {
  console.log("Creating macro:", {
    name: storeMacroName.value,
    actions: macroStore.currentRecording,
  });

  if (!storeMacroName.value) {
    throw new Error("宏名称不能为空");
  }

  try {
    const name = storeMacroName.value;
    await macroStore.createMacro(name, macroStore.currentRecording);
  } catch (error) {
    console.error("Failed to create macro:", error);
    message.error("宏创建失败：" + (error as Error).message);
    throw error;
  }
}

function handleCancelMacroName() {
  macroName.value = "";
  showNameInput.value = false;
  if (isRecording.value) {
    macroStore.stopRecording();
    isRecording.value = false;
    storeMacroName.value = "";
  }
}

async function deleteMacro(id: string) {
  await macroStore.deleteMacro(id);
  message.success("宏删除成功");
}

async function executeMacro(macro: Macro) {
  try {
    const trtc = roomStore.createClient();
    if (!trtc) {
      message.error("TRTC 实例未初始化");
      return;
    }

    // 记录需要更新的状态
    let hasEnterRoom = false;
    let hasStartLocalAudio = false;
    let hasStartLocalVideo = false;
    let hasStopLocalAudio = false;
    let hasStopLocalVideo = false;
    let hasExitRoom = false;

    for (const action of macro.actions) {
      const method = action.method;
      if (typeof (trtc as any)[method] === "function") {
        await ((trtc as any)[method] as Function)(...action.args);

        // 检查特定的操作并记录状态变更
        switch (method) {
          case "enterRoom":
            hasEnterRoom = true;
            break;
          case "exitRoom":
            hasExitRoom = true;
            break;
          case "startLocalAudio":
            hasStartLocalAudio = true;
            break;
          case "startLocalVideo":
            hasStartLocalVideo = true;
            break;
          case "stopLocalAudio":
            hasStopLocalAudio = true;
            break;
          case "stopLocalVideo":
            hasStopLocalVideo = true;
            break;
        }
      }
    }

    // 更新状态
    if (hasEnterRoom) {
      roomStore.setJoined(true);
    }
    if (hasExitRoom) {
      roomStore.setJoined(false);
    }
    if (hasStartLocalAudio || hasStopLocalAudio) {
      roomStore.toggleLocalAudio(hasStopLocalAudio);
    }
    if (hasStartLocalVideo || hasStopLocalVideo) {
      roomStore.toggleLocalVideo(hasStopLocalVideo);
    }

    message.success("宏执行成功");
  } catch (error) {
    message.error("宏执行失败：" + (error as Error).message);
  }
}

// 格式化单个动作
function formatAction(action: MacroAction): string {
  const argsStr = action.args
    .map((arg) => {
      if (typeof arg === "object" && arg !== null) {
        return JSON.stringify(arg);
      }
      return String(arg);
    })
    .join(", ");
  return `${action.method}(${argsStr})`;
}

// 格式化动作列表为简短显示
function formatActions(actions: MacroAction[]): string {
  const actionsStr = actions.map(formatAction).join(" → ");
  if (actionsStr.length > 50) {
    return actionsStr.slice(0, 47) + "...";
  }
  return actionsStr;
}
</script>

<style scoped>
.macro-tab {
  padding: 16px;
}

.macro-actions {
  font-size: 12px;
  color: var(--n-text-color-2);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 4px 0;
}

.macro-actions-tooltip {
  max-width: 400px;
  font-size: 12px;
  line-height: 1.6;
}

.macro-actions-tooltip > div {
  padding: 2px 0;
}

.macro-actions-tooltip > div:not(:last-child) {
  border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
}
</style>
