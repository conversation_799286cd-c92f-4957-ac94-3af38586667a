<template>
  <div class="ribbon-group">
    <div class="ribbon-group-content no-gap">
      <div class="control-buttons">
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button
              :type="mediaStore.videoPreview ? 'primary' : 'default'"
              @click="mediaStore.toggleVideoPreview"
              class="control-button"
            >
              <template #icon>
                <n-icon><VideocamOutline /></n-icon>
              </template>
            </n-button>
          </template>
          视频预览
        </n-tooltip>
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button
              @click="toggleVideoPublish"
              class="control-button"
              :type="videoPublished ? 'primary' : 'default'"
            >
              <template #icon>
                <n-icon><ShareSocialOutline /></n-icon>
              </template>
            </n-button>
          </template>
          {{ videoPublished ? "停止发布" : "发布视频" }}
        </n-tooltip>
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button tertiary @click="showVideoConfig" class="control-button">
              <template #icon>
                <n-icon><SettingsOutline /></n-icon>
              </template>
            </n-button>
          </template>
          视频设置
        </n-tooltip>
      </div>
    </div>
  </div>

  <!-- Video configuration modal -->
  <n-modal
    v-model:show="showVideoModal"
    preset="dialog"
    title="视频设置"
    class="video-config-modal"
  >
    <n-card size="small" class="config-card">
      <n-space vertical size="large">
        <!-- 基础设置 -->
        <div class="config-section">
          <div class="section-title">基础设置</div>
          <n-form
            :model="advancedVideoConfig"
            label-placement="left"
            label-width="80"
            size="small"
          >
            <n-form-item label="摄像头">
              <n-select
                v-model:value="selectedCamera"
                :options="cameraOptions"
                placeholder="选择摄像头"
              />
            </n-form-item>
            <n-form-item label="视频配置">
              <n-select
                v-model:value="videoProfile"
                :options="videoProfileOptions"
                placeholder="选择视频配置"
              />
            </n-form-item>
          </n-form>
        </div>

        <!-- 高级设置 -->
        <div class="config-section">
          <div class="section-title">高级设置</div>
          <n-form
            :model="advancedVideoConfig"
            label-placement="left"
            size="small"
          >
            <n-space vertical :size="12">
              <n-space :size="24">
                <n-checkbox v-model:checked="advancedVideoConfig.smallStream">
                  推小流
                </n-checkbox>
                <n-checkbox v-model:checked="advancedVideoConfig.vp8Encode">
                  VP8 编码
                </n-checkbox>
                <n-checkbox
                  v-model:checked="advancedVideoConfig.enableHWEncode"
                >
                  开启硬编
                </n-checkbox>
              </n-space>
              <n-form-item label="镜像设置" label-width="80">
                <n-select
                  v-model:value="advancedVideoConfig.mirror"
                  :options="mirrorOptions"
                  placeholder="选择镜像模式"
                />
              </n-form-item>
            </n-space>
          </n-form>
        </div>

        <!-- 下行配置 -->
        <div class="config-section">
          <div class="section-title">下行配置</div>
          <n-form
            :model="downstreamConfig"
            label-placement="left"
            label-width="80"
            size="small"
          >
            <n-space vertical :size="12">
              <!-- <n-form-item label="解码器">
                <n-select
                  v-model:value="downstreamConfig.videoDecoder"
                  :options="videoDecoderOptions"
                  placeholder="选择视频解码器"
                />
              </n-form-item>
              <n-form-item label="渲染方式">
                <n-select
                  v-model:value="downstreamConfig.videoRender"
                  :options="videoRenderOptions"
                  placeholder="选择渲染方式"
                />
              </n-form-item> -->
              <n-space :size="24">
                <n-checkbox v-model:checked="downstreamConfig.smallStream">
                  开启小流
                </n-checkbox>
                <n-checkbox v-model:checked="downstreamConfig.remoteMirror">
                  远端镜像
                </n-checkbox>
              </n-space>
            </n-space>
          </n-form>
        </div>
      </n-space>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import {
  NSpace,
  NButton,
  NSelect,
  NIcon,
  NTooltip,
  NCheckbox,
  NModal,
  NFormItem,
  NDivider,
  NCard,
  NForm,
} from "naive-ui";
import {
  VideocamOutline,
  ShareSocialOutline,
  SettingsOutline,
} from "@vicons/ionicons5";
import { useMediaStore } from "../../stores/media";
import { useSettingsStore } from "../../stores/settings";
import { storeToRefs } from "pinia";
import { ref } from "vue";

const mediaStore = useMediaStore();
// const settingsStore = useSettingsStore();

// 从 mediaStore 获取状态
const {
  selectedCamera,
  videoProfile,
  videoPublished,
  cameraOptions,
  videoProfileOptions,
  advancedVideoConfig,
  downstreamConfig,
} = storeToRefs(mediaStore);

// 从 settingsStore 获取状态
// const { videoDecoderOptions, videoRenderOptions } = storeToRefs(settingsStore);

const mirrorOptions = [
  { label: "关闭", value: "false" },
  { label: "开启", value: "true" },
  { label: "仅预览", value: "view" },
  { label: "仅推流", value: "publish" },
  { label: "预览和推流", value: "both" },
];

// Modal control
const showVideoModal = ref(false);
const showVideoConfig = () => {
  showVideoModal.value = true;
};

const toggleVideoPublish = () => {
  mediaStore.toggleVideoPublish();
};
</script>

<style scoped>
.ribbon-group {
  max-width: 250px;
  background-color: var(--n-color);
  position: relative;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}

.ribbon-group-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ribbon-group-content.no-gap {
  gap: 0;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.control-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.control-button :deep(.n-button__icon) {
  font-size: 20px;
  margin: 0;
}

.video-config-modal :deep(.n-card) {
  max-width: 500px;
  margin: -16px;
}

.config-section {
  background-color: var(--n-card-color);
  border-radius: 3px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--n-title-text-color);
  margin-bottom: 16px;
}

.config-card {
  background-color: var(--n-color);
}

:deep(.n-form-item) {
  margin-bottom: 0;
}

:deep(.n-form-item-label) {
  font-size: 13px;
}

:deep(.n-select) {
  width: 100%;
}
</style>
