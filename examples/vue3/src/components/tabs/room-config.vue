<template>
  <div class="ribbon-group">
    <div class="ribbon-group-content no-gap">
      <n-input-group>
        <n-input
          v-model:value="userName"
          placeholder="用户名"
          style="width: 150px"
        >
          <template #suffix>
            <n-button
              text
              class="control-button"
              size="tiny"
              @click="copyToClipboard(userName)"
            >
              <template #icon>
                <n-icon><CopyOutline /></n-icon>
              </template>
            </n-button>
          </template>
        </n-input>
        <n-input
          v-model:value="roomId"
          placeholder="房间号"
          style="width: 120px"
        >
          <template #suffix>
            <n-button
              text
              tertiary
              class="control-button"
              size="tiny"
              @click="copyToClipboard(roomId)"
            >
              <template #icon>
                <n-icon><CopyOutline /></n-icon>
              </template>
            </n-button>
          </template>
        </n-input>
        <n-button
          :type="inRoom ? 'primary' : 'default'"
          :disabled="!roomId || !userName"
          @click="inRoom ? leaveRoom() : joinRoom()"
          class="control-button"
        >
          <template #icon>
            <n-icon><LogInOutline /></n-icon>
          </template>
        </n-button>
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button
              :disabled="!roomId"
              @click="openInNewWindow"
              class="control-button"
            >
              <template #icon>
                <n-icon><OpenOutline /></n-icon>
              </template>
            </n-button>
          </template>
          在新窗口打开
        </n-tooltip>
        <n-button
          tertiary
          class="control-button"
          @click="showConfigDialog = true"
        >
          <template #icon>
            <n-icon><SettingsOutline /></n-icon>
          </template>
        </n-button>
      </n-input-group>
    </div>
  </div>

  <!-- 配置对话框 -->
  <n-modal v-model:show="showConfigDialog" preset="dialog" title="高级配置">
    <n-space vertical size="large">
      <!-- 进房配置 -->
      <div class="config-section">
        <div class="section-title">进房配置</div>
        <n-space class="config-options">
          <n-switch v-model:value="isRTCMode">
            <template #checked>通话</template>
            <template #unchecked>直播</template>
          </n-switch>
          <n-switch v-model:value="isAnchor">
            <template #checked>主播</template>
            <template #unchecked>观众</template>
          </n-switch>
          <n-space>
            <n-checkbox v-model:checked="autoPlayAudio"
              >自动播放音频</n-checkbox
            >
            <n-checkbox v-model:checked="autoPlayVideo"
              >自动播放视频</n-checkbox
            >
          </n-space>
        </n-space>
      </div>

      <!-- Proxy Server -->
      <div class="config-section">
        <div class="section-title">代理配置</div>
        <n-space vertical>
          <n-select
            v-model:value="proxyStore.proxyServer"
            :options="proxyOptions"
            filterable
            tag
            clearable
            placeholder="代理服务器地址"
          >
            <template #action>
              <n-button
                text
                type="error"
                @click="proxyStore.clearHistory('proxy')"
              >
                清空历史
              </n-button>
            </template>
          </n-select>

          <!-- Unified Proxy -->
          <n-select
            v-model:value="proxyStore.unifiedProxy"
            :options="unifiedProxyOptions"
            filterable
            tag
            clearable
            placeholder="统一代理地址"
          >
            <template #action>
              <n-button
                text
                type="error"
                @click="proxyStore.clearHistory('unified')"
              >
                清空历史
              </n-button>
            </template>
          </n-select>

          <!-- TURN Server -->
          <n-select
            v-model:value="proxyStore.turnServer"
            :options="turnServerOptions"
            filterable
            tag
            clearable
            placeholder="TURN 服务器地址"
          >
            <template #action>
              <n-button
                text
                type="error"
                @click="proxyStore.clearHistory('turn')"
              >
                清空历史
              </n-button>
            </template>
          </n-select>

          <!-- TURN Credentials -->
          <n-input
            v-if="proxyStore.turnServer"
            v-model:value="proxyStore.turnUsername"
            placeholder="TURN 用户名"
          />
          <n-input
            v-if="proxyStore.turnServer"
            v-model:value="proxyStore.turnCredential"
            type="password"
            placeholder="TURN 密码"
          />

          <!-- Force TURN -->
          <n-checkbox
            v-model:checked="proxyStore.forceTurn"
            v-if="proxyStore.turnServer"
          >
            强制使用 TURN
          </n-checkbox>
        </n-space>
      </div>
    </n-space>
  </n-modal>
</template>

<script setup lang="ts">
import {
  NInput,
  NButton,
  NIcon,
  NInputGroup,
  NTooltip,
  useNotification,
  NModal,
  NSpace,
  NSwitch,
  NSelect,
  NCheckbox,
} from "naive-ui";
import {
  LogInOutline,
  OpenOutline,
  SettingsOutline,
  CopyOutline,
} from "@vicons/ionicons5";
import { useRoomStore } from "../../stores/room";
import { useProxyStore } from "../../stores/proxy";
import { storeToRefs } from "pinia";
import { ref, computed, onMounted } from "vue";

const notification = useNotification();
const roomStore = useRoomStore();
const proxyStore = useProxyStore();

// 从 roomStore 获取状态
const {
  roomId,
  userName,
  inRoom,
  isRTCMode,
  isAnchor,
  autoPlayAudio,
  autoPlayVideo,
} = storeToRefs(roomStore);

// 配置对话框
const showConfigDialog = ref(false);

// Proxy options
const proxyOptions = computed(() => {
  return proxyStore.proxyHistory.map((value) => ({
    label: value,
    value,
  }));
});

const unifiedProxyOptions = computed(() => {
  return proxyStore.unifiedProxyHistory.map((value) => ({
    label: value,
    value,
  }));
});

const turnServerOptions = computed(() => {
  return proxyStore.turnServerHistory.map((value) => ({
    label: value,
    value,
  }));
});

// Load saved proxy settings
onMounted(async () => {
  await proxyStore.loadSettings();
});

// Room methods
const joinRoom = async () => {
  if (roomId.value && userName.value) {
    try {
      await roomStore.joinRoom();
      notification.success({
        title: "进房成功",
        content: `房间号: ${roomId.value}, 用户名: ${userName.value}`,
        duration: 3000,
      });
    } catch (error: any) {
      notification.error({
        title: "进房失败",
        content: error.message,
        duration: 3000,
      });
    }
  }
};

const leaveRoom = () => {
  roomStore.leaveRoom();
  notification.info({
    title: "退出房间",
    content: "已退出房间",
    duration: 3000,
  });
};

const openInNewWindow = () => {
  const url = new URL(window.location.href);
  url.searchParams.set("roomId", roomId.value);
  window.open(url.toString(), "_blank");
};

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    notification.success({
      title: "复制成功",
      duration: 2000,
    });
  } catch (err) {
    notification.error({
      title: "复制失败",
      duration: 2000,
    });
  }
};
</script>

<style scoped>
.ribbon-group {
  background-color: var(--n-color);
  position: relative;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}

.ribbon-group-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ribbon-group-content.no-gap {
  gap: 0;
}

.control-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.control-button :deep(.n-button__icon) {
  font-size: 20px;
  margin: 0;
}

.config-section {
  padding: 16px;
  background-color: var(--n-color);
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  color: var(--n-text-color-1);
}

.config-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
