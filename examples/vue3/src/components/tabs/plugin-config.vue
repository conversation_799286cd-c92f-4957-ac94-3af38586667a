<template>
  <div class="plugin-config">
    <n-button quaternary circle size="large" @click="showModal = true">
      <template #icon>
        <n-icon size="24">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <path fill="currentColor"
              d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7s2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z" />
          </svg>
        </n-icon>
      </template>
    </n-button>

    <n-modal v-model:show="showModal" preset="dialog" title="插件配置" style="width: 600px">
      <n-tabs type="line" animated>
        <n-tab-pane name="basic" tab="视频效果">
          <div class="tab-content"><n-space >
            <n-space vertical>
              <!-- 美颜设置 -->
              <div class="feature-group">
                <n-checkbox v-model:checked="pluginFeatures.beauty">美颜</n-checkbox>
                <div v-if="pluginFeatures.beauty" class="beauty-sliders">
                  <div class="slider-item">
                    <span>美颜程度</span>
                    <n-slider v-model:value="beautyValues.beauty" :min="0" :max="1" :step="0.1" />
                  </div>
                  <div class="slider-item">
                    <span>明亮程度</span>
                    <n-slider v-model:value="beautyValues.brightness" :min="0" :max="1" :step="0.1" />
                  </div>
                  <div class="slider-item">
                    <span>红润程度</span>
                    <n-slider v-model:value="beautyValues.ruddy" :min="0" :max="1" :step="0.1" />
                  </div>
                </div>
              </div>

              <!-- 高级美颜 -->
              <n-checkbox v-model:checked="pluginFeatures.advancedBeauty">高级美颜</n-checkbox>

              <!-- 虚拟背景 -->
              <div class="feature-group">
                <span>虚拟背景</span>
                <n-radio-group v-model:value="virtualBackground" name="virtualBackground">
                  <n-space>
                    <n-radio value="normal">正常</n-radio>
                    <n-radio value="blur">虚化</n-radio>
                    <n-radio value="image">图片</n-radio>
                  </n-space>
                </n-radio-group>
                <!-- 虚化程度滑块，仅在选择虚化时显示 -->
                <div v-if="virtualBackground === 'blur'" class="blur-slider">
                  <div class="slider-item">
                    <span>虚化程度</span>
                    <n-slider v-model:value="blurLevel" :min="1" :max="10" :step="1" />
                  </div>
                </div>
              </div>

              <!-- 水印设置 -->
              <div class="feature-group">
                <n-space align="center">
                  <n-checkbox v-model:checked="pluginFeatures.watermark">水印</n-checkbox>
                  <n-button size="small" @click="handleUpdateWatermark">更新水印</n-button>
                </n-space>
              </div>
            </n-space>
            <image-container style="width: 500px; height: 500px; display: none" src="./trtc-watermark-test.png"/>
          </n-space>
          </div>
        </n-tab-pane>
        <n-tab-pane name="encryption" tab="加密模块">
          <div class="tab-content">
            <div class="ribbon-group">
              <div class="ribbon-group-content">
                <n-space vertical>
                  <div class="encryption-header">
                    <span>开启</span>
                    <n-switch v-model:value="encryptionConfig.enabled" />
                  </div>
                  <n-select v-model:value="encryptionConfig.algorithm" :options="algorithmOptions"
                    placeholder="选择加密算法" />
                  <n-input v-model:value="encryptionConfig.key" placeholder="输入加密密钥" />
                  <n-input v-model:value="encryptionConfig.salt" placeholder="输入加密盐值" />
                </n-space>
              </div>
            </div>
          </div>
        </n-tab-pane>
      </n-tabs>
    </n-modal>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { 
  NButton, 
  NModal, 
  NTabs, 
  NTabPane, 
  NIcon, 
  NSpace, 
  NCheckbox, 
  NSelect, 
  NInput, 
  NSwitch,
  NSlider,
  NRadioGroup,
  NRadio,
  useMessage
} from 'naive-ui'
import { useRoomStore } from "@/stores/room"
import { useSettingsStore } from "@/stores/settings"
import {storeToRefs} from "pinia"
import ImageContainer from "../ImageContainer.vue"

import TRTC from "trtc-sdk-v5"
const settingsStore = useSettingsStore()
const { pluginFeatures, virtualBackground ,blurLevel, beautyValues } = storeToRefs(settingsStore)

const message = useMessage()
const showModal = ref(false)


const roomStore = useRoomStore();
const CUSTOM = "custom";

// 加密配置
const encryptionConfig = ref({
  enabled: false,
  algorithm: "AES-GCM",
  key: "",
  salt: "",
});

// 加密算法选项
const algorithmOptions = [
  {
    label: "AES-GCM",
    value: "AES-GCM",
  },
  {
    label: "SM4-CBC",
    value: "SM4-CBC",
  },
  {
    label: CUSTOM,
    value: CUSTOM,
  },
];

// 设置美颜效果
const setBeautyEffect = async () => {
  if (!pluginFeatures.value.beauty) return;
  try {
      await roomStore.updatePlugin('BasicBeauty',beautyValues.value);
  } catch (error) {
    console.error('设置美颜效果失败:', error);
    message.error('设置美颜效果失败');
  }
};


// 更新水印处理函数
const handleUpdateWatermark = () => {
  try {
    roomStore.updatePlugin('Watermark', {
      imageUrl: './test.png',
    });
  } catch (error) {
    console.error('更新水印失败:', error);
    message.error('更新水印失败');
  }
}

// 设置虚化背景
const setBlurBackground = async (level: number) => {
  try {
    const auth = await roomStore.genTestUserSig(roomStore.userName)
    await roomStore.startPlugin('VirtualBackground', {
      ...auth,
      type: 'blur',
      blurLevel: level
    })
    message.success('虚化背景设置成功')
  } catch (error) {
    console.error('设置虚化背景失败:', error)
    message.error('设置虚化背景失败')
  }
}

// 关闭虚拟背景
const disableVirtualBackground = async () => {
  try {
    await roomStore.stopPlugin('VirtualBackground')
  } catch (error) {
    console.error('关闭虚拟背景失败:', error)
  }
}

// 从 localStorage 加载配置
onMounted(() => {
  const savedConfig = localStorage.getItem("encryptionConfig");
  if (savedConfig) {
    encryptionConfig.value = JSON.parse(savedConfig);
  }
});

// 监听加密开关状态
watch(
  () => encryptionConfig.value.enabled,
  (newVal) => {
    if (newVal) {
      roomStore.startPlugin(
        "CustomEncryption",
        encryptionConfig.value.algorithm === CUSTOM
          ? {
              mode: TRTC.frameWorkType === 37 ? "udt3" : "webrtc",
              video: true,
              customCryptors: {
                encryptor: (data: Uint8Array) => {
                  return data;
                },
                decryptor: (data: Uint8Array) => {
                  return data;
                },
              },
            }
          : {
              mode: TRTC.frameWorkType === 37 ? "udt3" : "webrtc",
              video: true,
              builtinOptions: {
                algorithm: encryptionConfig.value.algorithm,
                secretKey: new TextEncoder().encode(encryptionConfig.value.key),
                salt: new TextEncoder().encode(encryptionConfig.value.salt),
              },
            }
      );
    } else {
      roomStore.stopPlugin("CustomEncryption");
    }
  }
);

// 监听美颜开关变化
watch(
  () => pluginFeatures.value.beauty,
  (newVal) => {
    if (!newVal) {
      roomStore.stopPlugin('BasicBeauty');
    } else {
      roomStore.startPlugin('BasicBeauty', beautyValues);
    }
  }
);

// 监听美颜参数变化
watch(
  beautyValues,
  () => {
    if (pluginFeatures.value.beauty) {
      setBeautyEffect();
    }
  },
  { deep: true }
);

// 监听虚拟背景变化
watch(
  virtualBackground,
  async (newVal, oldVal) => {
    if (oldVal !== 'normal') {
      await disableVirtualBackground()
    }
    if (newVal === 'blur') {
      await setBlurBackground(blurLevel.value)
    } else if (newVal === 'image') {
      const auth = await roomStore.genTestUserSig(roomStore.userName)
      await roomStore.startPlugin('VirtualBackground', {
        ...auth,
        type: 'image',
        src: './vb.jpg'
      })
    }
  }
);

// 监听虚化程度变化
watch(
  blurLevel,
  async (newVal) => {
    if (virtualBackground.value === 'blur') {
      await roomStore.updatePlugin('VirtualBackground',{
        type: 'blur',
        blurLevel: newVal
      })
    }
  }
);
watch(
  ()=> pluginFeatures.value.watermark, (newVal)=> {
   newVal?roomStore.startPlugin('Watermark', {
      imageUrl:'./trtc-watermark-test.png'
  }):roomStore.stopPlugin('Watermark')
  }
)
</script>

<style scoped>
.plugin-config {
  display: inline-block;
}

.tab-content {
  padding: 16px;
  min-height: 200px;
}

.ribbon-group {
  min-width: 200px;
  max-width: 250px;
  background-color: var(--n-color);
  position: relative;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}

.ribbon-group-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.encryption-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.feature-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.beauty-sliders {
  margin-left: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.blur-slider {
  margin-left: 24px;
  margin-top: 8px;
}

.slider-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-item span {
  min-width: 60px;
}
</style>