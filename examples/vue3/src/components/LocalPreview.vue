<template>
  <div class="local-preview">
    <div class="video-container" ref="videoRef"></div>
    <!-- Volume indicator -->
    <div class="volume-indicator" v-if="volume !== undefined">
      音量: {{ Math.round(volume) }}
    </div>
    <!-- User ID display -->
    <div class="user-id">
      {{ userName }}
    </div>

    <!-- Control buttons -->
    <div class="controls">
      <n-button
        circle
        :type="localAudioMuted ? 'warning' : 'primary'"
        @click="toggleAudio"
      >
        <template #icon>
          <n-icon>
            <mic-off-outline v-if="localAudioMuted" />
            <mic-outline v-else />
          </n-icon>
        </template>
      </n-button>

      <n-button
        circle
        :type="localVideoMuted ? 'warning' : 'primary'"
        @click="toggleVideo"
      >
        <template #icon>
          <n-icon>
            <videocam-off-outline v-if="localVideoMuted" />
            <videocam-outline v-else />
          </n-icon>
        </template>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useRoomStore } from "../stores/room";
import { useMediaStore } from "../stores/media";
import { NButton, NIcon } from "naive-ui";
import {
  VideocamOutline,
  VideocamOffOutline,
  MicOutline,
  MicOffOutline,
} from "@vicons/ionicons5";
import { storeToRefs } from "pinia";

const roomStore = useRoomStore();
const { userName } = storeToRefs(roomStore);
const mediaStore = useMediaStore();
const videoRef = ref<HTMLDivElement>();

const volume = computed(() => roomStore.userVolumes[roomStore.userName]);
const localAudioMuted = computed(() => roomStore.localAudioMuted);
const localVideoMuted = computed(() => roomStore.localVideoMuted);
const videoPreview = computed(
  () => mediaStore.videoPreview && !!videoRef.value
);
const audioEnabled = computed(() => mediaStore.audioEnabled);

watch(
  videoPreview,
  (newVal) => {
    if (newVal) {
      roomStore.startLocalVideo(videoRef.value!);
    } else {
      roomStore.stopLocalVideo();
    }
  },
  { immediate: true }
);

watch(
  audioEnabled,
  async (newVal) => {
    if (newVal) {
      roomStore.startLocalAudio();
    } else {
      roomStore.stopLocalAudio();
    }
  },
  { immediate: true }
);

const toggleAudio = () => {
  roomStore.toggleLocalAudio(!localAudioMuted.value);
};

const toggleVideo = () => {
  roomStore.toggleLocalVideo(!localVideoMuted.value);
};
</script>

<style scoped>
.local-preview {
  position: relative;
  width: 320px;
  height: 240px;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-container {
  width: 100%;
  height: 100%;
}

.volume-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}

.user-id {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}

.controls {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  z-index: 1;
}

.controls .n-button {
  background: rgba(0, 0, 0, 0.6);
}
</style>
