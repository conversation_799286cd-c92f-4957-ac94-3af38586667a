<template>
  <div class="screen-preview">
    <div class="preview-header">
      <span>屏幕分享预览</span>
    </div>
    <div class="preview-container" ref="previewRef"></div>
    <div class="controls">
      <n-button
        circle
        :type="screenPublished ? 'primary' : 'default'"
        @click="toggleScreenPublish"
      >
        <template #icon>
          <n-icon>
            <ShareSocialOutline />
          </n-icon>
        </template>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, watch, computed } from "vue";
import { useNotification } from "naive-ui";
import { useMediaStore } from "../stores/media";
import { storeToRefs } from "pinia";
import { useRoomStore } from "../stores/room";
import { NButton, NIcon } from "naive-ui";
import { ShareSocialOutline } from "@vicons/ionicons5";

const roomStore = useRoomStore();
const { userName } = storeToRefs(roomStore);

const mediaStore = useMediaStore();
const notification = useNotification();
const { screenSharing, screenPublished } = storeToRefs(mediaStore);

const previewRef = ref<HTMLDivElement>();

// 使用 computed 来控制屏幕分享的启动
const shouldStartShare = computed(
  () => screenSharing.value && !!previewRef.value
);

watch(
  shouldStartShare,
  async (newVal) => {
    if (newVal && previewRef.value) {
      try {
        await roomStore.startScreenShare(previewRef.value);
        notification.success({
          title: "屏幕分享",
          content: "屏幕分享已开启",
          duration: 3000,
        });
      } catch (error) {
        notification.error({
          title: "屏幕分享失败",
          content: error.message,
          duration: 3000,
        });
        screenSharing.value = false;
      }
    } else if (!newVal) {
      try {
        await roomStore.stopScreenShare();
        notification.info({
          title: "屏幕分享",
          content: "屏幕分享已停止",
          duration: 3000,
        });
      } catch (error) {
        notification.error({
          title: "停止屏幕分享失败",
          content: error.message,
          duration: 3000,
        });
      }
    }
  },
  { immediate: true }
);

onUnmounted(async () => {
  if (screenSharing.value) {
    try {
      await roomStore.stopScreenShare();
    } catch (error) {
      console.error("Failed to stop screen sharing:", error);
    }
  }
});

const toggleScreenPublish = () => {
  mediaStore.toggleScreenPublish();
};
</script>

<style scoped>
.screen-preview {
  width: 320px;
  background-color: var(--n-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: relative;
}

.preview-header {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--n-color);
  border-bottom: 1px solid var(--n-border-color);
}

.preview-container {
  width: 100%;
  height: 180px;
  background-color: #000;
}

.controls {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  z-index: 1;
}

.controls :deep(.n-button) {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  background: rgba(0, 0, 0, 0.6);
}

.controls :deep(.n-button__icon) {
  font-size: 20px;
  margin: 0;
}
</style>
