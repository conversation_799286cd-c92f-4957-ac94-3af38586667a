<template>
  <div class="home">
    <div class="preview-list">
      <LocalPreview v-if="showLocalPreview" />
      <ScreenPreview v-if="mediaStore.screenSharing" />
      <RemoteVideo
        v-for="userId in remoteUserIds"
        :key="userId"
        :userId="userId"
      />
      <RemoteScreenShare
        v-for="userId in remoteUserIds"
        :key="userId"
        :userId="userId"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import LocalPreview from "../components/LocalPreview.vue";
import ScreenPreview from "../components/ScreenPreview.vue";
import RemoteVideo from "../components/RemoteVideo.vue";
import RemoteScreenShare from "../components/RemoteScreen.vue";
import { useMediaStore } from "../stores/media";
import { useRoomStore } from "../stores/room";
import { computed } from "vue";

const mediaStore = useMediaStore();
const roomStore = useRoomStore();

const showLocalPreview = computed(
  () => mediaStore.videoPreview || mediaStore.audioEnabled
);

const remoteUserIds = computed(() => Object.keys(roomStore.remoteUsers));
</script>

<style scoped>
.home {
  height: 100%;
  padding: 16px;
}

.preview-list {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}
</style>
