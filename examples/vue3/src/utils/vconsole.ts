import VConsole from 'vconsole';

// Initialize vConsole only on mobile devices
const initVConsole = () => {
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  if (isMobile) {
    const vConsole = new VConsole();
    console.log('vConsole initialized for mobile debugging');
    return vConsole;
  }
  return null;
};

export default initVConsole;
