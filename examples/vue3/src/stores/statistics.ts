import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { TRTCStatistics } from 'trtc-sdk-v5';

export const useStatisticsStore = defineStore('statistics', () => {
  const stats = ref<TRTCStatistics>({
    rtt: 0,
    downLoss: 0,
    upLoss: 0,
    bytesSent: 0,
    bytesReceived: 0,
    localStatistics: {
      audio: {
        bitrate: 0,
        audioLevel: 0
      },
      video: []
    },
    remoteStatistics: []
  });

  const updateStats = (newStats: TRTCStatistics) => {
    stats.value = newStats;
  };

  return {
    stats,
    updateStats
  };
}); 