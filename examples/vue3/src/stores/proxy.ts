import { defineStore } from 'pinia';
import { ref, watch } from 'vue';

// Create IndexedDB database
const dbName = 'proxyConfigDB';
const storeName = 'proxySettings';
const dbVersion = 1;

async function initDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(dbName, dbVersion);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      if (!db.objectStoreNames.contains(storeName)) {
        db.createObjectStore(storeName);
      }
    };
  });
}

async function getFromDB<T>(key: string): Promise<T | null> {
  const db = await initDB() as IDBDatabase;
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.get(key);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => {
      // Parse stored JSON string, or return null if no value exists
      const value = request.result ? JSON.parse(request.result) as T : null;
      resolve(value);
    };
  });
}

async function saveToDB(key: string, value: any) {
  const db = await initDB() as IDBDatabase;
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    // Convert value to JSON string before storing
    const request = store.put(JSON.stringify(value), key);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
  });
}

export const useProxyStore = defineStore('proxy', () => {
  const proxyServer = ref<string | null>(null);
  const unifiedProxy = ref<string | null>(null);
  const turnServer = ref<string | null>(null);
  const turnUsername = ref('');
  const turnCredential = ref('');
  const forceTurn = ref(false);

  const proxyHistory = ref<string[]>([]);
  const unifiedProxyHistory = ref<string[]>([]);
  const turnServerHistory = ref<string[]>([]);

  // Load saved settings from IndexedDB
  async function loadSettings() {
    try {
      proxyServer.value = (await getFromDB<string>('proxyServer'));
      unifiedProxy.value = (await getFromDB<string>('unifiedProxy'));
      turnServer.value = (await getFromDB<string>('turnServer'));
      turnUsername.value = (await getFromDB<string>('turnUsername')) ?? '';
      turnCredential.value = (await getFromDB<string>('turnCredential')) ?? '';
      forceTurn.value = (await getFromDB<boolean>('forceTurn')) ?? false;
      proxyHistory.value = (await getFromDB<string[]>('proxyHistory')) ?? [];
      unifiedProxyHistory.value = (await getFromDB<string[]>('unifiedProxyHistory')) ?? [];
      turnServerHistory.value = (await getFromDB<string[]>('turnServerHistory')) ?? [];
    } catch (error) {
      console.error('Failed to load proxy settings:', error);
    }
  }

  // Watch for changes and save to IndexedDB
  watch([proxyServer, unifiedProxy, turnServer, turnUsername, turnCredential, forceTurn],
    async ([newProxy, newUnified, newTurn, newUsername, newCredential, newForceTurn]) => {
      try {
        await saveToDB('proxyServer', newProxy);
        await saveToDB('unifiedProxy', newUnified);
        await saveToDB('turnServer', newTurn);
        await saveToDB('turnUsername', newUsername);
        await saveToDB('turnCredential', newCredential);
        await saveToDB('forceTurn', newForceTurn);

        // Update history lists
        if (newProxy && !proxyHistory.value.includes(newProxy)) {
          proxyHistory.value.push(newProxy);
          await saveToDB('proxyHistory', proxyHistory.value);
        }
        if (newUnified && !unifiedProxyHistory.value.includes(newUnified)) {
          unifiedProxyHistory.value.push(newUnified);
          await saveToDB('unifiedProxyHistory', unifiedProxyHistory.value);
        }
        if (newTurn && !turnServerHistory.value.includes(newTurn)) {
          turnServerHistory.value.push(newTurn);
          await saveToDB('turnServerHistory', turnServerHistory.value);
        }
      } catch (error) {
        console.error('Failed to save proxy settings:', error);
      }
    },
    { deep: true }
  );

  // Clear history for a specific type
  async function clearHistory(type: 'proxy' | 'unified' | 'turn') {
    try {
      switch (type) {
        case 'proxy':
          proxyHistory.value = [];
          await saveToDB('proxyHistory', []);
          break;
        case 'unified':
          unifiedProxyHistory.value = [];
          await saveToDB('unifiedProxyHistory', []);
          break;
        case 'turn':
          turnServerHistory.value = [];
          await saveToDB('turnServerHistory', []);
          break;
      }
    } catch (error) {
      console.error('Failed to clear history:', error);
    }
  }

  // Get proxy configuration for TRTC
  function getProxyConfig() {
    const config: any = {
      unifiedProxy: unifiedProxy.value || undefined
    };

    if (proxyServer.value) {
      if (proxyServer.value.startsWith('ws')) {
        config.websocketProxy = proxyServer.value;
      } else {
        config.webtransportProxy = proxyServer.value;
      }
    }

    if (turnServer.value) {
      config.turnServer = {
        url: turnServer.value,
        username: turnUsername.value,
        credential: turnCredential.value,
        credentialType: 'password'
      };

      if (forceTurn.value) {
        config.iceTransportPolicy = 'relay';
      }
    }

    return config;
  }

  return {
    proxyServer,
    unifiedProxy,
    turnServer,
    turnUsername,
    turnCredential,
    forceTurn,
    proxyHistory,
    unifiedProxyHistory,
    turnServerHistory,
    loadSettings,
    clearHistory,
    getProxyConfig
  };
}); 