import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useVersionStore } from './version';
import { useMediaStore } from './media';
import { useStatisticsStore } from './statistics';
import { useMacroStore } from './macro';
import type { NotificationApi } from 'naive-ui';
import TRTC, {
  type LocalVideoConfig,
  type LocalAudioConfig,
  type Scene,
  type UserRole,
  type PluginStartOptionsMap,
  type PluginStopOptionsMap,
  type TRTCStreamType,
  type UpdateScreenShareConfig,
  TRTCType,
} from 'trtc-sdk-v5';
import { useProxyStore } from './proxy';
import { sm4 } from 'sm-crypto';
type TRTCClient = ReturnType<typeof TRTC.create>;
type TRTCMethods = {
  [K in keyof TRTCClient]: TRTCClient[K] extends Function ? K : never;
}[keyof TRTCClient];

const fetchUrl = 'https://service.trtc.qcloud.com/release/UserSigService';
const query = new URLSearchParams(location.search);
const sdkAppId = Number(query.get('sdkAppId')) || 1400188366;
const secretKey = query.get('secretKey');
const latencyLevel = Number(query.get('latencyLevel')) || 0;
const enableAutoPlayDialog = query.get('enableAutoPlayDialog') !== 'false';
const _roomId = query.get('roomId') || `${new Date().getMinutes() * 10 + 66666}`;
export interface RemoteUser {
  hasVideo: boolean;
  hasAudio: boolean;
  hasScreenShare: boolean;
  decoder: string;
  renderer: string;
}
export const useRoomStore = defineStore('room', () => {

  async function genTestUserSig() {
    if (!secretKey) {
      const response = await fetch(fetchUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pwd: '********',
          appid: sdkAppId,
          roomnum: parseInt(roomId.value),
          privMap: 255,
          identifier: userName.value,
          accounttype: 14418,
        })
      });
      const { data } = await response.json();
      return {
        sdkAppId,
        userId: userName.value,
        userSig: data.userSig
      };
    }
    const EXPIRETIME = 604800;
    // @ts-ignore
    const generator = new LibGenerateTestUserSig(sdkAppId, secretKey, EXPIRETIME);
    const userSig = generator.genTestUserSig(userName.value);
    return {
      sdkAppId,
      userId: userName.value,
      userSig
    };
  }
  const versionStore = useVersionStore();
  const mediaStore = useMediaStore();
  const statisticsStore = useStatisticsStore();
  const macroStore = useMacroStore();
  const proxyStore = useProxyStore();
  let notificationInstance: NotificationApi | null = null;

  let trtc: TRTCClient | null = null;
  const roomId = ref(_roomId);
  const userName = ref(`u${Math.random() * ********* >> 0}`);
  const inRoom = ref(false);
  const isRTCMode = ref(true);
  const isAnchor = ref(true);
  const autoPlayAudio = ref(true);
  const autoPlayVideo = ref(true);
  const enableSEI = ref(false);
  const videoEncodeCodec = ref(null);
  const userVolumes = ref<Record<string, number>>({});
  const localAudioMuted = ref(false);
  const localVideoMuted = ref(false);
  const remoteUsers = ref<Record<string, RemoteUser>>({});
  const useVp8 = ref(false);
  // Add a setter for the notification instance
  const setNotificationInstance = (instance: NotificationApi) => {
    notificationInstance = instance;
  };

  // Helper function to safely show notifications
  const showNotification = (options: {
    type: 'info' | 'success' | 'warning' | 'error',
    title: string,
    content: string,
    duration?: number;
  }) => {
    try {
      if (notificationInstance) {
        notificationInstance[options.type]({
          title: options.title,
          content: options.content,
          duration: options.duration || 3000
        });
      }
    } catch (error) {
      console.warn('Failed to show notification:', error);
    }
  };
  function downgradeVideoDecode(userId: string, streamType: TRTCStreamType) {
    const track = streamType === 'main' ? trtc?._room.remotePublishedUserMap.get(userId)?.remoteVideoTrack : trtc?._room.remotePublishedUserMap.get(userId)?.remoteAuxiliaryTrack;
    console.warn('updatePlugin downgrade streamType', streamType);
    trtc?.updatePlugin('TRTCVideoDecoder', { type: 'mock', track }).catch(() => {
      track?.emit('decode-failed');
    });
  }
  async function createClient() {
    if (trtc) {
      await trtc.exitRoom().catch();
      trtc.destroy();
    }
    const version = versionStore.currentVersion;
    const assetsPath = () => {
      switch (version) {
        case 'dev':
          return '/static/';
        case 'build':
          return './assets/';
        case 'latest':
          return 'https://web.sdk.cloudcachetci.com/trtc/webrtc/v5/dist/assets/';
        default:
          return `https://unpkg.com/trtc-sdk-v5@${version}/assets/`;
      }
    };
    const macroStore = useMacroStore();
    trtc = TRTC.create({
      plugins: Object.values(versionStore.pluginMap).filter(plugin => plugin.loaded && plugin.path !== 'trtc.js').map(plugin => plugin.clazz),
      assetsPath: assetsPath(),
    });
    window.trtc = trtc;
    const TRTCEvent = TRTC.EVENT;
    // Create a proxy to intercept and record method calls
    const rtcProxy = new Proxy(trtc, {
      get(target, prop, receiver) {
        const value = Reflect.get(target, prop, receiver);
        if (typeof value === 'function' && !prop.toString().startsWith('_') && prop.toString() !== 'on') {
          return function (...args: any[]) {
            // Record the method call if macro recording is active
            if (macroStore.isRecording) {
              macroStore.recordAction(prop.toString(), args);
            }
            return value.apply(target, args);
          };
        }
        return value;
      }
    }) as TRTCClient;

    trtc = rtcProxy;

    // 检查并启动加密插件
    const savedEncryptionConfig = localStorage.getItem('encryptionConfig');
    if (savedEncryptionConfig) {
      const config = JSON.parse(savedEncryptionConfig);
      if (config.enabled) {
        const key = new TextEncoder().encode(config.key);
        const iv = new TextEncoder().encode(config.salt);
        (trtc.startPlugin as any)('CustomEncryption', config.algorithm === 'custom' ? {
          mode: 'udt3',
          video: true,
          audio: true,
          customCryptors: {
            encryptor: (data: Uint8Array) => {
              const result = sm4.encrypt(data, config.key, { mode: 'cbc', iv: config.key, output: 'array' });
              return new Uint8Array(result);
            },
            decryptor: (data: Uint8Array) => {
              const result = sm4.decrypt(data, config.key, { mode: 'cbc', iv: config.key, output: 'array' });
              return new Uint8Array(result);
            },
          },
        } : {
          mode: 'udt3',
          video: true,
          audio: true,
          builtinOptions: {
            algorithm: config.algorithm,
            secretKey: key,
            salt: iv,
          },
        }).catch((error: unknown) => {
          console.error('Failed to start encryption plugin on client creation:', error);
        });
      }
    }

    // Add statistics event listener
    trtc.on(TRTCEvent.STATISTICS, (stats) => {
      statisticsStore.updateStats(stats);
    });

    // Connection state changes
    trtc.on(TRTCEvent.CONNECTION_STATE_CHANGED, (event) => {
      showNotification({
        type: 'info',
        title: '连接状态变更',
        content: `连接状态: ${event.state}`,
        duration: 3000
      });
    });

    // Network quality
    trtc.on(TRTCEvent.NETWORK_QUALITY, (event) => {
      const qualityMap = {
        0: '未知',
        1: '极好',
        2: '好',
        3: '一般',
        4: '差',
        5: '很差',
        6: '断开'
      };

      if (event.uplinkNetworkQuality >= 4 || event.downlinkNetworkQuality >= 4) {
        showNotification({
          type: 'warning',
          title: '网络质量警告',
          content: `上行: ${qualityMap[event.uplinkNetworkQuality]}, 下行: ${qualityMap[event.downlinkNetworkQuality]}`,
          duration: 5000
        });
      }
    });

    // Error events
    trtc.on(TRTCEvent.ERROR, (error) => {
      showNotification({
        type: 'error',
        title: '发生错误',
        content: `错误码: ${error.code}, 信息: ${error.message}`,
        duration: 8000
      });
    });

    // User joined/left
    trtc.on(TRTCEvent.REMOTE_USER_ENTER, (event) => {
      remoteUsers.value[event.userId] = {
        hasVideo: false,
        hasAudio: false,
        hasScreenShare: false
      };
      showNotification({
        type: 'success',
        title: '用户加入',
        content: `用户 ${event.userId} 加入房间`,
        duration: 3000
      });
    });

    trtc.on(TRTCEvent.REMOTE_USER_EXIT, (event) => {
      delete remoteUsers.value[event.userId];
      showNotification({
        type: 'info',
        title: '用户离开',
        content: `用户 ${event.userId} 离开房间`,
        duration: 3000
      });
    });

    // Device change
    trtc.on(TRTCEvent.DEVICE_CHANGED, (event) => {
      mediaStore.updateDeviceLists();
      let actionText = '';
      if (event.action === 'add') {
        actionText = '接入';
      } else if (event.action === 'remove') {
        actionText = '移除';
      } else if (event.action === 'active') {
        actionText = '激活';
      }

      const deviceType = {
        'audioinput': '麦克风',
        'audiooutput': '扬声器',
        'videoinput': '摄像头'
      }[event.device.kind] || '未知设备';

      const deviceInfo = event.device.label ?
        `${deviceType}「${event.device.label}」` :
        `${deviceType} (ID: ${event.device.deviceId})`;

      showNotification({
        type: 'info',
        title: `设备${actionText}`,
        content: `${deviceInfo}`,
        duration: 3000
      });
    });

    // Audio/Video state changes
    trtc.on(TRTCEvent.VIDEO_PLAY_STATE_CHANGED, (event) => {
      if (event.userId) { // Only show for remote users
        showNotification({
          type: 'info',
          title: '视频状态变更',
          content: `用户 ${event.userId} ${event.state === 'PLAYING' ? '开启' : '关闭'}视频`,
          duration: 3000
        });
      }
    });

    // Remote video availability events
    trtc.on(TRTCEvent.REMOTE_VIDEO_AVAILABLE, (event) => {
      if (remoteUsers.value[event.userId]) {
        if (event.streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
          remoteUsers.value[event.userId].hasVideo = true;
        } else if (event.streamType === TRTC.TYPE.STREAM_TYPE_SUB) {
          remoteUsers.value[event.userId].hasScreenShare = true;
        }
      }
      showNotification({
        type: 'info',
        title: '远端视频可用',
        content: `用户 ${event.userId} 的${event.streamType === TRTC.TYPE.STREAM_TYPE_SUB ? '屏幕分享' : '视频'}可用`,
        duration: 3000
      });
    });

    trtc.on(TRTCEvent.REMOTE_VIDEO_UNAVAILABLE, (event) => {
      if (remoteUsers.value[event.userId]) {
        if (event.streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
          remoteUsers.value[event.userId].hasVideo = false;
        } else if (event.streamType === TRTC.TYPE.STREAM_TYPE_SUB) {
          remoteUsers.value[event.userId].hasScreenShare = false;
        }
      }
      showNotification({
        type: 'info',
        title: '远端视频不可用',
        content: `用户 ${event.userId} 的${event.streamType === TRTC.TYPE.STREAM_TYPE_SUB ? '屏幕分享' : '视频'}不可用`,
        duration: 3000
      });
    });

    // Remote audio availability events
    trtc.on(TRTCEvent.REMOTE_AUDIO_AVAILABLE, (event) => {
      if (remoteUsers.value[event.userId]) {
        remoteUsers.value[event.userId].hasAudio = true;
      }
      showNotification({
        type: 'info',
        title: '远端音频可用',
        content: `用户 ${event.userId} 的音频可用`,
        duration: 3000
      });
    });

    trtc.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, (event) => {
      if (remoteUsers.value[event.userId]) {
        remoteUsers.value[event.userId].hasAudio = false;
      }
      showNotification({
        type: 'info',
        title: '远端音频不可用',
        content: `用户 ${event.userId} 的音频不可用`,
        duration: 3000
      });
    });

    trtc.on(TRTCEvent.AUDIO_PLAY_STATE_CHANGED, (event) => {
      if (event.userId) { // Only show for remote users
        showNotification({
          type: 'info',
          title: '音频状态变更',
          content: `用户 ${event.userId} ${event.state === 'PLAYING' ? '开启' : '关闭'}音频`,
          duration: 3000
        });
      }
    });
    trtc.on(TRTCEvent.SCREEN_SHARE_STOPPED, () => {
      showNotification({
        type: 'info',
        title: '屏幕分享停止',
        content: `用户停止屏幕分享`,
        duration: 3000
      });
      mediaStore.screenSharing = false;
    });
    // Add audio volume event listener
    trtc.on(TRTCEvent.AUDIO_VOLUME, (event) => {
      // Update volumes from the result array
      event.result.forEach(volume => {
        userVolumes.value[volume.userId || userName.value] = volume.volume;
      });
    });
    trtc.enableAudioVolumeEvaluation(1000);
    // @ts-ignore
    trtc.on('video-decode-downgrade-state-changed', (event) => {
      remoteUsers.value[event.userId].decoder = event.type;
      remoteUsers.value[event.userId].renderer = event.renderer;
    });
    return trtc;
  }
  // 方法
  const joinRoom = async () => {
    if (!roomId.value || !userName.value) return;

    try {
      const macroStore = useMacroStore();
      if (!trtc) await createClient();

      // Execute auto-run macro if set
      if (macroStore.autoRunMacro) {
        const macro = macroStore.macros.find(m => m.id === macroStore.autoRunMacro);
        if (macro && trtc) {
          for (const action of macro.actions) {
            const method = action.method as keyof TRTCClient;
            if (method in trtc && typeof trtc[method] === 'function') {
              await (trtc[method] as Function)(...action.args);
            }
          }
        }
      }

      await trtc.enterRoom({
        ...await genTestUserSig(),
        roomId: Number(roomId.value),
        privateMapKey: '255',
        useVp8: mediaStore.advancedVideoConfig.vp8Encode,
        preferHW: mediaStore.advancedVideoConfig.enableHWEncode,
        proxy: proxyStore.getProxyConfig(),
        role: (isAnchor.value ? 'anchor' : 'audience') as UserRole,
        scene: (isRTCMode.value ? 'rtc' : 'live') as Scene,
        autoReceiveAudio: autoPlayAudio.value,
        autoReceiveVideo: autoPlayVideo.value,
        enableAutoPlayDialog: enableAutoPlayDialog ? Boolean(Number(enableAutoPlayDialog)) : true,
        latencyLevel: latencyLevel,
      });
      inRoom.value = true;
    } catch (error) {
      showNotification({
        type: 'error',
        title: '进房失败',
        content: error instanceof Error ? error.message : 'Unknown error',
        duration: 8000
      });
      throw error;
    }
  };

  const leaveRoom = async () => {
    try {
      if (!trtc) throw new Error('TRTC not initialized');
      await trtc.exitRoom();
      inRoom.value = false;
    } catch (error) {
      console.error('Leave room failed:', error);
      throw error;
    }
  };

  const destroyRoom = async () => {
    if (!trtc) return;
    trtc.destroy();
    trtc = null;
    inRoom.value = false;
  };

  const startLocalVideo = async (element: HTMLElement) => {
    try {
      if (!trtc) throw new Error('TRTC not initialized');

      if (mediaStore.selectedCamera === 'music_A') {
        music_A.play();
        await trtc.startLocalVideo({
          view: element,
          publish: mediaStore.videoPublished,
          option: {
            videoTrack: streamGenerator.generateVideoTrack(),
            small: mediaStore.advancedVideoConfig.smallStream,
            mirror: mediaStore.advancedVideoConfig.localMirror,
          }
        });
      } else {
        await trtc.startLocalVideo({
          view: element,
          publish: mediaStore.videoPublished,
          option: {
            cameraId: mediaStore.selectedCamera,
            profile: mediaStore.videoProfile as '480p' | '720p' | '1080p',
            small: mediaStore.advancedVideoConfig.smallStream,
            mirror: mediaStore.advancedVideoConfig.localMirror,
          }
        });
      }
    } catch (error) {
      console.error('Start local video failed:', error);
      throw error;
    }
  };

  const startRemoteVideo = async (userId: string, element: HTMLElement, streamType: TRTCStreamType = TRTC.TYPE.STREAM_TYPE_MAIN) => {
    try {
      if (!trtc) throw new Error('TRTC not initialized');
      await trtc.startRemoteVideo({
        userId,
        view: element,
        streamType,
        option: {
          small: mediaStore.downstreamConfig.smallStream,
          mirror: mediaStore.downstreamConfig.remoteMirror,
        }
      });
    } catch (error) {
      console.error('Start remote video failed:', error);
      throw error;
    }
  };

  const stopRemoteVideo = async (userId: string, streamType: TRTCStreamType = TRTC.TYPE.STREAM_TYPE_MAIN) => {
    try {
      if (!trtc) throw new Error('TRTC not initialized');
      await trtc.stopRemoteVideo({ userId, streamType });
    } catch (error) {
      console.error('Stop remote video failed:', error);
      throw error;
    }
  };

  async function startLocalAudio() {
    try {
      if (!trtc) throw new Error('TRTC not initialized');

      if (mediaStore.selectedMicrophone === 'music_A') {
        music_A.play();
        await trtc.startLocalAudio({
          publish: mediaStore.audioPublished,
          option: {
            audioTrack: streamGenerator.generateAudioTrack(),
          }
        });
      } else {
        await trtc.startLocalAudio({
          publish: mediaStore.audioPublished,
          option: {
            microphoneId: mediaStore.selectedMicrophone,
            profile: mediaStore.audioProfile,
            echoCancellation: mediaStore.aec,
            noiseSuppression: mediaStore.ans,
            autoGainControl: mediaStore.agc
          }
        });
      }
    } catch (error) {
      console.error('Start local audio failed:', error);
      throw error;
    }
  }

  const updateLocalVideo = async (config: LocalVideoConfig) => {
    if (!trtc) throw new Error('TRTC not initialized');
    await trtc.updateLocalVideo(config);
  };

  const updateLocalAudio = async (config: LocalAudioConfig) => {
    if (!trtc) throw new Error('TRTC not initialized');
    await trtc.updateLocalAudio(config);
  };

  const stopLocalAudio = async () => {
    if (!trtc) throw new Error('TRTC not initialized');
    await trtc.stopLocalAudio();
  };

  const stopLocalVideo = async () => {
    if (!trtc) throw new Error('TRTC not initialized');
    await trtc.stopLocalVideo();
  };

  const startScreenShare = async (element?: HTMLElement) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.startScreenShare({
        view: element || undefined,
        publish: true,
        option: {
          profile: mediaStore.screenProfile,
          qosPreference: mediaStore.qosPreference,
          systemAudio: mediaStore.shareSystemAudio
        }
      });
    } catch (error) {
      console.error('Start screen share failed:', error);
      throw error;
    }
  };

  const stopScreenShare = async () => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.stopScreenShare();
    } catch (error) {
      console.error('Stop screen share failed:', error);
      throw error;
    }
  };

  const toggleLocalAudio = async (mute: boolean) => {
    if (!trtc) return;
    try {
      await trtc.updateLocalAudio({ mute });
      localAudioMuted.value = mute;
    } catch (error) {
      console.error('Failed to toggle local audio:', error);
    }
  };

  const toggleLocalVideo = async (mute: boolean) => {
    if (!trtc) return;
    try {
      await trtc.updateLocalVideo({ mute });
      localVideoMuted.value = mute;
    } catch (error) {
      console.error('Failed to toggle local video:', error);
    }
  };

  const updateScreenShare = async (config: UpdateScreenShareConfig) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.updateScreenShare(config);
    } catch (error) {
      console.error('Update screen share failed:', error);
      throw error;
    }
  };

  // Plugin management methods
  const startPlugin = async (pluginName: string, options: unknown) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.startPlugin(pluginName as keyof PluginStartOptionsMap, options as any);
    } catch (error) {
      console.warn(`Failed to start plugin ${pluginName}:`, error);
    }
  };

  const updatePlugin = async (pluginName: string, options: unknown) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.updatePlugin(pluginName as keyof PluginUpdateOptionsMap, options as any);
    } catch (error) {
      console.warn(`Failed to update plugin ${pluginName}:`, error);
    }
  };

  const stopPlugin = async (pluginName: string, options: unknown) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.stopPlugin(pluginName as keyof PluginStopOptionsMap, options as any);
    } catch (error) {
      console.warn(`Failed to stop plugin ${pluginName}:`, error);
    }
  };

  return {
    roomId,
    userName,
    inRoom,
    isRTCMode,
    isAnchor,
    autoPlayAudio,
    autoPlayVideo,
    enableSEI,
    videoEncodeCodec,
    userVolumes,
    localAudioMuted,
    localVideoMuted,
    remoteUsers,
    setNotificationInstance,
    createClient,
    // 方法
    joinRoom,
    leaveRoom,
    destroyRoom,
    startLocalVideo,
    startLocalAudio,
    updateLocalVideo,
    updateLocalAudio,
    toggleLocalAudio,
    toggleLocalVideo,
    stopLocalAudio,
    stopLocalVideo,
    startScreenShare,
    stopScreenShare,
    updateScreenShare,
    // Remote methods
    startRemoteVideo,
    stopRemoteVideo,
    // Plugin methods
    startPlugin,
    updatePlugin,
    stopPlugin,
    downgradeVideoDecode,
    // State methods
    setJoined: (value: boolean) => {
      inRoom.value = value;
    },
    sdkAppId,
    genTestUserSig
  };
});
