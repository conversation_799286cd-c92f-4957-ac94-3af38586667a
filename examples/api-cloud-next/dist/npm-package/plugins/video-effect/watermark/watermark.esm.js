var __defProp=Object.defineProperty,__defNormalProp=(e,t,r)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,__publicField=(e,t,r)=>__defNormalProp(e,"symbol"!=typeof t?t+"":t,r);function startValidateRule(e){return{name:"WatermarkOptions",type:"object",required:!0,allowEmpty:!1,properties:{imageUrl:{required:!0,type:"string"},x:{required:!1,type:"number"},y:{required:!1,type:"number"},size:{required:!1,type:["string","object","number"]}},validate(t,r,a,i){var o;const{RtcError:s,ErrorCode:n,ErrorCodeDictionary:c}=e.errorModule;if(!t)return;const{imageUrl:d}=t,l=d.split("?")[0].split(".").pop();if("jpg"!==l&&"jpeg"!==l||e.log.warn("The image format is not recommended to be jpg/jpeg, because the format does not support transparency."),!(null==(o=e.room.videoManager.cameraTrack)?void 0:o.mediaTrack))throw new s({code:n.INVALID_OPERATION,extraCode:c.INVALID_OPERATION_NEED_VIDEO,fnName:a});if(e.utils.isString(t.size)&&"contain"!==t.size&&"cover"!==t.size)throw new s({code:n.INVALID_PARAMETER,extraCode:c.INVALID_PARAMETER_TYPE,message:"The size parameter must be 'contain' or 'cover'",fnName:a});if(e.utils.isNumber(t.size)&&(t.size<=0||t.size>1))throw new s({code:n.INVALID_PARAMETER,extraCode:c.INVALID_PARAMETER_RANGE,message:"The size parameter must be greater than 0",fnName:a});if(e.utils.isObject(t.size)){if(!t.size.width||!t.size.height)throw new s({code:n.INVALID_PARAMETER,extraCode:c.INVALID_PARAMETER_TYPE,message:"The size parameter must be an object with width and height properties",fnName:a});if(t.size.width<=0||t.size.height<=0)throw new s({code:n.INVALID_PARAMETER,extraCode:c.INVALID_PARAMETER_RANGE,message:"The size parameter must be greater than 0",fnName:a})}}}}function stopValidateRule(e){return{name:"StopWatermarkOptions",required:!1}}var wSeq=0,_Watermark=class e{constructor(e){this.core=e,__publicField(this,"seq"),__publicField(this,"_core"),__publicField(this,"log"),__publicField(this,"startResolve"),__publicField(this,"startReject"),wSeq+=1,this.seq=wSeq,this._core=e,this.log=e.log.createChild({id:`${this.getAlias()}${wSeq}`}),this.log.info("created")}getName(){return e.Name}getAlias(){return"w"}getValidateRule(e){switch(e){case"start":case"update":return startValidateRule(this._core);case"stop":return stopValidateRule(this._core)}}getGroup(){return"w"}async start(e){return this.doStart(e)}async update(e){this.doStart(e)}async stop(){return this._core.room.videoManager.deleteWatermark()}async doStart(e){const{x:t=0,y:r=0}=e,a=await this.processWatermark(e);return this._core.room.videoManager.setWatermark({x:t,y:r,imageUrl:a})}async processWatermark(e){const{imageUrl:t,x:r=0,y:a=0,size:i="cover"}=e,{settings:o}=this._core.room.videoManager.cameraTrack;let s;try{s=await this._core.utils.loadImage(t)}catch(e){const{RtcError:r,ErrorCode:a}=this.core.errorModule;throw new r({code:a.INVALID_PARAMETER,message:`load image failed, url: ${t}`})}const{width:n,height:c}=o,{width:d,height:l}=s;let m=d,u=l;this._core.utils.isObject(i)&&(m=(null==i?void 0:i.width)||m,u=(null==i?void 0:i.height)||u),this._core.utils.isNumber(i)&&(m=d*i,u=l*i);const h=d/l,_=h>n/c;"contain"===i&&(_?(m=n,u=n/h):(m=c*h,u=c)),"cover"===i&&(_?(u=c,m=c*h):(m=n,u=n/h));const p=document.createElement("canvas"),g=p.getContext("2d");return p.width=Math.min(n-r,m),p.height=Math.min(c-a,u),null==g||g.drawImage(s,0,0,m,u),p.toDataURL("image/png")}};__publicField(_Watermark,"Name","Watermark");var Watermark=_Watermark,index_default=Watermark;export{index_default as default};export{Watermark};