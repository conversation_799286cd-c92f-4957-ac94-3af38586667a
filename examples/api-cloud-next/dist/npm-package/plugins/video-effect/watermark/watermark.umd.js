!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Watermark=e()}(this,(function(){"use strict";function t(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function e(e){return function(){var r=this,n=arguments;return new Promise((function(o,i){var a=e.apply(r,n);function c(e){t(a,o,i,c,u,"next",e)}function u(e){t(a,o,i,c,u,"throw",e)}c(void 0)}))}}function r(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,o(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function n(){n=function(){return e};var t,e={},r=Object.prototype,o=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof g?e:g,a=Object.create(o.prototype),c=new R(n||[]);return i(a,"_invoke",{value:N(t,r,c)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var p="suspendedStart",d="suspendedYield",y="executing",v="completed",m={};function g(){}function w(){}function b(){}var E={};f(E,c,(function(){return this}));var x=Object.getPrototypeOf,_=x&&x(x(P([])));_&&_!==r&&o.call(_,c)&&(E=_);var L=b.prototype=g.prototype=Object.create(E);function A(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(n,i,a,c){var u=l(t[n],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&o.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,c)}))}c(u.arg)}var n;i(this,"_invoke",{value:function(t,o){function i(){return new e((function(e,n){r(t,o,e,n)}))}return n=n?n.then(i,i):i()}})}function N(e,r,n){var o=p;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=T(c,n);if(u){if(u===m)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var s=l(e,r,n);if("normal"===s.type){if(o=n.done?v:d,s.arg===m)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=v,n.method="throw",n.arg=s.arg)}}}function T(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,T(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,m;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function R(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function P(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function r(){for(;++n<e.length;)if(o.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return w.prototype=b,i(L,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:w,configurable:!0}),w.displayName=f(b,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,f(t,s,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},A(k.prototype),f(k.prototype,u,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new k(h(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},A(L),f(L,s,"Generator"),f(L,c,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=P,R.prototype={constructor:R,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&o.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(n,o){return c.type="throw",c.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var u=o.call(a,"catchLoc"),s=o.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:P(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),m}},e}function o(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof e?e:e+""}function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}var a=Object.defineProperty,c=function(t,e,r){return function(t,e,r){return e in t?a(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r}(t,"symbol"!==i(e)?e+"":e,r)};function u(t){return{name:"WatermarkOptions",type:"object",required:!0,allowEmpty:!1,properties:{imageUrl:{required:!0,type:"string"},x:{required:!1,type:"number"},y:{required:!1,type:"number"},size:{required:!1,type:["string","object","number"]}},validate:function(e,r,n,o){var i,a=t.errorModule,c=a.RtcError,u=a.ErrorCode,s=a.ErrorCodeDictionary;if(e){var f=e.imageUrl.split("?")[0].split(".").pop();if("jpg"!==f&&"jpeg"!==f||t.log.warn("The image format is not recommended to be jpg/jpeg, because the format does not support transparency."),!(null==(i=t.room.videoManager.cameraTrack)?void 0:i.mediaTrack))throw new c({code:u.INVALID_OPERATION,extraCode:s.INVALID_OPERATION_NEED_VIDEO,fnName:n});if(t.utils.isString(e.size)&&"contain"!==e.size&&"cover"!==e.size)throw new c({code:u.INVALID_PARAMETER,extraCode:s.INVALID_PARAMETER_TYPE,message:"The size parameter must be 'contain' or 'cover'",fnName:n});if(t.utils.isNumber(e.size)&&(e.size<=0||e.size>1))throw new c({code:u.INVALID_PARAMETER,extraCode:s.INVALID_PARAMETER_RANGE,message:"The size parameter must be greater than 0",fnName:n});if(t.utils.isObject(e.size)){if(!e.size.width||!e.size.height)throw new c({code:u.INVALID_PARAMETER,extraCode:s.INVALID_PARAMETER_TYPE,message:"The size parameter must be an object with width and height properties",fnName:n});if(e.size.width<=0||e.size.height<=0)throw new c({code:u.INVALID_PARAMETER,extraCode:s.INVALID_PARAMETER_RANGE,message:"The size parameter must be greater than 0",fnName:n})}}}}}var s=0,f=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.core=e,c(this,"seq"),c(this,"_core"),c(this,"log"),c(this,"startResolve"),c(this,"startReject"),s+=1,this.seq=s,this._core=e,this.log=e.log.createChild({id:"".concat(this.getAlias()).concat(s)}),this.log.info("created")}return r(t,[{key:"getName",value:function(){return t.Name}},{key:"getAlias",value:function(){return"w"}},{key:"getValidateRule",value:function(t){switch(t){case"start":case"update":return u(this._core);case"stop":return this._core,{name:"StopWatermarkOptions",required:!1}}}},{key:"getGroup",value:function(){return"w"}},{key:"start",value:(h=e(n().mark((function t(e){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",this.doStart(e));case 1:case"end":return t.stop()}}),t,this)}))),function(t){return h.apply(this,arguments)})},{key:"update",value:(f=e(n().mark((function t(e){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this.doStart(e);case 1:case"end":return t.stop()}}),t,this)}))),function(t){return f.apply(this,arguments)})},{key:"stop",value:(a=e(n().mark((function t(){return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",this._core.room.videoManager.deleteWatermark());case 1:case"end":return t.stop()}}),t,this)}))),function(){return a.apply(this,arguments)})},{key:"doStart",value:(i=e(n().mark((function t(e){var r,o,i,a,c;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.x,o=void 0===r?0:r,i=e.y,a=void 0===i?0:i,t.next=3,this.processWatermark(e);case 3:return c=t.sent,t.abrupt("return",this._core.room.videoManager.setWatermark({x:o,y:a,imageUrl:c}));case 5:case"end":return t.stop()}}),t,this)}))),function(t){return i.apply(this,arguments)})},{key:"processWatermark",value:(o=e(n().mark((function t(e){var r,o,i,a,c,u,s,f,h,l,p,d,y,v,m,g,w,b,E,x,_,L,A;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.imageUrl,o=e.x,i=void 0===o?0:o,a=e.y,c=void 0===a?0:a,u=e.size,s=void 0===u?"cover":u,f=this._core.room.videoManager.cameraTrack.settings,t.prev=2,t.next=5,this._core.utils.loadImage(r);case 5:h=t.sent,t.next=12;break;case 8:throw t.prev=8,t.t0=t.catch(2),l=this.core.errorModule,p=l.RtcError,d=l.ErrorCode,new p({code:d.INVALID_PARAMETER,message:"load image failed, url: ".concat(r)});case 12:return y=f.width,v=f.height,g=(m=h).width,w=m.height,b=g,E=w,this._core.utils.isObject(s)&&(b=(null==s?void 0:s.width)||b,E=(null==s?void 0:s.height)||E),this._core.utils.isNumber(s)&&(b=g*s,E=w*s),_=(x=g/w)>y/v,"contain"===s&&(_?(b=y,E=y/x):(b=v*x,E=v)),"cover"===s&&(_?(E=v,b=v*x):(b=y,E=y/x)),L=document.createElement("canvas"),A=L.getContext("2d"),L.width=Math.min(y-i,b),L.height=Math.min(v-c,E),null==A||A.drawImage(h,0,0,b,E),t.abrupt("return",L.toDataURL("image/png"));case 28:case"end":return t.stop()}}),t,this,[[2,8]])}))),function(t){return o.apply(this,arguments)})}]);var o,i,a,f,h}();return c(f,"Name","Watermark"),f}));
