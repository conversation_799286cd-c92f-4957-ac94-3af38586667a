/* global $ TRTC */
/* eslint-disable require-jsdoc */

const fetchUrl = 'https://service.trtc.qcloud.com/release/UserSigService';
const defaultSdkAppId = **********; // unified sdkAppId for Demos on all platforms.
const accountType = 14418;
const audioSource = null;
// preset before starting RTC
class Presetting {
  init() {
    // populate userId/roomId/privMap
    $('#userId').val(`user_${parseInt(Math.random() * *********)}`);
    $('#roomId').val(new Date().getMinutes() * 10 + 88000);
    const roomId = this.query('roomId');
    const userId = this.query('userId');
    if (roomId) {
      $('#roomId')
        .val(roomId);
    }
    if (userId) {
      $('#userId')
        .val(userId);
    }
  }

  query(name) {
    const match = window.location.search.match(new RegExp(`(\\?|&)${name}=([^&]*)(&|$)`));
    return !match ? '' : decodeURIComponent(match[2]);
  }

  login(share, callback) {
    let userId = $('#userId')
      .val();
    if (share) {
      userId = `share_${parseInt(Math.random() * *********)}`;
    }
    const sdkAppId = parseInt(this.query('sdkAppId'), 10) || defaultSdkAppId;
    const roomId = parseInt(this.query('roomId'), 10) || parseInt($('#roomId').val(), 10);
    $.ajax({
      type: 'POST',
      url: fetchUrl,
      dataType: 'json',
      data: JSON.stringify({
        pwd: '********',
        appid: parseInt(sdkAppId),
        roomnum: parseInt(roomId),
        privMap: 255,
        identifier: userId,
        accounttype: accountType,
      }),
      success(json) {
        if (json && json.errorCode === 0) {
          const { userSig } = json.data;
          const privateMapKey = json.data.privMapEncrypt;
          callback({
            sdkAppId,
            userId,
            userSig,
            roomId,
            privateMapKey,
          });
        } else {
          console.error(`got invalid json:${json}`);
        }
      },
      error(err) {
        console.error('failed to retreive userSig');
      },
    });
  }
}

class RtcClient {
  constructor(options) {
    this.sdkAppId_ = options.sdkAppId;
    this.userId_ = options.userId;
    this.userSig_ = options.userSig;
    this.roomId_ = options.roomId;
    this.privateMapKey_ = options.privateMapKey;

    this.isJoined_ = false;
    this.isPublished_ = false;
    this.trtc = TRTC.create();
    this.trtc.enableAudioVolumeEvaluation(200);
    this.remotePublishedAudioUserIdSet = new Set();
    this.handleEvents();
  }

  updateParams(options) {
    if (typeof options.userId !== 'undefined' && typeof options.userSig !== 'undefined') {
      this.userId_ = options.userId;
      this.userSig_ = options.userSig;
    }
    if (typeof options.roomId !== 'undefined') {
      this.roomId_ = options.roomId;
    }
  }

  join() {
    this.trtc.enterRoom({
      sdkAppId: this.sdkAppId_,
      userId: this.userId_,
      userSig: this.userSig_,
      roomId: Number(this.roomId_),
      privateMapKey: this.privateMapKey_,
      proxy: { proxy: getProxyServer(), turnServer: { url: '109.244.129.237:443?transport=udp', username: '1677741774:turnuserid_55627', credential: 'mAya/0sr1lTNjSEKNnW+M8TBD4Y=', credentialType: 'password' } },
      role: getRole(),
      scene: getScene(),
      // enableAutoPlayDialog: false
      // receiveMode: getReceiveMode()
    });
  }

  leave() {
    this.trtc.exitRoom();
  }

  // 自定义采集
  // async startLocalVideo() {
  //   const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
  //   this.trtc.startLocalVideo({
  // element: 'local_stream', option: { videoSource: stream.getTracks()[0], mirror: false, fillMode: 'contain' } });
  // }

  startLocalVideo() {
    addLocalStreamDiv();
    const mainContainer = document.querySelector('#main');
    this.trtc.startLocalVideo({
      view: mainContainer.childNodes.length === 0 ? mainContainer : 'local_stream',
      option: {
        cameraId: getCameraId(),
        profile: getVideoProfile(),
        mirror: false,
        small: '120p',
      },
    });
  }

  switchCamera() {
    this.trtc.updateLocalVideo({
      option: {
        cameraId: getCameraId(),
        profile: getVideoProfile(),
        mirror: false,
        small: '120p',
      }
    });
  }

  switchMicrophone() {
    this.trtc.updateLocalAudio({
      option: { microphoneId: getMicrophoneId() },
    });
  }

  updateVideoProfile() {
    this.trtc.updateLocalVideo({
      option: { profile: getVideoProfile() },
    });
  }

  updateAudioProfile() {
    this.trtc.updateLocalAudio({
      option: { microphoneId: getMicrophoneId() },
    });
  }
  subscribeSmall(userId) {
    const div = $(`#${userId}_main`)[0];
    this.trtc.updateRemoteVideo({ element: div, userId, streamType: 0, option: { small: true } });
  }

  subscribeBig(userId) {
    const div = $(`#${userId}_main`)[0];
    this.trtc.updateRemoteVideo({ element: div, userId, streamType: 0, option: { small: false } });
  }

  stopLocalVideo() {
    removeLocalStreamDiv();
    this.trtc.stopLocalVideo();
  }
  switchRole(role) {
    this.trtc.switchRole(role);
  }
  handleEvents() {
    this.trtc.on(TRTC.EVENT.KICKED_OUT, console.error);
    this.trtc.on('screen-share-stopped', () => console.warn('screen share stopped'));
    this.trtc.on('remote-audio-available', ({ userId }) => {
      console.warn(`remote-audio-available ${userId}`);
      this.remotePublishedAudioUserIdSet.add(userId);
      if (getReceiveMode() === 0) {
      }
    });
    this.trtc.on(TRTC.EVENT.DEVICE_CHANGED, (data) => {
      console.warn(data);
    });
    this.trtc.on('remote-audio-unavailable', (event) => {
      console.warn(`remote-audio-unavailable ${event.userId}`);
      this.remotePublishedAudioUserIdSet.delete(event.userId);
    });
    this.trtc.on('remote-video-available', ({ userId, streamType }) => {
      const id = `${userId}_${streamType}`;
      addView(userId, streamType);
      this.trtc.startRemoteVideo({ element: id, userId, streamType });
    });
    this.trtc.on('remote-video-unavailable', (event) => {
      console.warn(`remote-video-unavailable ${JSON.stringify(event)}`);
      const id = `${event.userId}_${event.streamType}`;

      removeView(id);
    });
    this.trtc.on('audio-volume', (event) => {
      event.result.forEach(({ userId, volume }) => {
        const container = document.querySelector(`#${userId}_main`);
        if (container) {
          let volumeDiv = container.querySelector('#volume-div');
          if (!volumeDiv) {
            volumeDiv = document.createElement('div');
            volumeDiv.style.position = 'absolute';
            container.appendChild(volumeDiv);
          }
          volumeDiv.id = 'volume-div';
          volumeDiv.innerText = `${userId}音量：${volume}`;
        }
      });
    });
    this.trtc.on('network-quality', console.debug);
    this.trtc.on('audio-play-state-changed', console.warn);
    this.trtc.on('video-play-state-changed', console.warn);
    this.trtc.on('error', (error) => {
      console.error('rtc error:', error);
    });
    this.trtc.on('connection-state-changed', console.warn);
    this.trtc.on('autoplay-failed', () => {
      const button = document.createElement('button');
      button.innerText = '自动播放失败，点击恢复播放';
      button.onclick = () => document.body.removeChild(button);
      document.body.appendChild(button);
    });
  }


  startLocalAudio() {
    this.trtc.startLocalAudio({
      publish: true,
      option: {
        microphoneId: getMicrophoneId(),
      },
    });
  }

  stopLocalAudio() {
    this.trtc.stopLocalAudio();
  }
}

function addView(userId, streamType) {
  const id = `${userId}_${streamType}`;
  if (!document.querySelector(`#${id}`)) {
    const div = document.createElement('div');
    div.id = id;
    document.querySelector('#side').appendChild(div);
    const main = document.querySelector('#main');
    const side = document.querySelector('#side');
    div.onclick = () => {
      if (!div.querySelector('video')) {
        rtc.trtc.updateRemoteVideo({ element: div, userId, streamType });
        return;
      }
      if (main.childElementCount === 0) {
        // div.remove()
        // main.appendChild(div)
        rtc.trtc.updateRemoteVideo({ element: main, userId, streamType });
      } else {
        // side.appendChild(main.childNodes[0])
        // main.appendChild(div);
        side.childNodes.forEach((node) => {
          if (node.querySelector && !node.querySelector('video')) {
            if (node.id === 'local_stream') {
              rtc.trtc.updateLocalVideo({ element: 'local_stream' });
            } else {
              rtc.trtc.updateRemoteVideo({ element: node, userId, streamType });
            }
          }
        });
        rtc.trtc.updateRemoteVideo({ element: main, userId, streamType });
      }
    };
  }
}

function addLocalStreamDiv() {
  const div = document.createElement('div');
  div.id = 'local_stream';
  const main = document.querySelector('#main');
  const side = document.querySelector('#side');
  side.appendChild(div);
  div.onclick = () => {
    if (!div.querySelector('video')) {
      rtc.trtc.updateLocalVideo({ element: div });
      return;
    }
    if (main.childElementCount === 0) {
      rtc.trtc.updateLocalVideo({ element: main });
    } else {
      side.childNodes.forEach((node) => {
        if (node.querySelector && !node.querySelector('video')) {
          if (node.id === 'local_stream') {
            rtc.trtc.updateLocalVideo({ element: side });
          } else {
            rtc.trtc.updateRemoteVideo({ element: node, userId: node.id.split('_')[0], streamType: node.id.split('_')[1] });
          }
        }
      });
      rtc.trtc.updateLocalVideo({ element: main });
    }
  };
}

function removeLocalStreamDiv() {
  document.querySelector('#local_stream').remove();
}

function removeView(id) {
  if ($(`#${id}`)[0]) {
    $(`#${id}`)
      .remove();
  }
}

let rtc = null;
const presetting = new Presetting();

// initialize sdkAppId/userId/userSig stuffs
(function login() {
  presetting.init();
  presetting.login(false, (options) => {
    rtc = new RtcClient(options);
    // rtc.join();
  });
}());

// setup button event handlers

$('#test-onended').on('click', (event) => {
  console.warn('test-start');
  const player = new Audio('../assets/music/wow.mp3');
  player.crossOrigin = 'anonymous';
  player.loop = false;
  player.volume = 0.1;
  player.ontimeupdate = () => {
    console.warn(`currentTime: ${player.currentTime}/${player.duration}`);
  };
  player.onended = () => {
    console.warn('onended');
  };
  player.play();
});

$('#userId')
  .on('change', (e) => {
    e.preventDefault();
    console.log('userId changed');
    presetting.login(false, (options) => {
      delete options.roomId;
      rtc.updateParams(options);
    });
  });
$('#roomId')
  .on('input', (e) => {
    e.preventDefault();
    console.log(`roomId changed ${e.target.value}`);
    const validateVal = e.target.value.replace(/[^\d]/g, '');
    $('#roomId')
      .val(validateVal);
    rtc.updateParams({ roomId: validateVal });
  });

$('#sub_small')
  .on('click', (e) => {
    e.preventDefault();
    console.log('sub small');
    rtc.subscribeSmall($('#small_userId')[0].value);
  });

$('#sub_big')
  .on('click', (e) => {
    e.preventDefault();
    console.log('sub small');
    rtc.subscribeBig($('#big_userId')[0].value);
  });

$('#join')
  .on('click', (e) => {
    e.preventDefault();
    console.log('join');
    rtc.join();
  });

$('#start-local-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.startLocalVideo();
  });

$('#stop-local-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.stopLocalVideo();
  });
$('#start-local-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.startLocalAudio();
  });
$('#switch-microphone')
  .on('click', (e) => {
    e.preventDefault();
    rtc.switchMicrophone();
  });
$('#stop-local-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.stopLocalAudio();
  });
$('#start-screen-share')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.startScreenShare({
      element: 'local_stream',
      option: { systemAudio: true, profile: { width: 1920, height: 1080, frameRate: 60, bitrate: 2000 } },
    });
  });
$('#stop-screen-share')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.stopScreenShare();
  });
$('#switch-camera')
  .on('click', (e) => {
    e.preventDefault();
    rtc.switchCamera();
  });
$('#update-video-profile')
  .on('click', (e) => {
    e.preventDefault();
    rtc.updateVideoProfile();
  });
$('#switch-role-anchor').on('click', (e) => {
  e.preventDefault();
  console.log('switch-role-anchor');
  rtc.switchRole('anchor');
});

$('#switch-role-audience').on('click', (e) => {
  e.preventDefault();
  console.log('switch-role-audience');
  rtc.switchRole('audience');
});

$('#volume').on('input', (event) => {
  rtc.remotePublishedAudioUserIdSet.forEach((userId) => {
    rtc.trtc.setRemoteAudioVolume(userId, Number(event.target.value));
  });
});

$('#volume-local').on('input', (event) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: Number(event.target.value) }
  });
});

$('#start-audio-mixer').on('click', async (event) => {
  rtc.trtc.startPlugin('AudioMixer', {
    id: 'count',
    url: '../assets/music/count.mp3',
    loop: true,
    volume: 0.2
  });
});

$('#start-audio-mixer-m').on('click', async (event) => {
  rtc.trtc.startPlugin('AudioMixer', {
    id: 'robot',
    url: '../assets/music/robot.mp3?auth=1',
    loop: true,
    volume: 0.2
  });
});

$('#start-wow2').on('click', async (event) => {
  await rtc.trtc.startPlugin('AudioMixer', {
    id: 'wow',
    url: '../assets/music/wow.mp3',
    loop: true,
    volume: 0.2,
    playbackRate: 1,
    onDurationChange: (duration) => {
      console.warn(`duration: ${duration}`);
    },
    onTimeUpdate: (currentTime, duration) => {
      console.warn(`currentTime/duration: ${currentTime}/${duration}`);
    },
    onEnded: () => {
      console.warn('ended');
    }
  });
  console.warn('start success');
});

$('#start-wow').on('click', async (event) => {
  await rtc.trtc.startPlugin('AudioMixer', {
    id: 'wow',
    url: '../assets/music/wow.mp3',
    loop: false,
    volume: 0.2,
    playbackRate: 0.5,
    onDurationChange: (duration) => {
      console.warn(`duration: ${duration}`);
    },
    onTimeUpdate: (currentTime, duration) => {
      console.warn(`currentTime/duration: ${currentTime}/${duration}`);
    },
    onEnded: () => {
      console.warn('ended');
    }
  });
  console.warn('start success');
});

$('#update-wow').on('click', async (event) => {
  await rtc.trtc.updatePlugin('AudioMixer', {
    id: 'wow',
    seekFrom: 0,
    operation: 'resume',
    playbackRate: 2,
    onDurationChange: (duration) => {
      console.warn(`duration: ${duration}`);
    },
    onTimeUpdate: (currentTime, duration) => {
      console.warn(`currentTime/duration: ${currentTime}/${duration}`);
    },
    onEnded: () => {
      console.warn('ended');
    }
  });
  console.warn('update success');
});

$('#stop-wow').on('click', async (event) => {
  rtc.trtc.stopPlugin('AudioMixer', { id: 'wow' });
});

$('#stop-audio-mixer-m').on('click', async (event) => {
  rtc.trtc.stopPlugin('AudioMixer', { id: 'robot' });
});

$('#pause-audio-mixer').on('click', async (event) => {
  rtc.trtc.updatePlugin('AudioMixer', {
    id: 'count',
    operation: 'pause'
  });
});

$('#resume-audio-mixer').on('click', async (event) => {
  rtc.trtc.updatePlugin('AudioMixer', {
    id: 'count',
    operation: 'resume'
  });
});

$('#s-audio-mixer').on('click', async (event) => {
  rtc.trtc.updatePlugin('AudioMixer', {
    id: 'count',
    operation: 'stop'
  });
});

$('#seek-audio-mixer').on('click', async (event) => {
  rtc.trtc.updatePlugin('AudioMixer', {
    id: 'count',
    seekFrom: 5
  });
});

$('#pause-audio-mixer').on('click', async (event) => {
  rtc.trtc.updatePlugin('AudioMixer', {
    id: 'count',
    operation: 'pause'
  });
});

$('#stop-audio-mixer').on('click', (event) => {
  console.log('stop-audio-mixer');
  rtc.trtc.stopPlugin('AudioMixer', { id: 'count' });
});

window.track = null;
$('#get-track').on('click', (event) => {
  const audioPlayer = document.getElementById('audioPlayer');
  const mediaStream = audioPlayer.captureStream();
  window.track = mediaStream.getAudioTracks()[0];
});

// let audioTrack;
// 需要先执行此函数，再开启麦克风
$('#start-audio-track').on('click', (event) => {
  rtc.trtc.startPlugin('AudioMixer', { id: 'my-track', track: window.track });
  // rtc.trtc.startPlugin('AudioMixer', { id: 'my-track', track: window.track, url: '../assets/music/wow.mp3' });
});


$('#stop-audio-track').on('click', (event) => {
  rtc.trtc.stopPlugin('AudioMixer', { id: 'my-track' });
  // test
  // const mediaStream = new MediaStream([audioTrack]);
  // const audioPlayer = document.createElement('audio');
  // audioPlayer.srcObject = mediaStream;
  // audioPlayer.controls = true;
  // document.body.appendChild(audioPlayer);
});

$('#start-ai-denoiser').on('click', async (event) => {
  rtc.trtc.startPlugin('AIDenoiser', {
    assetsPath: 'https://web.sdk.qcloud.com/trtc/webrtc/download/aiNoiseReduce/',
    sdkAppId: defaultSdkAppId,
    userId: rtc.userId_,
    userSig: rtc.userSig_
  });
});

$('#stop-ai-denoiser').on('click', (event) => {
  rtc.trtc.stopPlugin('AIDenoiser');
});


$('#leave')
  .on('click', (e) => {
    e.preventDefault();
    console.log('leave');
    rtc.leave();
  });

$('#mute-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalVideo({ mute: true });
  });
$('#unmute-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalVideo({ mute: false });
  });
$('#mute-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalAudio({ mute: true });
  });
$('#unmute-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalAudio({ mute: false });
  });


$('#settings')
  .on('click', (e) => {
    e.preventDefault();
    $('#settings')
      .toggleClass('btn-raised');
    $('#setting-collapse')
      .collapse();
  });
TRTC.isSupported().then((result) => {
  const notSupported = Object.keys(result.detail).filter(key => !result.detail[key]);
  if (notSupported.length > 0) {
    const p = document.createElement('p');
    p.style.width = '300px';
    p.style.wordWrap = 'break-word';
    p.style.position = 'absolute';
    p.style.top = '50px';
    p.style.right = '50px';
    p.style.background = 'gray';

    p.innerText = `不支持如下能力：\n${notSupported.join('\n')}`;
    document.querySelector('#form').appendChild(p);
  }
});
// populate camera options
TRTC.getCameraList()
  .then((devices) => {
    devices.forEach((device) => {
      $('<option/>', {
        value: device.deviceId,
        text: device.label,
      })
        .appendTo('#cameraId');
    });
  });

// populate microphone options
TRTC.getMicrophoneList()
  .then((devices) => {
    devices.forEach((device) => {
      $('<option/>', {
        value: device.deviceId,
        text: device.label,
      })
        .appendTo('#microphoneId');
    });
  });

TRTC.setLogLevel(2);

function getCameraId() {
  const selector = document.getElementById('cameraId');
  const cameraId = selector[selector.selectedIndex].value;
  console.log(`selected cameraId: ${cameraId}`);
  return cameraId;
}

function getMicrophoneId() {
  const selector = document.getElementById('microphoneId');
  const microphoneId = selector[selector.selectedIndex].value;
  console.log(`selected microphoneId: ${microphoneId}`);
  return microphoneId;
}

function getVideoProfile() {
  const selector = document.getElementById('video-profile');
  const profile = selector[selector.selectedIndex].value;
  console.log(`selected video profile: ${profile}`);
  return profile;
}

function getProxyServer() {
  const selector = document.getElementById('proxy-server');
  const proxy = selector[selector.selectedIndex].value;
  console.log(`selected proxy: ${proxy}`);
  return proxy;
}

function getRole() {
  const selector = document.getElementById('role');
  const role = selector[selector.selectedIndex].value;
  console.log(`selected role: ${role}`);
  return role;
}

function getScene() {
  const selector = document.getElementById('scene');
  const scene = selector[selector.selectedIndex].value;
  console.log(`selected scene: ${scene}`);
  return scene;
}

function getReceiveMode() {
  const selector = document.getElementById('receive-mode');
  const receiveMode = selector[selector.selectedIndex].value;
  console.log(`selected receive-mode: ${receiveMode}`);
  return Number(receiveMode);
}
