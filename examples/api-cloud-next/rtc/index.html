<!doctype html>
<html lang="en">

<head>
  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Material Design for Bootstrap fonts and icons -->
  <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Material+Icons"> -->

  <!-- Material Design for Bootstrap CSS -->
  <link rel="stylesheet" href="../assets/css/bootstrap-material-design.min.css">
  <link rel="stylesheet" href="./css/common.css">
  <link rel="stylesheet" type="text/css" href="./css/toastify.min.css">
  <style>
    /* 修复美颜参数无法拖动的问题 */
    .set-beauty .bmd-form-group {
      padding-top: 0;
    }
    
    /* 修复顶部信息栏导致的内容区域偏移 */
    #form, .video-grid {
      margin-top: 25px !important;
    }
    
    /* 优化顶部信息栏样式 */
    .top-info-bar {
      position: absolute;
      left: 0;
      top: 0;
      display: flex;
      height: 25px;
      width: 100%;
      background-color: #f0f0f0;
      align-items: center;
      padding: 0 10px;
      z-index: 9999;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    /* 优化版本选择器样式 */
    .version-select {
      appearance: none;
      -webkit-appearance: none;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 3px;
      color: #333;
      cursor: pointer;
      font-size: 12px;
      margin-right: 10px;
      padding: 0 20px 0 8px;
      height: 20px;
      line-height: 20px;
      background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24'%3E%3Cpath fill='%23333' d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: right 2px center;
      background-size: 16px;
      transition: border-color 0.2s, box-shadow 0.2s;
    }
    
    .version-select:hover {
      border-color: #aaa;
    }
    
    .version-select:focus {
      border-color: #4285f4;
      box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
      outline: none;
    }
    
    /* 优化状态显示 */
    #sdk-status {
      font-size: 12px;
      margin-right: 10px;
      background-color: #f0f0f0;
      padding: 1px 6px;
      border-radius: 10px;
      color: #333;
      display: inline-flex;
      align-items: center;
    }
    
    /* 添加成功和失败的状态指示器 */
    #sdk-status.success::after {
      content: "✓";
      color: #4caf50;
      margin-left: 3px;
    }
    
    #sdk-status.fail::after {
      content: "✗";
      color: #f44336;
      margin-left: 3px;
    }
    
    /* 优化图标大小和样式 */
    .status-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      vertical-align: middle;
    }
    
    /* 优化详情按钮 */
    #show-sdk-info {
      font-size: 12px;
      padding: 0 8px;
      cursor: pointer;
      height: 20px;
      line-height: 20px;
      border-radius: 3px;
      background-color: #fff;
      border: 1px solid #ddd;
      color: #333;
      transition: background-color 0.2s;
    }
    
    #show-sdk-info:hover {
      background-color: #e8e8e8;
      border-color: #ccc;
    }
    
    /* 浮窗样式优化 */
    #sdk-loading-info {
      display: none; 
      position: absolute; 
      top: 28px; 
      left: 10px; 
      font-size: 12px; 
      padding: 12px; 
      border-radius: 4px; 
      background-color: #fff; 
      box-shadow: 0 3px 10px rgba(0,0,0,0.2); 
      z-index: 10000; 
      width: 300px;
      border: 1px solid #eee;
    }
    
    /* 插件状态样式 */
    .plugin-success {
      color: #4caf50;
    }
    
    .plugin-fail {
      color: #f44336;
    }
    
    /* 解决版本选择器上边距问题 */
    .version-select,
    .version-select.form-control,
    .version-select.form-control:focus {
      padding-top: 0 !important;
      margin-top: 0 !important;
      line-height: 20px !important;
      height: 20px !important;
    }
    
    /* 覆盖 bmd-form-group 样式对顶部栏的影响 */
    .bmd-form-group.is-filled .version-select,
    .bmd-form-group.is-focused .version-select,
    .top-info-bar .bmd-form-group {
      padding-top: 0 !important;
    }
  </style>
  <title>TRTC Web SDK Samples - 基础音视频通话</title>
</head>

<body>
  <!-- <nav class="navbar navbar-light fixed-top rtc-primary-bg">
    <h5>基础音视频通话</h5>
  </nav> -->
  <div style="display: flex; flex-direction: column; margin-top: 25px; background-color: gainsboro;">
    <div class="video-grid" id="video_grid">
      <div id="side"></div>
      <div id="main"></div>
    </div>
  </div>
  </div>
  <button id="open-setting" type="button" class="btn btn-raised btn-danger">关闭设置</button>
  <button id="open-stat" type="button" class="btn btn-raised btn-danger">打开 Stat</button>
  <div id="form">
    <div class="container">
      <div class="row">
        <div class="custom-row-container">
          <div>
            <div class="col-ms">
              <div class="card custom-card">
                <div class="form-group">
                  <label for="userId" class="bmd-label-floating">userID:</label>
                  <input type="text" class="form-control" name="userId" id="userId">
                </div>
                <div class="form-group bmd-form-group">
                  <label for="roomId" class="bmd-label-floating">roomID:</label>
                  <input type="text" class="form-control" name="roomId" id="roomId">
                </div>
                <div class="form-group bmd-form-group">
                  <label for="strRoomId" class="bmd-label-floating">strRoomId:</label>
                  <input type="text" class="form-control" name="strRoomId" id="strRoomId">
                </div>
                <div class="form-group none">
                  <label for="cameraId" class="bmd-label-floating">Remote UserId</label>
                  <select class="form-control" id="remote-userId" name="remote-userId">
                  </select>
                </div>
                <div class="form-group bmd-form-group none">
                  <label for="sub-num" class="bmd-label-floating">订阅路数上限:</label>
                  <input type="text" class="form-control" name="sub-num" id="sub-num">
                </div>
                <div>
                  <span id="join-cost"></span>
                  <span id="join-render-first"></span>
                  <span id="render-first"></span>
                </div>
                <div class="form-group" style="display: flex;">
                  <div style="display: flex;padding: 10px 0 0 0">
                    <div style="display: flex;">
                      <label for="enable-spc"></label>
                      <input type="checkbox" checked name="water-mark" id="enable-spc"> 单 PC
                      <input type="checkbox" name="vp8" id="vp8"> vp8 编码
                      <label for="HWEncoder"></label>
                      <input type="checkbox" name="HWEncoder" id="HWEncoder"> 开启硬编
                      <div id="encoderImplementation" style="margin-left: 5px"></div>
                    </div>
                  </div>
                </div>
                <div class="" style="display: flex;">
                  <div style="display: flex;">
                    <label for="small"></label>
                    <input type="checkbox" name="small" id="auto_sub_small"> 拉小流
                  </div>
                  <div style="display: flex;margin: 0 10px">
                    <label for="small"></label>
                    <input type="checkbox" name="small" id="small"> 推小流（不支持 IOS）
                  </div>
                </div>
                <div class="" style="display: flex;">
                  <div style="display: flex;">
                    <label for="playoutDelay"></label>
                    <input type="checkbox" name="playoutDelay" id="add_playout_delay">
                    设置 playoutDelay
                  </div>
                </div>
                <div class="" style="display: flex;">
                  <div style="display: flex;">
                    <label for="jitterBufferDelay"></label>
                    <input type="checkbox" name="jitterBufferDelay" id="jitterBufferDelay">
                    设置 jitterBufferDelay
                  </div>
                </div>
                <script>
                </script>
                <div class="" style="display: flex; flex-direction: row; width: 100%; margin-top: 10px;">
                  <div style="display: flex;margin: 0 10px">
                    <label for="small"></label>
                    <input type="radio" name="decoder" id="webcodecsDecode"> webcodecs解码
                  </div>
                  <div style="display: flex;margin: 0 10px">
                    <label for="small"></label>
                    <input type="radio" name="decoder" id="ffmpegDecode"> ffmpeg解码
                  </div>
                  <div style="display: flex;margin: 0 10px">
                    <label for="small"></label>
                    <input type="checkbox" name="yuvMode" id="yuvMode" checked> webgl渲染
                  </div>
                </div>

                <!-- <div style="display: flex;padding: px 0 0 0">
                  <label for="visible_video"></label>
                  <input type="checkbox" name="visible_video" checked id="visible_video"> 只拉可视区域视频流
                </div> -->
                <!-- <div class="form-group">
                  <label for="voice-changer" class="bmd-label-floating">voice changer</label>
                  <div style="display: flex;">
                    <input placeholder="voice type" type="number" value="1" class="form-control" name="voice-changer"
                      id="voice-changer">
                    <button id="start-voice-changer" type="button"
                      class="btn btn-raised btn-primary rtc-primary-bg">start</button>
                    <button id="update-voice-changer" type="button"
                      class="btn btn-raised btn-primary rtc-primary-bg">update</button>
                    <button id="stop-voice-changer" type="button"
                      class="btn btn-raised btn-primary rtc-primary-bg">stop</button>
                  </div>
                  <p>1-Brat 2-Loli 3-Uncle 4-Heavy Metal 5-Cold 6-Foreign Accent 7-Beast 8-Fat Otaku 9-Strong Current
                    10-Heavy Machinery
                    11-Ethereal.
                    1-熊孩子 2-萝莉 3-大叔 4-重金属 5-感冒 6-外语腔 7-困兽 8-肥宅 9-强电流 10-重机械 11-空灵</p>
                </div> -->
                <div class="form-group bmd-form-group">
                  <button id="join" type="button" class="btn btn-raised btn-primary rtc-primary-bg">进房</button>
                  <button id="leave" type="button" class="btn btn-raised btn-primary rtc-primary-bg">退房</button>
                  <button id="destroy" type="button" class="btn btn-raised btn-primary rtc-primary-bg">destroy</button>
                  <button id="switch-role-anchor" type="button"
                    class=" btn btn-raised btn-primary rtc-primary-bg">切主播</button>
                  <button id="switch-role-audience" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">切观众</button>
                  <br>
                  <button id="start-local-video" type="button"
                    class=" btn btn-raised btn-primary rtc-primary-bg">打开摄像头</button>
                  <button id="stop-preview-local-video" type="button"
                    class=" btn btn-raised btn-primary rtc-primary-bg">停止预览</button>
                  <button id="stop-local-video" type="button"
                    class=" btn btn-raised btn-primary rtc-primary-bg">关闭摄像头</button>
                  <button id="switch-camera" type="button"
                    class=" btn btn-raised btn-primary rtc-primary-bg">切换摄像头</button>
                  <button id="mute-video" type="button" class=" btn btn-raised btn-primary rtc-primary-bg">mute
                    Video</button>
                  <button id="mute-video-image" type="button" class=" btn btn-raised btn-primary rtc-primary-bg">mute
                    video 图片垫片</button>
                  <button id="unmute-video" type="button" class=" btn btn-raised btn-primary rtc-primary-bg">unmute
                    Video</button>
                  <button id="update-video-profile" type="button" class=" btn btn-raised btn-primary rtc-primary-bg">更新
                    video profile</button>
                  <button id="start-local-audio" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">开启麦克风</button>
                  <button id="stop-local-audio" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">关闭麦克风</button>
                  <button id="switch-microphone" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">切换麦克风</button>
                  <button id="mute-audio" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">muteAudio</button>
                  <button id="unmute-audio" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">unmuteAudio</button>
                  <button id="change-speaker" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">切换扬声器(speaker)</button>
                  <button id="change-headset" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">切换扬声器(headset)</button>
                  <button id="update-audio-profile" type="button" class="btn btn-raised btn-primary rtc-primary-bg">更新
                    audio profile</button>
                  <!-- <button id="recordPCM" type="button" class="btn btn-raised btn-primary rtc-primary-bg"
                    title="录制10s音频数据">录制PCM</button>
                  <button id="downloadPCM" type="button" class="btn btn-raised btn-primary rtc-primary-bg disabled"
                    title="点击'录制PCM'10s后可点击下载">下载PCM</button> -->
                  <br>
                  <div class="">
                    <label for="volume-local">调节本地音量</label>
                    <input type="range" id="volume-local" name="volume-local" min="0" max="500" value="100">
                  </div>
                  <div>
                    <label for="volume-local">耳返音量</label>
                    <input type="range" id="volume-ear" name="volume-local" min="0" max="100" value="0">
                  </div>

                  <div style="display: flex;">
                    <label for="system-audio"></label>
                    <input checked type="checkbox" name="system-audio" id="system-audio">系统音频
                  </div>
                  <button id="start-screen-share" type="button"
                    class=" btn btn-raised btn-primary rtc-primary-bg">开启屏幕分享</button>
                  <button id="stop-screen-share" type="button"
                    class=" btn btn-raised btn-primary rtc-primary-bg">关闭屏幕分享</button>
                  <div>
                    <button id="start-remote-video" type="button"
                      class=" btn btn-raised btn-primary rtc-primary-bg">观看远端</button>
                    <button id="stop-remote-video" type="button"
                      class=" btn btn-raised btn-primary rtc-primary-bg">停止观看</button>
                    <button id="mute-remote-audio" type="button"
                      class=" btn btn-raised btn-primary rtc-primary-bg">静音远端</button>
                    <button id="unmute-remote-audio" type="button"
                      class=" btn btn-raised btn-primary rtc-primary-bg">取消静音</button>
                  </div>
                  <div>
                    <button id="sub_small" type="button" class=" btn btn-raised btn-primary rtc-primary-bg">切小流</button>
                    <button id="sub_big" type="button" class=" btn btn-raised btn-primary rtc-primary-bg">切大流</button>
                  </div>

                  <div>
                    <label for="volume">调节远端音量</label>
                    <input type="range" id="volume" name="volume" min="0" max="500" value="100">
                  </div>

                  <div>
                    <input type="text" class="form-control" name="video-frame-userId" id="video-frame-userId"
                      placeholder="userId">
                    <!-- <input type="text" class="form-control" name="video-frame-streamType" id="video-frame-streamType"
                      placeholder="streamType"> -->
                    <select class="form-control" name="video-frame-streamType" id="video-frame-streamType">
                      <option value="main" selected>main</option>
                      <option value="sub">sub</option>
                    </select>
                    <button id="video-frame" type="button"
                      class=" btn btn-raised btn-primary rtc-primary-bg">获取视频帧</button>
                    <button id="device-detector" type="button"
                      class="btn btn-raised btn-primary rtc-primary-bg">开启检测组件</button>
                    <button id="device-detector-network" type="button"
                      class="btn btn-raised btn-primary rtc-primary-bg">开启检测组件(网络)</button>
                    <br>
                  </div>
                </div>
              </div>
              <div class="col-ms">
                <button class="btn rtc-expand-btn" id="message" data-toggle="collapse" data-target="#message-collapse"
                  aria-expanded="true" aria-controls="collapse">
                  Message
                </button>
                <div id="message-collapse" class="collapse card custom-card" aria-labelledby="message-collapse">
                  <div style="display: flex;padding: 10px 0 0 0">
                    <label for="enable-sei"></label>
                    <input type="checkbox" name="enable-sei" id="enable-sei">开启 SEI（需进房前开启）
                  </div>
                  <div class="form-group">
                    <label for="sei" class="bmd-label-floating">SEI Message</label>
                    <div style="display: flex;">
                      <input type="text" class="form-control" style="width: 40%;" name="sei" id="sei">
                      <button id="send-sei" type="button" class="btn btn-raised btn-primary rtc-primary-bg">发送
                        SEI</button>
                      <button id="send-sei-aux" style="margin-left: 10px;" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">发送辅流 SEI</button>
                    </div>
                  </div>
                  <div class="form-group">
                    <div>
                      <div style="display: flex; align-items: center;">
                        <span style="margin-right: 12px;">cmdId</span>
                        <input style="width: 30px;" placeholder="cmdId" type="number" value="1" class="form-control"
                          name="cmdId" id="cmdId">
                        <input placeholder="custom-msg" type="text" class="form-control" name="custom-msg"
                          id="custom-msg">
                        <button style="width: 230px;" id="send-custom-msg" type="button"
                          class="btn btn-raised btn-primary rtc-primary-bg">发自定义消息</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-ms">
                <button class="btn rtc-expand-btn" id="advance" data-toggle="collapse" data-target="#advance-collapse"
                  aria-expanded="true" aria-controls="collapse">
                  Advance
                </button>
                <div id="advance-collapse" class="collapse card custom-card" aria-labelledby="advance-collapse">
                  <div style="display: flex;">
                    mirror:
                    <div style="display: flex">
                      <label for="background"></label>
                      <input type="radio" name="mirror" id="blur-mirror" value="true"> true
                    </div>
                    <div style="display: flex">
                      <label for="background"></label>
                      <input type="radio" name="mirror" id="blur2-mirror" value="false"> false
                    </div>
                    <div style="display: flex">
                      <label for="background"></label>
                      <input type="radio" name="mirror" id="blur3-mirror" value="view"> 'view'
                    </div>
                    <div style="display: flex">
                      <label for="background"></label>
                      <input type="radio" name="mirror" id="virtual-mirror" value="publish"> 'publish'
                    </div>
                    <div style="display: flex">
                      <label for="background"></label>
                      <input type="radio" name="mirror" id="green-mirror" value="both"> 'both'
                    </div>
                  </div>
                  <div style="display: flex;">
                    <label for="water-mark"></label>
                    <input type="checkbox" name="water-mark" id="water-mark"> 水印
                    <button name="water-mark" id="update-water-mark"> 更新水印 </button>
                  </div>
                  <div style="display: flex">
                    <label for="mirror"></label>
                    <input type="checkbox" name="mirror" id="mirror"> 编码翻转
                  </div>
                  <div style="display: flex;">
                    <div style="display: flex;">
                      <label for="aec"></label>
                      <input type="checkbox" checked name="aec" id="aec"> 回声消除
                    </div>
                    <div style="display: flex;">
                      <label for="ans"></label>
                      <input type="checkbox" checked name="ans" id="ans"> 降噪
                    </div>
                    <div style="display: flex;">
                      <label for="agc"></label>
                      <input type="checkbox" checked name="agc" id="agc"> 自动增益
                    </div>
                    <div style="display: flex;">
                      <label for="ai-denoiser"></label>
                      <input type="checkbox" name="ai-denoiser" id="ai-denoiser"> AI 降噪
                    </div>
                    <div style="display: flex;">
                      <label for="bgm"></label>
                      <input type="checkbox" name="bgm" id="bgm"> 背景音
                    </div>
                  </div>
                  <div class="" style="display: flex; flex-wrap: nowrap; align-items: center;">
                    <span> 虚拟背景：</span>
                    <button class="btn btn-raised btn-primary rtc-primary-bg" id="install-vb"> preload </button>
                    <div style="display: flex">
                      <label for="background"></label>
                      <input type="radio" name="background" id="normal-background" value="normal" checked> 正常
                    </div>
                    <div style="display: flex">
                      <label for="background"></label>
                      <input type="radio" name="background" id="virtual-background" value="image"> 图片
                    </div>
                    <div style="display: flex">
                      <label for="background"></label>
                      <input type="radio" name="background" id="blur-background" value="blur"> 虚化
                    </div>
                    <span>
                      (level：
                      <select name="pets" id="vb-level-select" style="padding: 0;">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3" selected>3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                        <option value="8">8</option>
                        <option value="9">9</option>
                        <option value="10">10</option>
                      </select>
                      )
                    </span>
                  </div>
                  <div>
                    <button id="start-beauty" type="button"
                      class="btn btn-raised btn-primary rtc-primary-bg">开启美颜</button>
                    <button id="update-beauty" type="button"
                      class="btn btn-raised btn-primary rtc-primary-bg">更新美颜</button>
                    <button id="stop-beauty" type="button"
                      class="btn btn-raised btn-primary rtc-primary-bg">关闭美颜</button>
                    <div class="set-beauty">
                      美颜 <input type="range" id="beauty-beauty" name="beauty-beauty-local" min="0" max="1" step="0.1"
                        value="0.5">
                    </div>
                    <div class="set-beauty">
                      明亮 <input type="range" id="beauty-brightness" name="beauty-brightness-local" min="0" max="1"
                        step="0.1" value="0.5">
                    </div>
                    <div class="set-beauty">
                      红润 <input type="range" id="beauty-ruddy" name="beauty-ruddy-local" min="0" max="1" step="0.1"
                        value="0.5">
                    </div>
                  </div>
                  <br>
                  <button id="beauty-ar" type="button" class="btn btn-raised btn-primary rtc-primary-bg">开启高级美颜</button>
                  <div class="form-group">
                    <label for="voice-changer" class="bmd-label-floating">voice changer</label>
                    <div style="display: flex;">
                      <input placeholder="voice type" type="number" value="1" class="form-control" name="voice-changer"
                        id="voice-changer" oninput="if(value>12)value=12;if(value<0)value=0">
                      <button id="start-voice-changer" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">start</button>
                      <button id="update-voice-changer" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">update</button>
                      <button id="stop-voice-changer" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">stop</button>
                    </div>
                    <p>1-Brat 2-Loli 3-Uncle 4-Heavy Metal 5-Cold 6-Foreign Accent 7-Beast 8-Fat Otaku 9-Strong Current
                      10-Heavy Machinery
                      11-Ethereal.
                      1-熊孩子 2-小黄人 3-大叔 4-重金属 5-感冒 6-外语腔 7-困兽 8-肥宅 9-强电流 10-重机械 11-空灵</p>
                  </div>
                  <div class="form-group">
                    <input type="text" class="form-control" name="mp4-url" id="mp4-url">
                    <button id="publish-mp4" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">推视频文件</button>
                  </div>
                </div>
              </div>
              <div class="col-ms">
                <div class="card">
                  <button class="btn rtc-expand-btn" id="settings" data-toggle="collapse"
                    data-target="#setting-collapse" aria-expanded="true" aria-controls="collapse">
                    Settings
                  </button>
                  <div id="setting-collapse" class="collapse card custom-card" aria-labelledby="setting-collapse">
                    <div class="card-body">
                      <div class="form-group">
                        <label for="cameraId" class="bmd-label-floating">CAMERA</label>
                        <select class="form-control" id="cameraId" name="cameraId">
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="microphoneId" class="bmd-label-floating">MICROPHONE</label>
                        <select class="form-control" id="microphoneId" name="microphoneId">
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="speaker" class="bmd-label-floating">speaker</label>
                        <select class="form-control" id="speaker" name="speaker">
                        </select>
                      </div>
                      <div class="form-group">
                        <div style="display: flex;">
                          <label for="avoidCropping"></label>
                          <input type="checkbox" name="avoidCropping" id="avoidCropping"> avoidCropping
                        </div>
                        <label for="video-profile" class="bmd-label-floating">video profile</label>
                        <select class="form-control" id="video-profile" name="video-profile">
                          <option value="120p">120p</option>
                          <option value="180p">180p</option>
                          <option value="240p">240p</option>
                          <option value="360p">360p</option>
                          <option value="480p" selected>480p</option>
                          <option value="480p30fps600kbps">480p30fps600kbps</option>
                          <option value="480p30fps1500kbps">480p30fps1500kbps</option>
                          <option value="960p30fps1000kbps">960p30fps1000kbps</option>
                          <option value="960p30fps2000kbps">960p30fps2000kbps</option>
                          <option value="720p">720p</option>
                          <option value="720p30">720p30</option>
                          <option value="1080p">1080p</option>
                          <option value="1440p">2K</option>
                          <option value="4K">4K</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="audio-profile" class="bmd-label-floating">audio profile</label>
                        <select class="form-control" id="audio-profile" name="audio-profile">
                          <option value="standard" selected> 48000, 1, 40kbps</option>
                          <option value="standard-stereo">48000, 2, 64kbps</option>
                          <option value="high">48000, 1, 192kbps</option>
                          <option value="high-stereo">48000, 2, 192kbps</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="scene" class="bmd-label-floating">scene</label>
                        <select class="form-control" id="scene" name="scene">
                          <option value="rtc" selected>rtc</option>
                          <option value="live">live</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="role" class="bmd-label-floating">role</label>
                        <select class="form-control" id="role" name="role">
                          <option value="anchor" selected>anchor</option>
                          <option value="audience">audience</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="latencyLevel" class="bmd-label-floating">latencyLevel</label>
                        <select class="form-control" id="latencyLevel" name="latencyLevel">
                          <option value="" selected>不设置</option>
                          <option value="1">1低延时</option>
                          <option value="2">2普通延迟(极速直播)</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="auto-receive-audio" class="bmd-label-floating">autoReceiveAudio</label>
                        <select class="form-control" id="auto-receive-audio" name="auto-receive-audio">
                          <option value="0">不自动播音频</option>
                          <option value="1" selected>自动播音频</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="auto-receive-video" class="bmd-label-floating">autoReceiveVideo</label>
                        <select class="form-control" id="auto-receive-video" name="auto-receive-video">
                          <option value="0">不自动播视频</option>
                          <option value="1" selected>自动播视频</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="screen-qos" class="bmd-label-floating">屏幕分享编码策略</label>
                        <select class="form-control" id="screen-qos" name="screen-qos">
                          <option value="clear">清晰度优先</option>
                          <option value="smooth" selected>流畅度优先</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="proxy-server" class="bmd-label-floating">proxy server</label>
                        <select class="form-control" id="proxy-server" name="proxy-server">
                          <option value="" selected>no proxy</option>
                          <option value="https://test11.rtc.qq.com:8080">test11_wt8080</option>
                          <option value="wss://test11.rtc.qq.com:8687">test11_ws8687</option>
                          <option value="wss://signaling.rtc.qcloud.com">signaling.rtc.qcloud.com</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="unified-proxy" class="bmd-label-floating">unified proxy</label>
                        <select class="form-control" id="unified-proxy" name="unified-proxy">
                          <option value="" selected>no proxy</option>
                          <option value="webproxy-zhngs-test.cloud-rtc.com">webproxy-zhngs-test.cloud-rtc.com</option>
                          <option value="webproxy-test.cloud-rtc.com">webproxy-test.cloud-rtc.com</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="unified-proxy-input" class="bmd-label-floating">unified proxy</label>
                        <input type="text" class="form-control" name="unified-proxy-input" id="unified-proxy-input">
                      </div>
                      <div class="form-group" style="display: flex;">
                        <div>
                          <label for="turn-server" class="bmd-label-floating">turn server</label>
                          <input type="text" class="form-control" name="turn-server" id="turn-server">
                        </div>
                        <div style="display: flex;padding: 10px 0 0 0">
                          <label for="force-turn"></label>
                          <input type="checkbox" name="force-turn" id="force-turn">强制走 turn（需加 url 参数 forceTurn=1）
                        </div>
                      </div>
                      <div>
                        <label for="turn-username" class="bmd-label-floating">username</label>
                        <input type="text" class="form-control" name="turn-username" id="turn-username">
                      </div>
                      <div>
                        <label for="turn-credential" class="bmd-label-floating">credential</label>
                        <input type="text" class="form-control" name="turn-credential" id="turn-credential">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-ms">
                <button class="btn rtc-expand-btn" id="cross-room" data-toggle="collapse"
                  data-target="#cross-room-collapse" aria-expanded="true" aria-controls="collapse">
                  Cross Room
                </button>
                <div id="cross-room-collapse" class="collapse card custom-card" aria-labelledby="cross-room-collapse">
                  <div class="form-group bmd-form-group">
                    <label for="remote-roomId" class="bmd-label-floating">remote roomId</label>
                    <input type="text" class="form-control" name="remote-roomId" id="remote-roomId">
                  </div>
                  <div class="form-group bmd-form-group">
                    <label for="remote-strRoomId" class="bmd-label-floating">remote strRoomId</label>
                    <input type="text" class="form-control" name="remote-strRoomId" id="remote-strRoomId">
                  </div>
                  <div class="form-group bmd-form-group">
                    <label for="cross-room-remote-userId" class="bmd-label-floating">remote userId</label>
                    <input type="text" class="form-control" name="cross-room-remote-userId"
                      id="cross-room-remote-userId">
                  </div>
                  <div style="display: flex;">
                    <label for="cross-room-muteAudio"></label>
                    <input type="checkbox" name="cross-room-muteAudio" id="cross-room-muteAudio"> muteAudio
                  </div>
                  <div style="display: flex;">
                    <label for="cross-room-muteVideo"></label>
                    <input type="checkbox" name="cross-room-muteVideo" id="cross-room-muteVideo"> muteVideo
                  </div>
                  <div style="display: flex;">
                    <label for="cross-room-muteSubStream"></label>
                    <input type="checkbox" name="cross-room-muteSubStream" id="cross-room-muteSubStream"> muteSubStream
                  </div>
                  <button id="start-cross-room" type="button" class="btn btn-raised btn-primary rtc-primary-bg">Start
                    Cross Room</button>
                  <button id="update-cross-room" type="button" class="btn btn-raised btn-primary rtc-primary-bg">Update
                    Cross Room</button>
                  <button id="stop-cross-room" type="button" class="btn btn-raised btn-primary rtc-primary-bg">Stop
                    Cross Room</button>
                </div>
              </div>
              <div class="col-ms">
                <button class="btn rtc-expand-btn" id="custom-encryption" data-toggle="collapse"
                  data-target="#custom-encryption-collapse" aria-expanded="true" aria-controls="collapse">
                  Custom Encryption
                </button>
                <div id="custom-encryption-collapse" class="collapse card custom-card"
                  aria-labelledby="custom-encryption-collapse">
                  <div>
                    <label for="algorithm-select">选择加解密算法</label>
                    <select id="algorithm-select" name="algorithm-select">
                      <option value="AES-GCM">[内置] AES-GCM</option>
                      <option value="">[自定义] 对每个 bit 做异或操作</option>
                    </select>
                  </div>
                  <div class="form-group bmd-form-group">
                    <label for="secret-key" class="bmd-label-floating">secret key</label>
                    <input type="text" class="form-control" name="secret-key" id="secret-key" value="1111222233334444">
                    <button type="button" class="btn btn-raised btn-secondary" onclick="generateSecretKey()">Generate
                      Secret Key</button>
                    <script>
                      function generateSecretKey() {
                        const hexDigits = '0123456789abcdef';
                        let secretKey = '';
                        for (let i = 0; i < 32; i++) {
                          secretKey += hexDigits[Math.floor(Math.random() * 16)];
                        }
                        document.getElementById('secret-key').value = secretKey;
                      }
                    </script>
                  </div>
                  <div class="form-group bmd-form-group">
                    <label for="salt" class="bmd-label-floating">salt (optional)</label>
                    <input type="text" class="form-control" name="salt" id="salt" value="1111222233334444">
                  </div>
                  <button id="start-custom-encryption" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">Start
                    Custom Encryption</button>
                  <button id="stop-custom-encryption" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">Stop
                    Custom Encryption</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button id="close-setting" type="button" style="position: absolute; right: 12px; top: 12px;">close</button>
    </div>

    <div id="stat" style="display: none;">
      <table>
        <tr>
          <th>进房耗时</th>
          <th>订阅首帧</th>
          <th>进房首帧</th>
          <th>秒开率</th>
          <th>远端数</th>
        </tr>
      </table>
    </div>
  </div>
  <div id="debug-stat"
    style="z-index: 999;display: none;position: fixed;left: 12px;top: 0;color: #fff;background: rgba(0, 0, 0, .5);padding: 6px 12px;">
  </div>
  <div class="top-info-bar">
    <img id="smile" hidden src="./image/smile.svg" alt="" class="status-icon">
    <img id="sad" hidden src="./image/sad.svg" alt="" class="status-icon">
    <img id="loading" hidden src="./image/loading.svg" alt="" class="status-icon">
    <span style="margin-right: 5px; font-size: 12px; color: #333;">版本:</span>
    <select id="version" class="version-select">
      <option value="dev">本地(dev)</option>
      <option value="latest">线上(latest)</option>
      <option value="build">构建(build)</option>
    </select>
    <span id="sdk-status">-</span>
    <button id="show-sdk-info">查看详情</button>
    
    <!-- SDK加载信息浮窗 -->
    <div id="sdk-loading-info">
      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
        <strong style="color: #333;">SDK加载详情</strong>
        <span id="close-sdk-info" style="cursor: pointer; font-size: 16px; color: #999;">&times;</span>
      </div>
      <div style="margin-bottom: 5px; color: #555;"><strong>SDK版本:</strong> <span id="sdk-version-info">-</span></div>
      <div style="margin-bottom: 5px; color: #555;"><strong>加载路径:</strong> <span id="sdk-path-info" style="word-break: break-all; display: inline-block; max-width: 100%;">-</span></div>
      <div style="color: #555;"><strong>插件状态:</strong> <div id="plugins-status" style="display: inline; word-break: break-all;">-</div></div>
    </div>
  </div>
  <script src="./js/vconsole.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <!-- Optional JavaScript -->
  <!-- jQuery first, then Popper.js, then Bootstrap JS -->
  <!-- <script src="/assets/js/jquery-3.2.1.slim.min.js"></script> -->
  <script>
    var ua = navigator.userAgent.toLowerCase();
    if (/mobile|android|iphone|ipad|phone/i.test(ua)) {
      var vConsole = new window.VConsole();
    }
  </script>
  <script src="../assets/js/jquery-3.2.1.min.js"></script>
  <!-- <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script> -->
  <script src="../assets/js/popper.js"></script>
  <script src="../assets/js/bootstrap-material-design.js"></script>
  <script>$(document).ready(function () { $('body').bootstrapMaterialDesign(); });</script>
  <script type="text/javascript" src="./js/toastify-js.js"></script>
  <script src="./js/lib-generate-test-usersig.min.js"></script>
  <script src="./js/debug/GenerateTestUserSig.js"></script>

  <script>
    // 1. 初始化变量
    const pluginMap = {
      'CDNStreaming': { path: 'plugins/cdn-streaming/cdn-streaming.umd.js' },
      'BasicBeauty': { path: 'plugins/video-effect/basic-beauty/basic-beauty.umd.js' },
      'VirtualBackground': { path: 'plugins/video-effect/virtual-background/virtual-background.umd.js' },
      'Watermark': { path: 'plugins/video-effect/watermark/watermark.umd.js' },
      'Beauty': { path: 'plugins/video-effect/beauty/beauty.umd.js' },
      'DeviceDetector': { path: 'plugins/device-detector/device-detector.umd.js' },
      'CrossRoom': { path: 'plugins/cross-room/cross-room.umd.js' },
      'TRTCVideoDecoder': { path: 'plugins/video-decoder/video-decoder.umd.js' },
      'CustomEncryption': { path: 'plugins/custom-encryption/custom-encryption.umd.js' },
      'VoiceChanger': { path: 'plugins/voice-changer/voice-changer.umd.js' },
      'TRTC': { path: 'trtc.js' },
    };
    
    // 设置SDK信息浮窗的显示和隐藏
    document.getElementById('show-sdk-info').addEventListener('click', () => {
      document.getElementById('sdk-loading-info').style.display = 'block';
    });
    document.getElementById('close-sdk-info').addEventListener('click', () => {
      document.getElementById('sdk-loading-info').style.display = 'none';
    });
    
    // 清除可能存在的之前的状态类，防止状态图标重复
    function clearPreviousStatusIcons() {
      const sdkStatus = document.getElementById('sdk-status');
      if (sdkStatus) {
        sdkStatus.className = '';
      }
    }
    
    // 根据版本获取SDK根路径，移到全局作用域
    function getRootPathForSDK(version) {
      if (version === 'custom') {
        const url = new URL(location.href);
        let path = url.searchParams.get('sdkPath');
        if (path[path.length - 1] !== '/') {
          path += '/';
        }
        let needIIFE = url.searchParams.get('needIIFE') === '1';
        if (needIIFE) {
          Object.entries(pluginMap).forEach(([key, plugin]) => {
            if (plugin.path.includes('.umd.js')) {
              plugin.path = plugin.path.replace('.umd.js', '.iife.js');
            }
          });
        }
        console.log('custom', path);
        return path; // https://web.sdk.qcloud.com/trtc/webrtc/v5/test/5.8.5/dist/npm-package
      }
      if (version.includes('dev')) return '/';
      if (version === 'latest') return 'https://web.sdk.cloudcachetci.com/trtc/webrtc/v5/dist/';
      if (version === 'build') return '../dist/npm-package/';
      return `https://unpkg.com/trtc-sdk-v5@${version}/`;
    }
    
    // 添加状态图标管理函数
    function updateStatusIcons(status, sdkStatus) {
      const loading = document.getElementById('loading');
      const smile = document.getElementById('smile');
      const sad = document.getElementById('sad');
      
      // 隐藏所有图标
      loading.hidden = true;
      smile.hidden = true;
      sad.hidden = true;
      
      // 根据状态显示对应图标和添加状态类
      switch(status) {
        case 'loading':
          loading.hidden = false;
          break;
        case 'success':
          smile.hidden = false;
          sdkStatus.classList.add('success');
          break;
        case 'fail':
          sad.hidden = false;
          sdkStatus.classList.add('fail');
          break;
      }
    }
    
    document.getElementById('version').addEventListener('change', () => loadScripts(true));
    $('#version').val(location.origin === 'https://web.sdk.qcloud.com' ? 'latest' : 'dev');
    
    // 指定了版本
    const queryVersion = new URLSearchParams(location.search).get('version') || null;
    if (queryVersion) {
      const select = document.getElementById('version');
      const option = document.createElement('option');
      option.value = queryVersion;
      option.textContent = queryVersion;
      select.appendChild(option);
      for (let i = 0; i < select.options.length; i++) {
        select.options[i].defaultSelected = select.options[i].value === queryVersion;
      }
    }

    // 2. 动态加载 trtc sdk
    loadScripts();
    async function loadScripts(initTRTC = false) {
      // 更新加载信息显示
      const sdkStatus = document.getElementById('sdk-status');
      
      // 清除之前的状态类，防止状态图标重复
      clearPreviousStatusIcons();
      
      const sdkVersionInfo = document.getElementById('sdk-version-info');
      const sdkPathInfo = document.getElementById('sdk-path-info');
      const pluginsStatus = document.getElementById('plugins-status');

      const currentVersion = queryVersion || $('#version').val();
      // 设置加载状态
      updateStatusIcons('loading', sdkStatus);
      
      // 只在开始时设置一次状态文本，后续只更新状态类
      sdkStatus.textContent = `${currentVersion}`;
      sdkVersionInfo.textContent = `正在加载 ${currentVersion} 版本`;
      
      // 使用全局定义的函数获取根路径
      const rootPath = getRootPathForSDK(currentVersion);
      sdkPathInfo.textContent = rootPath;
      
      // 根据版本决定插件文件格式
      const [major, minor, patch] = currentVersion.split('.');
      const minorNum = Number(minor);
      console.warn(minorNum, patch, String(patch).includes('beta'));
      if (minorNum < 10 && (minorNum <= 9 && !String(patch).includes('2-beta'))) {
        Object.entries(pluginMap).forEach(([key, plugin]) => {
          if (plugin.path.includes('.umd.js')) {
            plugin.path = plugin.path.replace('.umd.js', '.iife.js');
          }
        });
      }

      // 开始加载所有插件
      pluginsStatus.textContent = '正在加载插件...';
      
      // 主要加载过程
      await Promise.all(Object.entries(pluginMap).map(([key, plugin]) =>
        load(key, `${rootPath}${plugin.path}`)
      ));
      
      // 条件检查和错误处理
      if (!pluginMap.TRTC.loaded && queryVersion) {
        alert(`The TRTC version ${queryVersion} you queried does not exist.`);
        sdkVersionInfo.textContent = `${queryVersion} 版本不存在`;
        updateStatusIcons('fail', sdkStatus);
      }

      // 加载不成功的兜底处理 - 简化逻辑，只尝试加载失败的插件
      if (currentVersion === 'dev') {
        const failedPluginLoads = [];
        
        // 收集失败的插件，准备重新加载
        for (const key in pluginMap) {
          const plugin = pluginMap[key];
          if (!plugin.loaded) {
            // 尝试从npm-package目录加载
            failedPluginLoads.push(load(key, `${rootPath}npm-package/${plugin.path}`));
          }
        }
        
        // 如果有失败的插件，尝试重新加载
        if (failedPluginLoads.length > 0) {
          await Promise.all(failedPluginLoads);
        }
      }
      
      // 优化：通过一次遍历同时获取成功和失败的插件
      const loadedPlugins = [];
      const failedPlugins = [];
      
      Object.entries(pluginMap).forEach(([name, plugin]) => {
        if (plugin.loaded) {
          loadedPlugins.push(name);
          console.log(`Plugin ${name} loaded from ${plugin.src}`);
        } else {
          failedPlugins.push(name);
          console.warn(`Plugin ${name} load failed from ${plugin.src}`);
        }
      });
      
      // 将成功和失败的插件一起显示，失败的用红色标注
      pluginsStatus.innerHTML = '';
      if (loadedPlugins.length > 0) {
        const successElement = document.createElement('div');
        successElement.innerHTML = `<span>成功: </span>
          <span class="plugin-success">${loadedPlugins.join(', ')}</span>`;
        pluginsStatus.appendChild(successElement);
      }
      
      if (failedPlugins.length > 0) {
        const failElement = document.createElement('div');
        failElement.innerHTML = `<span>失败: </span>
          <span class="plugin-fail">${failedPlugins.join(', ')}</span>`;
        pluginsStatus.appendChild(failElement);
      }
      
      if (loadedPlugins.length === 0 && failedPlugins.length === 0) {
        pluginsStatus.textContent = '无插件加载信息';
      }

      // 根据TRTC加载状态更新图标
      if (pluginMap.TRTC.loaded) {
        updateStatusIcons('success', sdkStatus);
      } else {
        updateStatusIcons('fail', sdkStatus);
      }
      
      sdkVersionInfo.textContent = `${currentVersion} 版本加载${pluginMap.TRTC.loaded ? '成功' : '失败'}`;

      // 切换版本后重新初始化 trtc
      let isIndexFileLoaded = false;
      if (!initTRTC) {
        const indexScript = document.createElement('script');
        indexScript.src = './js/index.js';
        document.head.appendChild(indexScript);
        isIndexFileLoaded = true;
      } else {
        console.log(presetting);
        presetting.init();
        presetting.login(false, (options) => {
          rtc = new RtcClient(options);
          if (new URLSearchParams(location.search).get('autoJoin') === '1') {
            rtc.join();
          };
        });
      }

      async function load(name, src) {
        return new Promise((resolve, reject) => {
          pluginMap[name].src = src;
          const script = document.createElement('script');
          script.src = src;
          script.onload = () => {
            pluginMap[name].loaded = true;
            resolve();
          };
          script.onerror = () => {
            pluginMap[name].loaded = false;
            resolve();
          };
          document.head.appendChild(script);
        });
      }
    }
    
    // 恢复自动调用loadNextBetaVersion
    loadNextBetaVersion();
    async function loadNextBetaVersion() {
      const data = await fetch('https://registry.npmjs.org/trtc-sdk-v5/beta').then(res => res.json());
      const latestBetaVersion = data.version;
      const select = document.getElementById('version');
      const latestNumber = latestBetaVersion.split('.').reverse()[0];
      for (let i = 1; i <= latestNumber; i++) {
        const option = document.createElement('option');
        const version = `${latestBetaVersion.split('.').slice(0, -1).join('.')}.${i}`;
        option.value = version;
        option.textContent = version;
        select.appendChild(option);
      }
    };
  </script>
</body>

</html>