{"name": "trtc-sdk-v5", "version": "5.10.1", "description": "TRTC Web SDK", "main": "dist/trtc.js", "module": "dist/trtc.esm.js", "types": "dist/npm-package/index.d.ts", "dependencies": {"@trtc/js-sdk-types": "workspace:*", "@trtc/video-decoder": "file:../../plugins/video-decoder", "afsm": "2.4.9", "core-js": "^3.30.0", "eventemitter3": "4.0.7", "fastrx": "^3.3.1", "jv4-decoder": "^1.1.0", "sdp-transform": "2.14.1", "trtc-js-sdk-core": "workspace:*", "trtc-js-sdk-wasm": "workspace:*", "webrtc-adapter": "^8.2.3"}, "scripts": {"dev": "node ./build/esbuild", "dev:vue": "node ./build/esbuild", "dev:wasm": "node ./build/esbuild --room wasm", "esbuild": "node ./build/esbuild", "esbuild2": "node ./build/esbuild2", "esbuild:prd": "node ./build/esbuild --prd", "esbuild:prd:iife": "node ./build/esbuild --prd -f iife", "esbuild:example:blur:dev": "node ./build/esbuild -f iife -d ../../examples/api-cloud-next/background-blur/v5-ziyan -n srtc ", "esbuild:example:blur": "node ./build/esbuild --prd -f iife -d ../../examples/api-cloud-next/background-blur/v5-ziyan -n srtc ", "esbuild:wasm": "node ./build/esbuild --room wasm --prd", "esbuild:wasm:iife": "node ./build/esbuild --room wasm --prd -f iife", "babel": "babel dist/trtc-cloud.js --config-file --out-file dist/trtc.js", "build:wasm": "node ./build/esbuild --room wasm --prd && rollup -c build/rollup.config.mjs", "build": "node ./build/build.js", "tag": "node ./build/tag.js", "deprecate-beta-version": "node ./build/deprecate-beta-version.js", "version": "node ./build/update-package-version.js", "rollup": "rollup -c build/rollup.config.js", "lint": "./node_modules/.bin/eslint ./src", "fix": "./node_modules/.bin/eslint ./src --fix", "docs": "pnpm run docs:tsc || pnpm run docs:clean && pnpm run docs:build:zh-cn && pnpm run docs:build:en", "docs:build:zh-cn": "jsdoc -c build/jsdoc/jsdoc-zh-cn.json && node ./build/jsdoc/fix-doc-zh-cn.js", "docs:build:en": "jsdoc -c build/jsdoc/jsdoc.json && node ./build/jsdoc/fix-doc.js", "docs:revert-translate": "node ./build/jsdoc/revert-translate-doc2js.js > ./docs/en/translate/convert.output", "docs:clean": "node ./build/jsdoc/clean-doc.js", "docs:tsc": "tsc --outDir dist --target esnext src/main.ts src/common/error-code.ts src/common/rtc-error.ts ../sdk-core/src/common/rtc-error.ts", "test": "karma start --enableSPC=true", "test:safari": "pkill -9 Safari; pkill -9 osascript; (sleep 3; osascript ../../scripts/click-safari.scpt) & OSA_PID=$!; karma start --enableSPC=true --safari --log=4; kill $OSA_PID", "test:mpc": "karma start --enableSPC=false", "test:mpc:debug": "karma start --enableSPC=false --debug=true", "test:coverage": "karma start --coverage=true", "test:debug": "karma start --debug=true", "test:wasm": "karma start --wasm=true", "test:debug:wasm": "karma start --debug=true --wasm=true", "dts": "ts-node --esm ../../scripts/dts-generator.mts --out dist/npm-package/index.d.ts --depProject ../sdk-core --extern ./core.d.ts"}, "devDependencies": {"@babel/preset-env": "^7.21.4", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.3.0", "@rtc-plugin/basic-beauty": "workspace:*", "@rtc-plugin/beauty": "workspace:*", "@rtc-plugin/cdn-streaming": "workspace:*", "@rtc-plugin/cross-room": "workspace:*", "@rtc-plugin/custom-encryption": "workspace:*", "@rtc-plugin/device-detector": "workspace:*", "@rtc-plugin/virtual-background": "workspace:*", "@rtc-plugin/watermark": "workspace:*", "@trtc/cloud-sdk-types": "^1.0.1", "@types/dom-mediacapture-transform": "^0.1.1", "@types/dom-webcodecs": "^0.1.13", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "chokidar": "^4.0.3", "dayjs": "^1.11.13", "docdash-blue": "1.1.12", "esbuild": "^0.25.0", "eslint": "^8.21.0", "eslint-config-tencent": "^1.0.4", "eslint-plugin-prettier": "^4.2.1", "jsdoc": "^3.6.2", "jszip": "^3.10.1", "karma": "^6.4.4", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^2.1.0", "karma-esbuild": "^2.3.0", "karma-jasmine": "^2.0.1", "karma-jasmine-diff-reporter": "^2.0.1", "karma-parallel": "^0.3.1", "karma-rollup-preprocessor": "^7.0.0", "karma-safari-launcher": "^1.0.0", "karma-spec-reporter": "0.0.32", "karma-typescript": "^5.5.4", "prettier": "^2.7.1", "pretty": "^2.0.0", "replace": "^1.2.2", "rollup": "^2.78.1", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-polyfill-node": "^0.10.2", "rollup-plugin-progress": "^1.1.2", "rollup-plugin-terser": "^7.0.2", "typescript": "~5.0.4", "yargs": "^17.5.1"}}