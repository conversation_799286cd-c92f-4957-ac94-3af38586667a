#!/usr/bin/env node
const fs = require('fs');
// const define = require('../package.json')
const esbuild = require('esbuild');
const yargs = require('yargs');
const { hideBin } = require('yargs/helpers');
const { createServer, request } = require('http');
const { spawn } = require('child_process');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const path = require('path');

// 配置 dayjs
dayjs.extend(utc);
dayjs.extend(timezone);

// 获取北京时间的函数
function getBeiJingTime() {
  return dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');
}

const argv = yargs(hideBin(process.argv))
  .alias('a', 'assetsPath')
  .alias('d', 'dir')
  .alias('f', 'format')
  .alias('n', 'name')
  .options({
    room: {
      type: 'string',
    },
    prd: {
      type: 'boolean',
      describe: 'build for production',
    },
    inline: {
      type: 'boolean',
      describe: 'inline worker js and wasm',
    },
  }).parse();
const wasmURL = 'node_modules/trtc-js-sdk-wasm/wasm/av_processing.wasm';
const workerURL = 'node_modules/trtc-js-sdk-wasm/dist/worker.js';
// const audioCode = fs.readFileSync('node_modules/trtc-js-sdk-wasm/dist/audio.js', 'utf8').replace(/\n/g, '');
// 支持 esm iife 两种格式，umd 需要通过 rollup。
const format = (argv.format) || 'esm';
const name = (argv.name) || 'trtc';
const config = {
  define: {
    // __AUDIO_CODE__: `'${audioCode}'`,
    useWt: argv.ws ? 'false' : 'true',
    assetsPath: `'${argv.assetsPath || ""}'`,
    use_hw_dec: argv.hwdec ? 'true' : 'false',
    use_hw_enc: argv.hwenc ? 'true' : 'false',
    __BUILD_TIME__: JSON.stringify(getBeiJingTime()),
    use_inline_worker: argv.inline ? '"__INLINE__WORKER__"' : 'null',
    use_inline_wasm: argv.inline ? '"__INLINE__WASM__"' : 'null',
  },
  platform: 'browser',
  bundle: true,
  format,
  target: 'ES2015',
  metafile: true,
  sourcemap: !argv.prd,
  minify: argv.prd,
  // webrtc-adater
  // inject: argv.room === 'wasm' ? [] : ['node_modules/webrtc-adapter/dist/adapter_core5.js'],
  entryPoints: ['src/main.ts'],
  tsconfig: './tsconfig.json',
  globalName: "TRTC",
  outfile: `${argv.dir || 'dist'}/${name}${format === 'esm' ? '.esm.js' : '.js'}`,
  external: format === 'esm' ? ['webrtc-adapter'] : undefined,
  banner: (argv.prd ? undefined : {
    js: `
      (() => {
        const urlParams = new URLSearchParams(window.location.search);
        const lockHotUpdate = urlParams.get('lockHotUpdate');
        console.log('lockHotUpdate:', lockHotUpdate);
        if (!lockHotUpdate) {
          const eventSource = new EventSource("/esbuild");
          eventSource.onmessage = () => location.reload();
        }
      })();
    `
  }),
  plugins: [{
    name: 'iife',
    setup(build) {
      build.onEnd(async result => {
        if (result.errors.length > 0) return;
        if (argv.format == 'iife') {
          // 读取构建结果
          const s = fs.readFileSync(config.outfile, 'utf8');
          const lastSemi = s.lastIndexOf(';');
          const newContent = s.substring(0, lastSemi) + '.default' + s.substring(lastSemi);
          fs.writeFileSync(config.outfile, newContent);
        }
        if (argv.inline) {
          const raw1 = fs.readFileSync(workerURL, 'base64').replace(/\n/g, '');
          const raw3 = fs.readFileSync(wasmURL, 'base64').replace(/\n/g, '');
          fs.writeFileSync(config.outfile, fs.readFileSync(config.outfile, 'utf8')
            .replace('__INLINE__WORKER__', raw1)
            .replace('__INLINE__WASM__', raw3)
          );
        }
        console.log('build finished');
      }),
        build.onStart(() => {
          console.log('building...');
        });
    },
  }]
};

const clients = [];

function onBuild(error) {
  if (error instanceof Error) console.error('build failed:', error);
  else {
    if (error && error.metafile) esbuild.analyzeMetafile(error.metafile, {
      verbose: true
    }).then(console.log);
    console.log('build succeeded');
    clients.forEach((res) => res.write('data: update\n\n'));
    clients.length = 0;
    if (argv.inline) {
      const raw1 = fs.readFileSync(workerURL, 'base64').replace(/\n/g, '');
      const raw3 = fs.readFileSync(wasmURL, 'base64').replace(/\n/g, '');
      fs.writeFileSync(config.outfile, fs.readFileSync(config.outfile, 'utf8')
        .replace('__INLINE__WORKER__', raw1)
        .replace('__INLINE__WASM__', raw3)
      );
    }
  }
}
if (argv.prd) {
  esbuild.build(config).then(onBuild, onBuild);
} else {
  esbuild.context(config).then(ctx => {
    ctx.watch();
    // 创建静态资源服务器
    const staticDir = path.resolve(__dirname, '../../../examples/api-cloud-next');

    console.warn('page served: http://localhost:9988/rtc/index.html');
    createServer(async (req, res) => {
      const { url, method, headers } = req;

      // 添加CORS头到所有响应中
      setCorsHeaders(res);

      // 处理OPTIONS预检请求
      if (method === 'OPTIONS') {
        res.writeHead(204);
        res.end();
        return;
      }

      function tryFiles(...trys) {
        for (let i = 0; i < trys.length; i++) {
          const filePath = trys[i].split('?')[0];
          if (fs.existsSync(filePath)) {
            const content = fs.readFileSync(filePath);
            const contentType = getContentTypeByExtension(filePath);
            res.setHeader('Content-Type', contentType);
            res.setHeader('Cache-Control', 'no-cache');
            res.end(content);
            return;
          }
        }
        console.log('404 Not Found:', url);
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('404 Not Found');
        return;
      }
      // 处理 esbuild 的热更新
      if (req.url === '/esbuild')
        return clients.push(
          res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            Connection: 'keep-alive',
          })
        );

      let filePath = path.join(staticDir, url).split('?')[0];

      if (req.url.endsWith('.mp3')) {
        res.setHeader('Content-Type', 'audio/mpeg');
        res.setHeader('Accept-Ranges', 'bytes');
      }
      if (/trtc\.js$/.test(filePath)) {
        filePath = config.outfile;
      } else if (/trtc\.js\.map$/.test(filePath)) {
        filePath = config.outfile + '.map';
      } else if (url.includes("plugins")) {
        tryFiles('dist' + url, 'dist/npm-package' + url);
        return;
      } else if (url.startsWith('/static')) {
        const rest = url.substring(7);
        tryFiles('dist' + rest, 'dist/npm-package/assets' + rest);
        return;
      }

      // 提供静态文件服务
      fs.stat(filePath, (err, stats) => {
        if (err) {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('404 Not Found');
          return;
        }

        if (stats.isDirectory()) {
          filePath = path.join(filePath, 'index.html');
        }

        fs.readFile(filePath, (err, data) => {
          if (err) {
            res.writeHead(500, { 'Content-Type': 'text/plain' });
            res.end('500 Internal Server Error');
            return;
          }

          const ext = path.extname(filePath).toLowerCase();
          const contentType = getContentTypeByExtension(ext.substring(1));

          res.writeHead(200, {
            'Content-Type': contentType,
            'Cache-Control': 'no-cache'
          });
          res.end(data);
        });
      });
    }).listen(9988);

    setTimeout(() => {
      const op = { darwin: ['open'], linux: ['xdg-open'], win32: ['cmd', '/c', 'start'] };
      const ptf = process.platform;
      if (clients.length === 0) spawn(op[ptf][0], [`http://localhost:9988/rtc/index.html${argv.room === 'wasm' ? '?version=wasm-dev' : ''}`]);
    }, 1000); //open the default browser only if it is not opened yet
  });
}

// 添加一个根据文件扩展名获取 Content-Type 的辅助函数
function getContentTypeByExtension(filePath) {
  const ext = typeof filePath === 'string' ? filePath.split('.').pop().toLowerCase() : '';
  const types = {
    'html': 'text/html',
    'css': 'text/css',
    'js': 'application/javascript',
    'json': 'application/json',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'svg': 'image/svg+xml',
    'mp3': 'audio/mpeg',
    'mp4': 'video/mp4',
    'wasm': 'application/wasm'
  };
  return types[ext] || 'application/octet-stream';
}

// 添加一个设置CORS头的辅助函数
function setCorsHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Max-Age', '86400');
}