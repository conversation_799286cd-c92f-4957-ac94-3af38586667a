#!/usr/bin/env node
const fs = require('fs');
const esbuild = require('esbuild');
const yargs = require('yargs');
const { hideBin } = require('yargs/helpers');
const { createServer, request } = require('http');
const { spawn } = require('child_process');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const path = require('path');
// 尝试加载 chokidar，如果没有则使用原生 fs
let chokidar;
try {
  chokidar = require('chokidar');
} catch (e) {
  console.log('[worker-watcher] chokidar not installed, will use fs.watchFile as fallback');
}

// 配置 dayjs
dayjs.extend(utc);
dayjs.extend(timezone);

// 获取北京时间的函数
function getBeiJingTime() {
  return dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');
}

const argv = yargs(hideBin(process.argv))
  .alias('a', 'assetsPath') // assetsPath 仍然保留，但不在 define 中使用
  .alias('d', 'dir')
  .alias('f', 'format')
  .alias('n', 'name')
  .options({
    prd: {
      type: 'boolean',
      describe: 'build for production',
    }
  }).parse();

// 支持 esm iife 两种格式，umd 需要通过 rollup。
const format = (argv.format) || 'esm';
const name = (argv.name) || 'trtc';
// 定义 worker 文件路径，使用项目中的 worker.ts
const workerFilePath = path.resolve(__dirname, '../../sdk-core/src/worker/index.ts');
console.log(`[worker] Using worker file: ${workerFilePath}`);

// 全局变量存储编译上下文和 worker 依赖
let buildContext = null;
let workerDependencies = new Set();
let workerWatcher = null;
const watchedFiles = new Set();

// 设置 worker 文件监听器函数
function setupWorkerWatcher() {
  console.log(`[worker-watcher] Setting up worker file watcher...`);

  if (workerWatcher) {
    console.log(`[worker-watcher] Closing existing watcher...`);
    workerWatcher.close();
  }

  // 清理之前的 fs.watchFile
  if (watchedFiles.size > 0) {
    console.log(`[worker-watcher] Cleaning up ${watchedFiles.size} previous fs.watchFile watchers...`);
    watchedFiles.forEach(filePath => {
      try {
        fs.unwatchFile(filePath);
      } catch (e) {
        console.log(`[worker-watcher] Error unwatching ${filePath}:`, e.message);
      }
    });
    watchedFiles.clear();
  }

  if (workerDependencies.size === 0) {
    console.log(`[worker-watcher] No worker dependencies to watch yet.`);
    return;
  }

  const watchPaths = Array.from(workerDependencies);
  console.log(`[worker-watcher] Setting up file watcher for ${watchPaths.length} worker dependency files:`);

  // 验证所有路径是否存在
  const validPaths = [];
  const invalidPaths = [];
  watchPaths.forEach((filePath, index) => {
    if (fs.existsSync(filePath)) {
      validPaths.push(filePath);
      console.log(`  ${index + 1}. ✓ ${filePath}`);
    } else {
      invalidPaths.push(filePath);
      console.log(`  ${index + 1}. ✗ ${filePath} (file not found)`);
    }
  });

  if (invalidPaths.length > 0) {
    console.warn(`[worker-watcher] Warning: ${invalidPaths.length} files not found and will be skipped.`);
  }

  if (validPaths.length === 0) {
    console.warn(`[worker-watcher] No valid files to watch.`);
    return;
  }

  // 检查是否有 chokidar
  if (chokidar) {
    console.log(`[worker-watcher] Using chokidar for file watching (${validPaths.length} files)`);
    workerWatcher = chokidar.watch(validPaths, {
      ignored: /node_modules/,
      persistent: true
    });

    workerWatcher.on('change', (path) => {
      console.log(`[worker-watcher] Worker dependency changed: ${path}`);
      console.log(`[worker-watcher] Triggering rebuild...`);

      // 手动触发重新构建
      if (buildContext) {
        buildContext.rebuild().then(() => {
          console.log(`[worker-watcher] Rebuild completed`);
          clients.forEach((res) => res.write('data: update\n\n'));
        }).catch(err => {
          console.error(`[worker-watcher] Rebuild failed:`, err);
        });
      } else {
        console.error(`[worker-watcher] No build context available for rebuild`);
      }
    });

    workerWatcher.on('error', error => {
      console.error(`[worker-watcher] Watcher error:`, error);
    });

    workerWatcher.on('ready', () => {
      console.log(`[worker-watcher] Chokidar watcher is ready, monitoring ${validPaths.length} files`);
    });
  } else {
    // 使用原生 fs.watchFile
    console.log(`[worker-watcher] Using fs.watchFile as fallback (${validPaths.length} files)`);
    validPaths.forEach(watchPath => {
      try {
        watchedFiles.add(watchPath);
        fs.watchFile(watchPath, { interval: 1000 }, () => {
          console.log(`[worker-watcher] Worker dependency changed: ${watchPath}`);
          if (buildContext) {
            buildContext.rebuild().then(() => {
              console.log(`[worker-watcher] Rebuild completed`);
              clients.forEach((res) => res.write('data: update\n\n'));
            }).catch(err => {
              console.error(`[worker-watcher] Rebuild failed:`, err);
            });
          } else {
            console.error(`[worker-watcher] No build context available for rebuild`);
          }
        });
      } catch (err) {
        console.error(`[worker-watcher] Failed to watch ${watchPath}:`, err);
      }
    });
    console.log(`[worker-watcher] fs.watchFile setup completed for ${watchedFiles.size} files`);
  }
}

const config = {
  define: {
    // 仅保留 __BUILD_TIME__
    __BUILD_TIME__: JSON.stringify(getBeiJingTime()),
  },
  platform: 'browser',
  bundle: true,
  format,
  target: 'ES2015',
  metafile: true,
  sourcemap: !argv.prd,
  minify: argv.prd,
  entryPoints: ['src/main.ts'],
  tsconfig: './tsconfig.json',
  globalName: "TRTC",
  outfile: `${argv.dir || 'dist'}/${name}${format === 'esm' ? '.esm.js' : '.js'}`,
  external: format === 'esm' ? ['webrtc-adapter'] : undefined,
  banner: (argv.prd ? undefined : {
    js: `
      (() => {
        const urlParams = new URLSearchParams(window.location.search);
        const lockHotUpdate = urlParams.get('lockHotUpdate');
        console.log('lockHotUpdate:', lockHotUpdate);
        if (!lockHotUpdate) {
          const eventSource = new EventSource("/esbuild");
          eventSource.onmessage = () => location.reload();
        }
      })();
    `
  }),
  plugins: [{
    name: 'iife-handler',
    setup(build) {
      build.onEnd(async result => {
        if (result.errors.length > 0) {
          console.error('Build failed with errors.');
          return;
        }
        if (argv.format == 'iife') {
          const s = fs.readFileSync(config.outfile, 'utf8');
          const lastSemi = s.lastIndexOf(';');
          if (lastSemi === s.length - 1 || (s.endsWith('\n') && lastSemi === s.length - 2)) {
            const newContent = s.substring(0, lastSemi) + '.default' + s.substring(lastSemi);
            fs.writeFileSync(config.outfile, newContent);
          } else {
            console.warn(`[iife-handler] Could not append .default to IIFE output for ${config.outfile} as it did not end with a semicolon as expected.`);
          }
        }
        console.log('build finished', config.outfile);
      });
      build.onStart(() => {
        console.log('building...');
      });
    },
  },
  {
    name: 'inline-worker-as-text',
    setup(build) {
      build.onResolve({ filter: /worker\/index\.ts$/ }, (args) => {
        if (args.path.endsWith('worker/index.ts')) {
          console.log(`[worker-plugin] Intercepting import for worker: ${args.path}`);
          return {
            path: workerFilePath,
            namespace: 'worker-inline-text-namespace',
          };
        }
        return undefined;
      });

      build.onLoad({ filter: /.*/, namespace: 'worker-inline-text-namespace' }, async (args) => {
        console.log(`[worker-plugin] Compiling worker ${args.path} to string...`);
        try {
          const workerBuildResult = await esbuild.build({
            entryPoints: [args.path],
            bundle: true,
            write: false,
            format: 'esm',
            platform: 'browser',
            minify: argv.prd,
            target: build.initialOptions.target || ['es2017'],
            tsconfig: build.initialOptions.tsconfig,
            metafile: true, // 启用 metafile 以获取依赖信息
          });
          const compiledJs = workerBuildResult.outputFiles[0].text;
          console.log(`[worker-plugin] Worker compiled, size: ${compiledJs.length} bytes.`);

          // 收集 worker 的所有依赖文件并更新全局依赖集合
          if (workerBuildResult.metafile) {
            const inputs = workerBuildResult.metafile.inputs;
            const newDependencies = new Set();
            console.log(`[worker-plugin] Debug: args.path = ${args.path}`);
            console.log(`[worker-plugin] Debug: esbuild working directory = ${process.cwd()}`);

            // 获取构建配置中的 tsconfig 目录作为基准
            const tsconfigDir = path.dirname(build.initialOptions.tsconfig || path.resolve(process.cwd(), 'tsconfig.json'));
            console.log(`[worker-plugin] Debug: tsconfig directory = ${tsconfigDir}`);

            for (const inputPath in inputs) {
              console.log(`[worker-plugin] Debug: processing input = ${inputPath}`);
              let absolutePath;
              if (path.isAbsolute(inputPath)) {
                absolutePath = inputPath;
              } else {
                // 相对路径相对于 tsconfig 目录解析
                absolutePath = path.resolve(tsconfigDir, inputPath);
              }

              // 验证文件是否存在
              if (fs.existsSync(absolutePath)) {
                console.log(`[worker-plugin] Debug: ✓ resolved path = ${absolutePath}`);
                newDependencies.add(absolutePath);
              } else {
                console.log(`[worker-plugin] Debug: ✗ file not found = ${absolutePath}`);
                // 尝试其他可能的基准目录
                const altPath = path.resolve(process.cwd(), inputPath);
                if (fs.existsSync(altPath)) {
                  console.log(`[worker-plugin] Debug: ✓ alternative path = ${altPath}`);
                  newDependencies.add(altPath);
                } else {
                  console.log(`[worker-plugin] Debug: ✗ alternative path also not found = ${altPath}`);
                }
              }
            }

            // 更新依赖列表
            const oldSize = workerDependencies.size;
            workerDependencies = newDependencies;
            console.log(`[worker-plugin] Updated worker dependencies: ${oldSize} -> ${workerDependencies.size} files:`);

            // 打印所有监控的文件列表
            const sortedFiles = Array.from(workerDependencies).sort();
            sortedFiles.forEach((file, index) => {
              console.log(`  ${index + 1}. ${file}`);
            });

            // 如果是在开发模式下且有新的依赖，设置文件监听器
            if (!argv.prd && newDependencies.size > 0) {
              setTimeout(() => {
                console.log(`[worker-plugin] Setting up file watcher with ${newDependencies.size} dependencies...`);
                setupWorkerWatcher();
              }, 100);
            }
          }

          return {
            contents: compiledJs,
            loader: 'text',
            resolveDir: path.dirname(args.path),
          };
        } catch (e) {
          console.error(`[worker-plugin] Error compiling worker:`, e);
          return { errors: [{ text: `Failed to compile worker: ${e.message}` }] };
        }
      });
    }
  }]
};

const clients = [];

function onBuild(error) {
  if (error instanceof Error) console.error('build failed:', error);
  else {
    if (error && error.metafile && !error.errors?.length) {
      esbuild.analyzeMetafile(error.metafile, {
        verbose: true
      }).then(console.log);
    } else if (error && error.errors?.length) {
      console.error('build failed with errors:', error.errors);
      return;
    }
    console.log('build succeeded');
    clients.forEach((res) => res.write('data: update\n\n'));
    clients.length = 0;
  }
}

if (argv.prd) {
  esbuild.build(config).then(onBuild, onBuild);
} else {
  esbuild.context(config).then(ctx => {
    buildContext = ctx;
    ctx.watch();

    const staticDir = path.resolve(__dirname, '../../../examples/api-cloud-next');

    console.warn('page served: http://localhost:9988/rtc/index.html');
    createServer(async (req, res) => {
      const { url, method, headers } = req;

      setCorsHeaders(res);

      if (method === 'OPTIONS') {
        res.writeHead(204);
        res.end();
        return;
      }

      function tryFiles(...trys) {
        for (let i = 0; i < trys.length; i++) {
          const filePath = trys[i].split('?')[0];
          if (fs.existsSync(filePath)) {
            const content = fs.readFileSync(filePath);
            const contentType = getContentTypeByExtension(filePath);
            res.setHeader('Content-Type', contentType);
            res.setHeader('Cache-Control', 'no-cache');
            res.end(content);
            return;
          }
        }
        console.log('404 Not Found:', url);
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('404 Not Found');
        return;
      }

      if (req.url === '/esbuild')
        return clients.push(
          res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            Connection: 'keep-alive',
          })
        );

      let filePath = path.join(staticDir, url).split('?')[0];

      if (req.url.endsWith('.mp3')) {
        res.setHeader('Content-Type', 'audio/mpeg');
        res.setHeader('Accept-Ranges', 'bytes');
      }
      if (/trtc\.js$/.test(filePath)) {
        filePath = config.outfile;
      } else if (/trtc\.js\.map$/.test(filePath)) {
        filePath = config.outfile + '.map';
      } else if (url.includes("plugins")) {
        tryFiles('dist' + url, 'dist/npm-package' + url);
        return;
      } else if (url.startsWith('/static')) {
        const rest = url.substring(7);
        tryFiles('dist' + rest, 'dist/npm-package/assets' + rest);
        return;
      }

      fs.stat(filePath, (err, stats) => {
        if (err) {
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('404 Not Found');
          return;
        }

        if (stats.isDirectory()) {
          filePath = path.join(filePath, 'index.html');
        }

        fs.readFile(filePath, (err, data) => {
          if (err) {
            res.writeHead(500, { 'Content-Type': 'text/plain' });
            res.end('500 Internal Server Error');
            return;
          }

          const ext = path.extname(filePath).toLowerCase();
          const contentType = getContentTypeByExtension(ext.substring(1));

          res.writeHead(200, {
            'Content-Type': contentType,
            'Cache-Control': 'no-cache'
          });
          res.end(data);
        });
      });
    }).listen(9988);

    setTimeout(() => {
      const op = { darwin: ['open'], linux: ['xdg-open'], win32: ['cmd', '/c', 'start'] };
      const ptf = process.platform;
      if (clients.length === 0) spawn(op[ptf][0], [`http://localhost:9988/rtc/index.html`]);
    }, 1000);
  });
}

function getContentTypeByExtension(filePath) {
  const ext = typeof filePath === 'string' ? filePath.split('.').pop().toLowerCase() : '';
  const types = {
    'html': 'text/html',
    'css': 'text/css',
    'js': 'application/javascript',
    'json': 'application/json',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'svg': 'image/svg+xml',
    'mp3': 'audio/mpeg',
    'mp4': 'video/mp4',
    'wasm': 'application/wasm'
  };
  return types[ext] || 'application/octet-stream';
}

function setCorsHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Max-Age', '86400');
}
