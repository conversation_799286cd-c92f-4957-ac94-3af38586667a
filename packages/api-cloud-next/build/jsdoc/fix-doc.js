const replace = require('replace');
const fs = require('fs');
const path = require('path');
const apiPath = path.join(__dirname, '../../', 'docs/en', 'api');
const pretty = require('pretty');
const translatePath = path.join(__dirname, '../../', 'docs/en', 'translate');

const files = fs.readdirSync(apiPath);
const replacements = [
  {
    find: '</title>',
    replace: `</title>
    <meta name="description" content="RTC SDK Documentation. Run a demo within one minute and build solutions for audio/video calls or interactive live streaming within 30 minutes"/>
    <meta name="keywords" content="call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    `
  },
  // Title 统一加上 腾讯云 RTC SDK
  {
    find: '<title>(.+)</title>',
    replace: '<title>Tencent RTC SDK - $1</title>'
  },
  {
    find: '<header>',
    replace: '<header style="display:none">'
  },
  {
    find: '<h4 class="name" id="Client">',
    replace: '<h4 class="name" style="display:none" id="Client">'
  },
  {
    find: '<p>!</p>',
    replace: '<p>Notice：</p>'
  },
  {
    find: '<blockquote',
    replace:
      '<blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;"'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="Client.html#startPublishCDNStream">startPublishCDNStream</a></li>',
    replace: ''
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="Client.html#stopPublishCDNStream">stopPublishCDNStream</a></li>',
    replace: ''
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="Client.html#startMixTranscode">startMixTranscode</a></li>',
    replace: ''
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="Client.html#stopMixTranscode">stopMixTranscode</a></li>',
    replace: ''
  },
  {
    find: '<li data-type=\'member\' style=\'display: none;\'><a href="module-ErrorCode.html#.START_PUBLISH_CDN_FAILED">START_PUBLISH_CDN_FAILED</a></li>',
    replace: ''
  },
  {
    find: '<li data-type=\'member\' style=\'display: none;\'><a href="module-ErrorCode.html#.STOP_PUBLISH_CDN_FAILED">STOP_PUBLISH_CDN_FAILED</a></li>',
    replace: ''
  },
  {
    find: '<li data-type=\'member\' style=\'display: none;\'><a href="module-ErrorCode.html#.START_MIX_TRANSCODE_FAILED">START_MIX_TRANSCODE_FAILED</a></li>',
    replace: ''
  },
  {
    find: '<li data-type=\'member\' style=\'display: none;\'><a href="module-ErrorCode.html#.STOP_MIX_TRANSCODE_FAILED">STOP_MIX_TRANSCODE_FAILED</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-00-info-update-guideline.html">Upgrade Guide</a>',
    replace:
      '<ul><li style="margin:10px 0 5px 0">Related Information</li><li><a href="tutorial-00-info-update-guideline.html">Upgrade Guide</a>'
  },
  {
    find: '<li><a href="tutorial-10-basic-get-started-with-demo.html">Demo Quick Run</a>',
    replace:
      '</ul><ul><li style="margin:10px 0 5px 0">Basic Tutorials</li><li><a href="tutorial-10-basic-get-started-with-demo.html">Demo Quick Run</a>'
  },
  {
    find:
      '<li><a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Rendering</a>',
    replace:
      '</ul><ul><li style="margin:10px 0 5px 0">Advanced Tutorials</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Rendering</a>'
  },
  {
    find: '<pre class="prettyprint',
    replace: '<pre class="highlight lang-javascript'
  },

  // API 分隔符
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#.create">create</a></li>',
    replace: `<li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>`
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>',
    replace: '<li><span class="nav-separator">Local Audio</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>',
    replace: '<li><span class="nav-separator">Local Video</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>',
    replace: '<li><span class="nav-separator">Local Screen Share</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>',
    replace: '<li><span class="nav-separator">Remote Video</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>',
    replace: '<li><span class="nav-separator">Remote Audio</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startPlugin">startPlugin</a></li>',
    replace: '<li><span class="nav-separator">Plugins</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startPlugin">startPlugin</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>',
    replace: '<li><span class="nav-separator">Others</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>',
    replace: `<li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>`
  },
  // 将《自动播放受限处理建议》文章放到 最佳实践 栏目中。
  // FIXME: 英文文档的 sdk 接口文档部分（例如：Client.join 接口的文档），无法显示 最佳实践 栏目，需要修复。
  {
    find: '<li><a href="tutorial-21-advanced-auto-play-policy.html">Handle Autoplay Restriction</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-27-advanced-small-stream.html">Multi-Person Video Calls</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-34-advanced-proxy.html">Handle Firewall Restriction</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-35-advanced-ai-denoiser.html">Implement AI noise reduction</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-39-advanced-video-decoder.html">Enable Video Decoder Plugin</a></li>',
    replace: ''
  },{
    find: '<li><a href="tutorial-37-advanced-voice-changer.html">Enable Voice Changer</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-36-advanced-virtual-background.html">Enable Virtual Background</a></li>',
    replace:
      `<li><a href="https://trtc.io/document/66148?product=rtcengine&menulabel=serverfeaturesapis">Speech to Text</a></li>
        <li><a href="tutorial-35-advanced-ai-denoiser.html">Implement AI noise reduction</a></li>
        <li><a href="tutorial-36-advanced-virtual-background.html">Enable Virtual Background</a></li>
        <li><a href="tutorial-37-advanced-voice-changer.html">Enable Voice Changer</a></li>
        <li><a href="tutorial-39-advanced-video-decoder.html">Enable Video Decoder Plugin</a></li>
      </ul>
      <ul><li style="margin:10px 0 5px 0">Best Practices</li>
        <li><a href="tutorial-27-advanced-small-stream.html">Multi-Person Video Calls</a></li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">Handle Autoplay Restriction</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">Handle Firewall Restriction</a></li>
      </ul>`
  },
  {
    find: '<script src="scripts/nav.js" defer></script>',
    replace: `<script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>`
  },
  {
    find: '<nav>',
    replace: `<nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="zh-cn" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="切换至中文" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/en/', '/zh-cn/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    <a href="https://t.me/+EPk6TMZEZMM5OGY1" title="contact us on telegram" class="contact_us_tg" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1685076466271" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="1951" width="24" height="24"><path d="M679.424 746.862l84.005-395.996c7.424-34.852-12.581-48.567-35.438-40.009L234.277 501.138c-33.72 13.13-33.134 32-5.706 40.558l126.282 39.424 293.156-184.576c13.714-9.143 26.295-3.986 16.018 5.157L426.898 615.973l-9.143 130.304c13.13 0 18.871-5.706 25.71-12.581l61.696-59.429 128 94.282c23.442 13.129 40.01 6.29 46.3-21.724zM1024 512c0 282.843-229.157 512-512 512S0 794.843 0 512 229.157 0 512 0s512 229.157 512 512z" fill="#1296DB" p-id="1952"/></svg></a>
    </div>
    `
  },
  // 外链链接用新窗口打开
  {
    find: '<a href="http',
    replace: '<a target="_blank" href="http'
  },
  {
    find: '<a href="tutorial-11-basic-video-call.html">Quick Start Call</a>',
    replace: '<a href="tutorial-11-basic-video-call.html">Quick Start Call<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a>'
  },
  {
    find: '<li><a href="tutorial-16-basic-screencast-old.html">16-basic-screencast-old</a></li>',
    replace: ''
  },
  // 语言自动切换
  {
    find:
      '</html>',
    replace:
      `</html>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', \`/\${lang}/\`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      `
  },
  // 代码块右上角增加 copy 按钮
  {
    find:
      '</html>',
    replace:
      `</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>`
  }
];

files.forEach(file => {
  if (path.extname(file) === '.html') {
    // console.log(__dirname, file, path.join(apiPath, file));
    // console.log(fs.readFileSync(path.join(apiPath, file)).toString());
    const htmlPath = path.join(apiPath, file);
    const htmlContent = fs.readFileSync(htmlPath).toString();
    fs.writeFile(htmlPath, pretty(htmlContent, { ocd: true }), err => {
      if (err) {
        return console.error(err);
      }
      console.log(htmlPath, '格式化成功！');

      replacements.forEach(obj => {
        replace({
          regex: obj.find,
          replacement: obj.replace,
          paths: [htmlPath],
          recursive: false,
          silent: true
        });
      });
    });
  }
});

function copyDir(fromPath, toPath) {
  if (!fs.existsSync(toPath)) {
    fs.mkdirSync(toPath);
  }
  const dirsPath = fs.readdirSync(fromPath);
  dirsPath.forEach(dirPath => {
    const from = path.join(fromPath, dirPath);
    const to = path.join(toPath, dirPath);
    if (fs.statSync(from).isDirectory()) {
      copyDir(from, to);
    } else {
      fs.copyFileSync(from, to);
    }
  });
}

// 复制 assets 资源
copyDir(path.resolve('./docs/en/tutorials/assets'), path.resolve('./docs/en/api/assets'));
