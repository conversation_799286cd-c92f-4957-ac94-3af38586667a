The version number `major.minor.patch` follows these rules:

- major: Major version number. If there is a major version refactoring, this field will be incremented. Usually, the interfaces between major versions are not compatible.
- minor: Minor version number. The interfaces between minor version numbers are compatible. If there are interface additions or optimizations, this field will be incremented.
- patch: Patch number. If there are functional improvements or bug fixes, this field will be incremented.

> !
> - We recommend that you update to the latest version in a timely manner to obtain better product stability and online support.
> - For version upgrade precautions, please refer to: [Upgrade Guide](./tutorial-00-info-update-guideline.html).

## Version 5.10.1 @2025.05.15

**Bug Fixed**

- Fixed an issue where switching cameras occasionally caused a black screen after enabling small stream, beauty effects, or virtual background.
- Fixed an issue where updating camera resolution failed when used with the WebAR SDK.
- Fixed an issue where streams could not be played in iPad Chrome when accessing the desktop website.
- Fixed an occasional black screen issue when using the `receiveWhenViewVisible` parameter.

## Version 5.10.0 @2025.04.17

**Breaking Changed**

- The plugin format has been changed from `iife` to `umd`, supporting more integration environments. If you load plugins via the `<script>` tag, please note that you need to update the plugin file name during the upgrade.
- The wasm resource files in the npm package have been unified and moved to the `assets` directory. You can directly deploy the `assets` directory during deployment.

**Feature**

- Support [Voice Changer Plugin](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-37-advanced-voice-changer.html).

**Improvement**

- Optimized the downgrade strategy for the TRTCVideoDecoder plugin.
- Optimized Android H264 detection logic.
- Optimized the ear return logic to play the processed audio.

**Bug Fixed**

- Fixed an error that occurred when reusing WebSocket and switching userId to enter a room.
- Fixed an issue where the video frame was cropped during 360p capture on PC Chrome. To avoid this, specify the parameter: `TRTC.startLocalVideo({ option: { avoidCropping: true }})`.

## Version 5.9.2 @2025.04.10

**Improvement**

- [trtc.startRemoteVideo](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#startRemoteVideo) added poster parameter.
- Improved call quality under poor network conditions.

## Version 5.9.1 @2025.03.07

*Bug Fixed*

- Fixed an issue where audio was lost on iOS after being interrupted by other apps playing sound.
- Fixed uneven audio callback frequency after the page was moved to the background.
- Fixed an issue where dynamically updating the resolution could cause the virtual background to fail.

## Version 5.9.0 @2025.02.17

**Features**

- Added [Video Decoder Plugin](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-39-advanced-video-decoder.html) to improve decoding success rate.
- Added audio route switching between headset and speaker for Android devices. See:[TRTC.setCurrentSpeaker](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#.setCurrentSpeaker).
- Added video first frame callback event. See: [FIRST_VIDEO_FRAME](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/module-EVENT.html#.FIRST_VIDEO_FRAME).

**Improvements**

- Reduced ultra-fast startup time. For optimal quick launch experience, contact technical support.
- Optimized volume calculation logic to support volume retrieval under HTTP protocol.
- Add resume method for the callback parameter of [AUTOPLAY_FAILED](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/module-EVENT.html#.AUTOPLAY_FAILED).

**Bug Fixes**

- Fixed low-probability no-audio issue in iOS.
- Fixed SEI message loss issue in Safari & Firefox.
- Fixed untriggered autoplay failed event in specific scenarios.
- Fixed occasional issue that muteRemoteAudio does not working.
- Fixed audio-only call failures in environments without H.264 support.

## Version 5.8.6 @2024.12.27

**Improvement**

- Optimize the reconnection logic to improve the success rate of reconnection.
- Optimize the setRemoteAudioVolume interface to support setting the volume before playing the audio stream.
- Avoid the issue of no audio on some Honor devices.
- Avoid the issue of memory leakage caused by enabling watermark in iOS 16 & 18.

**Bug Fixed**

- Fix the issue of no audio when screen sharing + system audio.
- Fix the issue of occasional rendering distortion after dynamically updating the resolution in iOS 16.
- Fix the issue of occasional AudioContext error in iOS WKWebview.
- Fix the issue of empty pcm data in audio-frame event after switching microphone.

## Version 5.8.5 @2024.12.03

**Breaking Changed**

- The Debug plugin becomes a built-in plugin and does not need to be imported externally. Reference:[Debug plugin](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-18-basic-debug.html).

**Feature**

- Support screen sharing on main stream.
- The Debug plugin supports to dump audio & video.

**Improvement**

- Improved volume callback accuracy and compatibility.

**Bug Fixed**

- Fixed the no audio issue after occasionally sharing system audio.
- Fixed the loading SDK error in Android X5 kernel.
- Fixed compatibility issues with WebAR SDK in iOS 16.
- Fixed an issue where AI denoiser might not work if starting AI denoiser immediately after calling trtc.startLocalAudio.
- Fixed the no audio issue with frequent enterRoom & exitRoom.
- Fixed the video lag issue when using CrossRoom plugin.
- Fixed the video rendering issue with enabling virtual background when switching between front and background.
  
## Version 5.8.3 @2024.10.25

**Feature**

- Add [AUDIO_FRAME](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/module-EVENT.html#.AUDIO_FRAME) event. You can get the microphone pcm data from this event.

**Improvement**

- Improve the rendering performance in Safari.

**Bug Fixed**

- Fixed the issue that the non-specified device may be captured when capturing device.
- Fixed an issue of CDNStreaming plugin that the mixing stream may be failed when the roomId is not fill in.

## Version 5.8.2 @2024.10.11
 
**Improvement**

- Reduce the time taken to enter the room.
- Improve the stability of the virtual background plugin.

**Bug Fixed**
 
- Fixed an no audio issue when calling trtc.stopScreenShare.
- Fixed an occasional issue that CrossRoom plugin cannot start again.
- Fixed an occasional issue that the custom messages being lost.

## Version 5.8.1 @2024.09.12

**Feature**

- Support sending and receiving SEI messages in Safari and Firefox.
- Add [VirtualBackground.isSupported](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-36-advanced-virtual-background.html) method to detect whether the current browser supports VirtualBackground plugin.

**Improvement**

- Optimize type declaration files.
- Improve the browser compatibility(Firefox and Safari) of VirtualBackground plugin.

**Bug Fixed**

- Fix the occasional issue of custom message loss.
- Fixed the occasional issue that the remote video was not playing in Firefox.
- Fixed the issue that cannot publish video with 15fps+ in Safari.
- Fixed the issue that removing the camera view would cause the stream to stop publishing in Safari 16.

## Version 5.8.0 @2024.08.23

**Feature**

- Support [CrossRoom](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-30-advanced-cross-room-link.html) Plugin.
- Support [DeviceDetector](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-23-advanced-support-detection.html) Plugin.
- Support [Debug](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-18-basic-debug.html) Plugin.

**Bug Fixed**

- Fix the issue that publishing video failed after enableSEI occasionally.
- Fix the issue that the resolution of screen sharing is higher than setting in Firefox.

## Version 5.7.1 @2024.08.07

**Bug Fixed**

- Fix the issue of remote video rendering in iOS 17.
- Fix the issue that the unifiedProxy does not works.

## Version 5.7.0 @2024.07.19

**Feature**

- Support sending & receiving sei message in sub stream.

**Improvement**

- The preview box will only display the current page when screen sharing using the captureElement parameter.
- Avoid the [chrome issue](https://issues.chromium.org/issues/330055211) that unplug the headset will cause no audio issue in Android Webview.
- Improve the detection logic of h264 capability.

**Bug Fixed**

- Fix the issue that cannot see remote video in the browser that support h264 decoding, but not h264 encoding.

## Version 5.6.3 @2024.06.28

**Feature**

- Support listening to audio playback progress events in AudioMixer plugin.
- Support set blur level in VirtualBackground plugin

**Improvement**

- Improve the success rate of resuming normal calls after interrupting iOS calls.
- Improve the success rate of audio autoplay in iOS.

**Bug Fixed**

- Fixed the occasional reconnection issue in Chrome M91-.
- Fixed the audio lag issue after remote user mute & unmute mircrophone.
- Fixed the issue that remote publishing screen share event was mistakenly thrown in certain scenarios.

## Version 5.6.2 @2024.06.07

**Improvement**

- Reduce the video or audio lag for the audience.
- Improve the success rate of reconnection.

**Bug Fixed**

- Fixed the issue that capturing camera 1920 * 1280 failed in Mac Safari.
- Fixed a occasional issue that cannot resume audio after autoplay failed in mobile chrome.
- Fixed a occasional issue that cannot receive remote audio.
- Fixed a occasional issue that muteRemoteAudio throws an abort error.

## Version 5.6.1 @2024.05.23

**Bug Fixed**

- Fixed a no audio issue when autoReceiveAudio disabled.

## Version 5.6.0 @2024.05.17

**Breaking Changed**

Set the default value of `autoReceiveVideo` to false, refer to: [Upgrade Guide](./tutorial-00-info-update-guideline.html).

**Feature**

- Support [trtc.sendCustomMessage](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#sendCustomMessage).

**Bug Fixed**

- Fixed the issue that startRemoteVideo got error in Chrome 123.
- Fixed the issue that enter room failed in iOS 12.0.
- Fixed the issue that the encoding mirror occasionally fails.
- Fixed the issue that the AudioMixer plugin loop would not work.

## Version 5.5.2 @2024.04.29

**Improvement**

- Speed ​​up loading by deploying model files yourself, refer to: [Enable Visual Background](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-36-advanced-virtual-background.html).
- Avoid the video flicker issue in iOS 17, refer to: [webkit bug](https://bugs.webkit.org/show_bug.cgi?id=230532).

**Bug Fixed**

- Fixed the issue that getting error occasionally when subscribing remote small video in Chrome M123+.
- Fixed the issue that playing remote video failed.

## Version 5.5.1 @2024.04.12

**Improvement**

- Improve the success rate of reconnection.
- Improve the success rate of recapturing.

**Bug Fixed**

- Fix the issue that video was not playing in iOS 15.1.
- Fix the issue that screen sharing was publishing on the main stream but not sub stream after calling `trtc.updateScreenShare({ publish: true })`
- Fix the issue that the bitrate was not 128kbps when setting the audio profile to `TRTC.TYPE.AUDIO_PROFILE_HIGH`.
- Fix the issue that the base64 imageUrl was unable to set to the WaterMark plugin.

## Version 5.5.0 @2024.03.29

**Feature**

- Add [Beauty](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-28-advanced-beauty.html) plugin.

**Improvement**

- Optimize the capability of the AI Denoiser plugin in mobile phone.
- Improve the success rate of media recapturing.

**Bug Fixed**

- Fixed the issue that the preview of local video is black in iOS 16.
- Fixed the issue that the user cannot hear the sound of remote audio occasionally in iOS 14.
- Fixed the issue that startLocalAudio throws TypeError occasionally.

## Version 5.4.3 @2024.03.15

**Feature**

- Added support for passing MediaStreamTrack to the [AudioMixer](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-22-advanced-audio-mixer.html) plugin.
- Added support for calling [trtc.getAudioTrack](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#getAudioTrack) to obtain the screen sharing audio MediaStreamTrack.

**Bug Fixed**

- Fixed the occasional issue where setting setRemoteAudioVolume to 0 did not works.
- Fixed the issue that screen sharing audio was not published after calling updateScreenShare({ publish: true }).
- Fixed the issue where virtual backgrounds could not be enabled in Safari.

## Version 5.4.2 @2024.03.01

**Bug Fixed**

- Fixed the issue that startRemoteVideo failed occasionally.
- Fixed the issue that unpublish failed occasionally.
- Fixed the issue that audio & video are not synchronized.
- Fixed the issue that enterRoom failed when using nginx proxy.

## Version 5.4.1 @2024.02.05

**Improvement**

- Optimize the reconnection logic to improve the success rate of reconnection.

**Bug Fixed**

- Fixed the issue that mirror is reset by calling updateLocalVideo.
- Fixed the issue that CONNECTING state is not emitted by CONNECTION_STATE_CHANGED event.

## Version 5.4.0 @2024.01.16

**Feature**

- Support [getVideoSnapshot()](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#getVideoSnapshot)。
- Support publish an image after mute video. Refer to: the mute param of [startLocalVideo()](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#startLocalVideo)
- Support receive video only when the view is visible. Refer to: [Multi-Person Video Calls](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-27-advanced-small-stream.html)。
- Support region capture of screen sharing. Refer to: the captureElement param of [startScreenShare()](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#startScreenShare)

**Improvement**

- Reduce the time cost of enterRoom.
- Optimize the reconnection logic of laptop closed and reopened.

**Bug Fixed**

- Fix the issue that publish failed on the version before Chrome 69.
- Fix the issue that publish 1080p video failed on iOS 13 & 14.

## Version 5.3.2 @2023.12.22

**Feature**

- Support watermark plugin, refer to: [Enable Watermark](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-29-advance-water-mark.html).
- Support encoding mirror, refer to: [startLocalVideo() 's mirror parameter](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#startLocalVideo).

**Improvement**

- Improve audio and video encoding stability and quality.

**Bug Fixed**

- Fix known issues with the CDNStreaming plugin.
- Fix the issue where the volume value returned by the volume event is 0 after setting remoteAudioVolume to 0.
- Fix the issue of occasional vocals dropping words when AI denoiser is enabled on some external microphones.

## Version 5.3.1 @2023.12.08

**Bug Fixed**

- Fix known issues of AudioMixer plugin.
- Fix the issue that cannot enterRoom on Chrome version before 74.
- Fix the issue that some audio APIs don't working properly by enabling AI denoiser.
- Fix the issue that DEVICE_CHANGED event does't fired if you destroy other trtc instance.

## Version 5.3.0 @2023.12.01

**Feature**

- Support SEI message sending and receiving, which can be used to implement functions such as lyric synchronization and live quiz. Refer to [sendSEIMessage](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#sendSEIMessage).
- Support dynamic switching of large and small streams. Refer to the option.small parameter of [updateLocalVideo](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#updateLocalVideo).
- Support mute streaming. Refer to the mute parameter of [startLocalAudio()](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#startLocalVideo).
- Support updating privateMapKey when switching roles. Refer to the privateMapKey parameter of [switchRole](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#switchRole).
- Support [TRTC.EVENT.TRACK](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/module-EVENT.html#.TRACK) event.

**Improvement**

- Optimize the process of entering room to shorten the time it takes.
- Optimize the encoding quality of high-resolution call scenarios and low-version Android Chrome devices.
- Optimize the device capture logic. When there is no media access permission, the SDK may temporarily request media permission to ensure that media devices can be obtained normally and will release the media device afterward.
- Optimize the parsing logic of the url parameter of the AudioMixer plugin.
- Improve the noise reduction effect of the AIDenoiser plugin.

**Bug Fixed**

- Fix the issue that Android Chrome cannot encode 120p.
- Fix the issue where stopping screen sharing in a non-streaming scenario would cause the camera to stop streaming.
- Fix the issue that a parameter of CDN streaming plugin does not working.

## Version 5.2.1 @2023.11.08

**Feature**

- Add 'captureVolume' parameter to API [startLocalAudio](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#startLocalVideo) & [updateLocalAudio](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#updateLocalAudio).
- Support [TRTC.EVENT.DEVICE_CHANGED](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/module-EVENT.html#.DEVICE_CHANGED) event on mobile phone. You can implement the feature that auto switch to new microphone when a new headset is connected based on this event. Refer to [Handle Device Change](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-25-advanced-device-change.html)。

**Bug Fixed**

- Fix the issue that local microphone is muted after switching microphone.
- Fix the issue that the mediaType of [TRTC.EVENT.PUBLISH_STATE_CHANGED](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/module-EVENT.html#.PUBLISH_STATE_CHANGED) is wrong after [stopScreenShare](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#stopScreenShare).

## Version 5.2.0 @2023.10.30

**Feature**

- Add [TRTC.EVENT.STATISTICS](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/module-EVENT.html#.STATISTICS) event.

**Improvement**

- Improve the success rate of device capture.
- Optimize the mirror processing logic of "Picture-in-Picture mode".
- When the user's system rejects the browser permission, RtcError.handler() can be called to jump to the system authorization settings and guide the user to turn on the permission quickly. Refer to [5302](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/module-ERROR_CODE.html#.ENV_NOT_SUPPORTED)。

**Bug Fixed**

- Fixed a occasional issue that remote audio is not playing in low version of Chrome.

## Version 5.1.3 @2023.09.11

**Feature**

- [trtc.setRemoteAudioVolume](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#setRemoteAudioVolume) supports setting the volume higher than 100 to gain the remote playback volume.

**Improvement**

- Avoided [iOS 15.1 bug](https://bugs.webkit.org/show_bug.cgi?id=232006) that caused page crash when switching camera.

**Bug Fixed**

- Fix the issue that Firefox stopLocalVideo and then restart startLocalVideo failed.
- Fix the issue that Firefox fails to capture camera with certain resolution, e.g. 640 * 360.
- Fix the issue of remote video not playing occasionally.

## Version 5.1.2 @2023.08.25

**Improvement**

- Reduce time cost to enter a room.

**Bug Fixed**

- Fix the issue that webpack package build of trtc.esm.js occasionally reporting errors.
- Fix the issue that startLocalAudio passing in custom capture audioTrack does not work.

## Version 5.1.1 @2023.08.18

**Improvement**

- Default video profile changed to 480p_2 to reduce uplink bandwidth consumption.
- Avoid the [Chrome Bug](https://bugs.chromium.org/p/chromium/issues/detail?id=1469318) that Android Chrome 115 occasionally fails to encode at resolutions lower than 360p.

**Bug Fixed**

- Fix the issue that cannot enter room or startLocalVideo on Windows Chrome 57 and iOS Safari 12.
- Fix the issue that the video bitrate is abnormal on Dashboard.

## Version 5.1.0 @2023.08.11

**Breaking Change**

- Restrict the roomId parameter of the [trtc.enterRoom](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/TRTC.html#enterRoom) interface to be of number type and no longer support passing in string type. If you want to use a string roomId, please use the strRoomId parameter. When upgrading, please pay attention, see [Upgrade Guide](./tutorial-00-info-update-guideline.html) for details.

**Feature**.

- Support background music plugin, refer to the tutorial: [Implement Background Music](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-22-advanced-audio-mixer.html).
- Support AI noise reduction plugin, refer to tutorial: [Implement AI Denoise](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-35-advanced-ai-denoiser.html).

**Bug Fixed**.

- Fix the issue that setting screen sharing capture resolution does not work.
- Fix the issue of occasional playback failure of remote screen sharing.

## Version 5.0.3 @2023.07.31

**Improvement**

- Optimize the reconnection mechanism to improve the stability of network connection.

**Bug Fixed**

- Fix the issue that when calling trtc.stopRemoteVideo to stop the main video, the sub video is also stopped.

## Version 5.0.2 @2023.07.21

**Improvement**

- Optimize the performance and weak network resistance in multi-person call.
- Optimize device capture logic to avoid the issue that some Lenovo devices cannot turn on the camera.
- Optimize the capture parameters of screen sharing to avoid the issue of occasional frame dropping in long-time screen sharing.

**Bug Fixed**

- Fix the issue that the small stream bit rate setting does not take effect.
- Fix the issue that systemAudio parameter does not work.
- Fix the issue that video tag is not destroyed after remote user screen sharing stopped.

## Version 5.0.1 @2023.06.25

**Feature**

- Support playing video with multiple view  at the same time

**Bug Fixed**

- Fix the issue that screen sharing cannot be restarted after clicking the browser hover window to close screen sharing.

## Version 5.0.0 @2023.05.26

The new architecture version of the TRTC Web SDK provides a flat interface that dramatically simplifies the API and reduces access costs. Features of the new API:

- Flatter APIs that are easier to access.
- Better stability.
- Better performance.
