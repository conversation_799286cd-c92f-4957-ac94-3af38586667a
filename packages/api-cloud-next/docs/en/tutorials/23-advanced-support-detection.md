## Browser Environment Check

Before calling the communication capabilities of the SDK, we recommend you use the `{@link TRTC.isSupported TRTC.isSupported}` API to check whether the SDK supports the current browser first, and if not, please recommend the user to use a supported browser(Chrome 56+, Edge 80+, Firefox 56+, Safari 11+).

```javascript
TRTC.isSupported().then(checkResult => {
  // Not supported, guide the user to use a supported browser(Chrome 56+, Edge 80+, Firefox 56+, Safari 11+).
  if (!checkResult.result) {}
  // Not support to publish video
  if (!checkResult.detail.isH264EncodeSupported) {}
  // Not support to subscribe video
  if (!checkResult.detail.isH264DecodeSupported) {}
})
```

⚠️ If the check result returned by `TRTC.isSupported` is `false`, this may be because:

1. The web page uses the http protocol. Browsers do not allow http protocol sites to capture cameras and microphones, you need to deploy your page using the https protocol.
2. The current browser does not support WebRTC, you need to guide the user to use the recommended browser {@tutorial 05-info-browser}.
3. Firefox browser needs to load H264 codec dynamically after installation, so the detection result will be false for a short period of time, please wait and try again or guide to use other browsers.

## Test Media Device By Plugin

The device detector plugin is used to test the user's media device(camera, microphone, speaker) and network which is recommended to be used before the user enters the room. 。

> ⚠️ Support TRTC Web SDK version >= v5.8.0

### Usage

```javascript
import { DeviceDetector } from 'trtc-sdk-v5/plugins/device-detector';
const trtc = TRTC.create({ plugins: [DeviceDetector] });

// 1. Test Media Device Only
const result = await trtc.startPlugin('DeviceDetector');

// 2. Test Media Device & Network Quality
const options = { 
    networkDetect: { sdkAppId, userId, userSig }
}
const resultWithNetwork = await trtc.startPlugin('DeviceDetector', options);
```

### API Description

#### trtc.startPlugin('DeviceDetector', options)

##### options.mirror(optional)
Enable the mirror checkbox during camera detection

##### options.cameraDetect(optional)
| Name | Type | Attributes | Description |
| --------------- | ------ | ---------- | ------------------------------------------------------------ |
| mirror | boolean | Optional | Optional. When true, the mirror image will be enabled during the camera detection phase, and a checkbox for the mirror image will be displayed |

##### options.networkDetect(optional)

| Name            | Type   | Attributes | Description                                                  |
| --------------- | ------ | ---------- | ------------------------------------------------------------ |
| sdkAppId        | string | required   | Your sdkAppId                                                |
| userId          | string | required   | The userId for testing uplink network quality. It should be different from downlinkUserId. |
| userSig         | string | required   | The [UserSig](https://trtc.io/document/35166) of the userId. |
| downlinkUserId  | string | required   | The userId for testing downlink network quality. It should be different from userId. Fill in downlinkUserId and downlinkUserSig will perform a downlink network test. |
| downlinkUserSig | string | required   | The [UserSig](https://trtc.io/document/35166) of the downlinkUserId. |
| roomId          | number | optional   | Optional. The default value is 8080. The value must be an integer of [1, 4294967294] |

#### trtc.stopPlugin('DeviceDetector')

| Name           | Type     | Description                                                  |
| -------------- | -------- | ------------------------------------------------------------ |
| **camera**     | `object` | { isSuccess: boolean, device: { deviceId: string, groupId: string, kind: "videoinput", label: string }} |
| **microphone** | `object` | { isSuccess: boolean, device: { deviceId: string, groupId: string, kind: "audioinput", label: string }} |
| **speaker**    | `object` | { isSuccess: boolean, device: { deviceId: string, groupId: string, kind: "audiooutput", label: string }} |
| **network**    | `object` | { isSuccess: boolean, result: { quality: number , rtt: number }}<br />The quality is uplink network quality. When the downlinkUserId and downlinkUserSig passed, the quality is the worse one of the upstream and downstream network quality<br />**Network Quality Values**<br />0: The network quality is unknown, indicating that the current TRTC instance has not established a media connection<br />1:  The network quality is excellent<br />2:  The network quality is good<br />3:  The network quality is average<br />4:  The network quality is poor<br />5:  The network quality is extremely poor<br />6:  The network connection has been disconnected. |

### Plugin Demo

<iframe allow="microphone; camera; display-capture;" height="700" style="width:100%" scrolling="no" title="Device Detector" src="https://codepen.io/TRTC/embed/eYwVVja?default-tab=" frameborder="no" loading="lazy" allowtransparency="true" allowfullscreen="true">
  See the Pen <a href="https://codepen.io/TRTC/pen/eYwVVja">
  Device Detector</a> by TRTC (<a href="https://codepen.io/TRTC">@TRTC</a>)
  on <a href="https://codepen.io">CodePen</a>.
</iframe>


## TRTC Capability Detection Page

You can use the [TRTC Detection Page](https://web.sdk.qcloud.com/trtc/webrtc/demo/detect/index.html) where you are currently using the TRTC SDK to detect the current environment. You can also click the "Generate Report" button to get a report of the current environment for environment detection or troubleshooting.

