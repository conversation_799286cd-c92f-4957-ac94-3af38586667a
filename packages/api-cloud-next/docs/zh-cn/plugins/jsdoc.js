/**
 * 插件名
 * @typedef {'AudioMixer'|'AIDenoiser'|'CDNStreaming'|'Watermark'|'BasicBeauty'} PluginName
 */


/**
 * 新增背景音乐插件参数
 * @typedef {Object} AudioMixerOptions
 * @property {string} id 给每个传入的音乐 url 设置一个唯一的 ID
 * @property {string} url 背景音乐地址的 url，支持的格式为 MP3，AAC（以及浏览器支持的其他音频格式）
 * @property {boolean} [loop] 背景音乐是否重复播放
 * @property {number} [volume] 背景音乐的播放音量 (0-1)
 */

/**
 * 更新背景音乐插件参数
 * @typedef {Object} UpdateAudioMixerOptions
 * @property {string} id 给背景音乐设置的唯一 ID
 * @property {boolean} [loop] 背景音乐是否重复播放
 * @property {number} [volume] 背景音乐的播放音量 (0-1)
 * @property {number} [seekFrom] 从某一秒开始 seek
 * @property {string} [operation] 对背景音乐的操作: 'pause' ｜ 'resume' ｜ 'stop'
 */


/**
 * 停止背景音乐插件参数
 * @typedef {Object} StopAudioMixerOptions
 * @property {string} id 给背景音乐设置的唯一 ID
 */

/**
 * 开启降噪参数
 * @typedef {Object} AIDenoiserOptions
 * @property {string} assetsPath 降噪资源路径
 * @property {number} sdkAppId 应用的 SDKAppId
 * @property {string} userId 当前进房用户的 userId
 * @property {string} userSig 当前进房用户的 userSig
 */

/**
 * CDNStreaming 插件参数，教程可见 {@tutorial 26-advanced-publish-cdn-stream}
 * @typedef {Object} CDNStreamingOptions
 * @property {Target} target 目标地址，支持转推/转码到腾讯或者第三方 CDN
 * @property {Encoding} encoding 编码输出参数，推混流模式时必填，您需要指定您预期的转码输出参数。
 * @property {Mix} mix 混流配置参数，您需要指定您预期的混流转码配置参数。转推模式下填写则无效。
 */

/**
 * @typedef {Object} Target
 * @property {PublishMode} publishMode - 选择推流模式，可选值为 PublishMode.PublishMainStreamToCDN、PublishMode.PublishSubStreamToCDN、PublishMode.PublishMixStreamToCDN、PublishMode.PublishMixStreamToRoom
 * @property {string} [streamId] - 发布 CDN 后的流 ID，默认值为转推流名为 `${sdkappid}_${roomid}_${userid}_main`。当 roomId 或者 userId 包含大小写英文字母(a-zA-Z)、数字(0-9)、连字符及下划线之外的特殊字符时，转推流名为 `${sdkAppId}_${md5(${roomId}_${userId}_main)}`
 * @property {number} [appId] - 应用 ID（当转推到第三方 CDN 时必填）
 * @property {number} [bizId] - 默认推流域名的前 6 个数字（当转推到第三方 CDN 时必填）
 * @property {string} [url] - 第三方 CDN 的推流地址（当转推到第三方 CDN 时必填）
 * @property {FixedVideoUser} [robotUser] - 回推房间机器人信息（当推流模式为回推时必填）
 */

/**
 * @typedef {Object} Encoding - 编码配置（推混流时必填）
 * @property {number} [videoWidth] - 转码后视频分辨率的宽度（px），转码后视频的宽度设置必须大于等于 0 且能容纳所有混入视频，默认值为 640
 * @property {number} [videoHeight] - 转码后视频分辨率的高度（px），转码后视频的高度设置必须大于等于 0 且能容纳所有混入视频，默认值为 480
 * @property {number} videoBitrate - 转码后的视频码率（kbps），如果传入值为 0，码率值将由 videoWidth 和 videoHeight 决定
 * @property {number} [videoFramerate] - 转码后的视频帧率（fps），默认值为 15，取值范围为 (0, 30]
 * @property {number} [videoGOP] - 转码后的视频关键帧间隔（s），默认值为 2，取值范围为 [1, 8]
 * @property {number} [audioSampleRate] - 转码后的音频采样率（Hz），默认值为 48000
 * @property {number} [audioBitrate] - 转码后的音频码率（kbps），默认值为 64，取值范围为 [32, 192]
 * @property {number} [audioChannels] - 转码后的音频声道数，默认值为 1，取值范围为 1 或 2
 */

/**
 * @typedef {Object} Mix - 混流配置（推混流时必填）
 * @property {number} [backgroundColor] - 混合后画面的背景颜色，格式为十六进制数字，默认值为 0x000000（黑色）
 * @property {string} [backgroundImage] - 混合后画面的背景图，默认值为 ''。背景图需要事先在实时音视频控制台的应用管理 > 功能配置 > 素材管理中上传，并获取对应的图片ID，然后将图片ID转换成字符串类型并设置到 backgroundImage 中
 * @property {Array<AudioMixUser>} audioMixUserList - 音频混流用户列表，未填时默认混流全部用户的音频
 * @property {Array<VideoLayout>} videoLayoutList - 混流中的视频布局，必须包含自己
 */

/**
 * @typedef {Object} AudioMixUser
 * @property {string} userId - 用户ID
 * @property {number} roomId - 数字类型房间 ID，如果需要使用字符串类型的房间号请使用 strRoomId 参数，roomId 和 strRoomId 必须填一个。若两者都填，则优先选择 roomId。
 * @property {string} strRoomId - 字符串类型的房间ID
 */

/**
 * @typedef {Object} VideoLayout
 * @property {FixedVideoUser} fixedVideoUser - 该布局对应的用户
 * @property {string} fixedVideoStreamType - 用于指定混入流类型，TRTC.TYPE.STREAM_TYPE_MAIN 为摄像头流，TRTC.TYPE.STREAM_TYPE_SUB 为屏幕分享流
 * @property {number} fillMode - 该用户流在混流中的渲染模式，默认为裁剪模式。0：裁剪模式，1：用户流缩放并显示背景，2：用户流缩放并显示黑底
 * @property {number} zOrder - 该用户流在混流中的图层层次，取值范围为 [1, 15]
 * @property {number} [width] - 该用户流在混流中的宽度（px），必须大于等于 0，默认值为 0
 * @property {number} [height] - 该用户流在混流中的高度（px），必须大于等于 0，默认值为 0
 * @property {number} [locationX] - 以混流左上角为起点，该用户流在混流中的 X 坐标（px），必须大于等于 0，默认值为 0
 * @property {number} [locationY] - 以混流左上角为起点，该用户流在混流中的 Y 坐标（px），必须大于等于 0，默认值为 0
 */

/**
 * @typedef {Object} FixedVideoUser
 * @property {string} userId - 该用户ID
 * @property {number} roomId - 该用户所在的数字房间ID，如果需要使用字符串类型的房间号请使用 strRoomId 参数，roomId 和 strRoomId 必须填一个。若两者都填，则优先选择 roomId。
 * @property {string} strRoomId - 字符串类型的房间ID
 */

/**
 * @typedef {Enum} PublishMode
 * @property {string} PublishMainStreamToCDN - 推送主画面到 CDN
 * @property {string} PublishSubStreamToCDN - 推送辅流到 CDN
 * @property {string} PublishMixStreamToCDN - 推送混流到 CDN
 */

/**
 * VirtualBackground 插件启动参数，教程可见 {@tutorial 36-advanced-virtual-background}
 * @typedef {Object} VirtualBackgroundOptions
 * @property {number} sdkAppId 当前应用 ID，可在 [实时音视频控制台](https://console.cloud.tencent.com/trtc) 获取
 * @property {string} userId 用户ID。建议限制长度为32字节，只允许包含大小写英文字母(a-zA-Z)、数字(0-9)及下划线和连词符。
 * @property {string} userSig 计算 userSig 的方式请参考 [UserSig 相关](https://cloud.tencent.com/document/product/647/17275)。
 * @property {string=} type 虚拟背景类型。填入 `'blur'` 代表模糊背景；填入 `'image'` 代表图片背景，需要传入图片 url。默认为 blur。
 * @property {number=} blurLevel `blur` 虚化背景时可用，用于调节虚化程度，默认为 3，取值范围为 [1,10]。
 * @property {string=} src 设置类型为图片背景时，需通过此参数传入图片 url。
 * @property {function=} onAbort 运行过程中发生错误导致关闭插件的回调。
 */

/**
 * VirtualBackground 插件更新参数，教程可见 {@tutorial 36-advanced-virtual-background}
 * @typedef {Object} UpdateVirtualBackgroundOptions
 * @property {string} type 虚拟背景类型。填入 `'blur'` 代表模糊背景；填入 `'image'` 代表图片背景，需要传入图片 url。
 * @property {number=} blurLevel `blur` 虚化背景时可用，用于调节虚化程度，默认为 3，取值范围为 [1,10]。
 * @property {string=} src 设置类型为图片背景时，需通过此参数传入图片 url。
 */

/**
 * Watermark 插件启动参数，教程可见 {@tutorial 29-advanced-water-mark}
 * @typedef {Object} WatermarkOptions
 * @property {string} imageUrl - 图片水印地址。这是一个必填参数。
 * @property {string=} [x] - 水印左边距。这是一个选填参数。
 * @property {string=} [y] - 水印上边距。这是一个选填参数。
 * @property {(string|number|object)} [size] - 指定水印的大小。这是一个选填参数。默认值为 `cover`。
 *   传入字符串时：
 *   - `"cover"` 缩放背景图片以完全覆盖背景区，可能导致背景图片的部分区域看不见。
 *   - `"contain"` 缩放背景图片以完全装入背景区，可能导致背景区的部分空白。
 *   传入数字时：
 *   - `x` 表示将背景图片缩放 x 倍，如 0.5，有效取值范围为 `(0,1]`。
 *   传入对象时：
 *   - 可以通过传入 `{width: 200, height: 300}` 来手动指定尺寸。
 *
 */

/**
 * 美颜插件参数
 * @typedef {Object} BasicBeautyOptions
 * @property {number} [beauty=0.5] 美颜程度，取值范围 [0, 1]，默认为 0.5
 * @property {number} [brightness=0.5] 明亮程度，取值范围 [0, 1]，默认为 0.5
 * @property {number} [ruddy=0.5] 红润程度，取值范围 [0, 1]，默认为 0.5
 */
