import { CoreError, CoreErrorCode, ErrorInfo } from 'trtc-js-sdk-core';
import { ErrorCode, ErrorCodeDictionary } from './error-code';
import { getErrorMessage } from './error-message';
import { IS_MAC, IS_WIN } from 'trtc-js-sdk-core/src/utils/environment';
/**
 * RtcError 错误对象
 *
 * @extends Error
 * @class RtcError
 *
 */
class RtcError extends Error {
    name = 'RtcError';
    /**
     *
     * 错误码
     * @see 详细错误码列表： {@link module:ERROR_CODE ErrorCode}
     * @readonly
     * @memberof RtcError
     */
    code;
    /**
     *
     * 扩展错误码
     * @see 详细错误码列表： {@link module:ERROR_CODE ErrorCode}
     * @readonly
     * @memberof RtcError
     */
    extraCode;
    /**
     *
     * 抛出错误的函数名称
     * @readonly
     * @memberof RtcError
     */
    functionName;
    /**
     *
     * 错误信息
     * @readonly
     * @memberof RtcError
     */
    message;
    /**
     * 错误回调处理函数，可在部分错误出现时，尝试进行恢复。目前支持如下错误的 handler：
     *
     * - extraCode 5302：当系统关闭了浏览器的摄像头、麦克风、屏幕分享权限时，{@link TRTC#startLocalAudio trtc.startLocalAudio} {@link TRTC#startLocalVideo trtc.startLocalVideo} {@link TRTC#startScreenShare trtc.startScreenShare} 会采集失败，此时调用 error.handler 可跳转至系统权限设置 APP，方便用户开启权限。
     *    ```js
     *    trtc.startLocalAudio().catch(error => {
     *     if (error.extraCode === 5302 && typeof error.handler === 'function') {
     *       // 提示用户系统关闭了浏览器的摄像头 or 麦克风权限，即将跳转至系统权限设置 APP，请打开浏览器摄像头、麦克风权限。
     *       // 适用于 Windows 和 MacOS 系统。
     *       error.handler();
     *     }
     *    })
     *    ```
     * @since v5.2.0
     * @readonly
     * @memberof RtcError
     */
    handler;
    originError;
    constructor({ code, extraCode, message = '', messageParams, fnName = '', originError }) {
        let _message;
        if (!message) {
            _message = getErrorMessage({
                // code: extraCode ? extraCode : code,
                code: extraCode || code,
                params: { fnName, error: originError, ...messageParams }
            });
        }
        else {
            _message = message;
        }
        super(_message);
        this.name = ErrorCodeDictionary[code];
        this.code = code;
        this.extraCode = extraCode;
        this.functionName = fnName;
        this.originError = originError;
        this.message = _message;
        // 
        if (this.extraCode === ErrorCodeDictionary.DEVICE_NOT_ALLOWED_ERROR && this.originError?.message.includes('system')) {
            this.handler = () => {
                const MAC_URL_SCHEMES = {
                    startLocalVideo: 'Camera',
                    startLocalAudio: 'Microphone',
                    startScreenShare: 'ScreenCapture'
                };
                const WIN_URL_SCHEMES = {
                    startLocalVideo: 'webcam',
                    startLocalAudio: 'microphone'
                };
                const a = document.createElement('a');
                if (IS_WIN) {
                    a.href = `ms-settings:privacy-${WIN_URL_SCHEMES[this.functionName]}`;
                }
                else if (IS_MAC) {
                    a.href = `x-apple.systempreferences:com.apple.preference.security?Privacy_${MAC_URL_SCHEMES[this.functionName]}`;
                }
                if (a.href.length > 0) {
                    a.click();
                }
            };
        }
    }
    static convertFrom(originError, fnName, fnParams) {
        let err = originError;
        if (originError instanceof CoreError) {
            const { stack } = originError;
            const rtcErrorParams = {
                code: ErrorCode.UNKNOWN_ERROR,
                fnName,
                originError
            };
            switch (originError.getCode()) {
                case CoreErrorCode.INVALID_PARAMETER:
                    rtcErrorParams.code = ErrorCode.INVALID_PARAMETER;
                    break;
                case CoreErrorCode.INVALID_OPERATION:
                    rtcErrorParams.code = ErrorCode.INVALID_OPERATION;
                    break;
                case CoreErrorCode.NOT_SUPPORTED:
                case CoreErrorCode.NOT_SUPPORTED_H264:
                    rtcErrorParams.code = ErrorCode.ENV_NOT_SUPPORTED;
                    if (originError.getCode() === CoreErrorCode.NOT_SUPPORTED_H264) {
                        rtcErrorParams.extraCode = originError.message.includes(ErrorInfo.NOT_SUPPORTED_H264ENCODE) ? ErrorCodeDictionary.NOT_SUPPORTED_H264_ENCODE : ErrorCodeDictionary.NOT_SUPPORTED_H264_DECODE;
                    }
                    break;
                case CoreErrorCode.DEVICE_NOT_FOUND:
                case CoreErrorCode.DEVICE_AUTO_RECOVER_FAILED:
                    rtcErrorParams.code = ErrorCode.DEVICE_ERROR;
                    break;
                case CoreErrorCode.JOIN_ROOM_FAILED:
                    rtcErrorParams.messageParams = { fnParams };
                case CoreErrorCode.SERVER_TIMEOUT:
                case CoreErrorCode.SWITCH_ROLE_FAILED:
                    rtcErrorParams.code = ErrorCode.SERVER_ERROR;
                    rtcErrorParams.extraCode = originError.getExtraCode();
                    break;
                case CoreErrorCode.API_CALL_ABORTED:
                    rtcErrorParams.code = ErrorCode.OPERATION_ABORT;
                    break;
                case CoreErrorCode.INITIALIZE_FAILED:
                    rtcErrorParams.code = ErrorCodeDictionary.DEVICE_ERROR;
                    rtcErrorParams.extraCode = convertDeviceError(originError.name);
                    break;
                case CoreErrorCode.UNKNOWN:
                    break;
                // case CoreErrorCode.PLAY_NOT_ALLOWED:
                // case CoreErrorCode.SCHEDULE_FAILED:
                // case CoreErrorCode.API_CALL_TIMEOUT:
                // case CoreErrorCode.REMOTE_STREAM_NOT_EXIST:
                // case CoreErrorCode.DOWNLINK_RECONNECTION_FAILED:
                // case CoreErrorCode.UPLINK_RECONNECTION_FAILED:
                // case CoreErrorCode.SIGNAL_CHANNEL_RECONNECTION_FAILED:
                // case CoreErrorCode.ICE_TRANSPORT_ERROR:
                // case CoreErrorCode.SIGNAL_CHANNEL_SETUP_FAILED:
                default:
                    rtcErrorParams.code = ErrorCode.OPERATION_FAILED;
            }
            err = new RtcError(rtcErrorParams);
            if (stack)
                err.stack += stack.substr(stack.indexOf('\n'));
        }
        else {
            err = new RtcError({
                code: ErrorCode.UNKNOWN_ERROR,
                fnName,
                originError
            });
        }
        return err;
    }
}
function convertDeviceError(errorName) {
    let errorCode;
    switch (errorName) {
        case 'NotFoundError':
            errorCode = ErrorCodeDictionary.DEVICE_NOT_FOUND_ERROR;
            break;
        case 'NotAllowedError':
            errorCode = ErrorCodeDictionary.DEVICE_NOT_ALLOWED_ERROR;
            break;
        case 'NotReadableError':
            errorCode = ErrorCodeDictionary.DEVICE_NOT_READABLE_ERROR;
            break;
        case 'OverconstrainedError':
            errorCode = ErrorCodeDictionary.DEVICE_OVERCONSTRAINED_ERROR;
            break;
        case 'InvalidStateError':
            errorCode = ErrorCodeDictionary.DEVICE_INVALID_STATE_ERROR;
            break;
        case 'SecurityError':
            errorCode = ErrorCodeDictionary.DEVICE_SECURITY_ERROR;
            break;
        case 'AbortError':
            errorCode = ErrorCodeDictionary.DEVICE_ABORT_ERROR;
            break;
        default:
            errorCode = 5300;
    }
    return errorCode;
}
export default RtcError;
