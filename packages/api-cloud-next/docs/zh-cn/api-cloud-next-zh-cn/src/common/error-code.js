/**
 * 增加错误码规范：
 * SDK 规范了 8个错误码类型，新增的错误码必须属于这些类型
 * ERROR_CODE 常量只需要更新文档说明新增的错误码 number以及含义
 * ErrorCodeDictionary 枚举需要添加对应的 常量字符和 errorcode 的映射，用于获取 error message 时互查
 * ErrorMessage 可以增加对应常量的 message，否则将会使用所属错误码类型的 message
 */
/**
 * TRTC SDK v5.0 定义了8种错误码类型，通过 RtcError 对象来获取 errorCode 并做相应的处理。
 *
 * @see RtcError
 * @see {@link module:EVENT.ERROR TRTC.EVENT.ERROR}
 * @module ERROR_CODE
 * @example
 * // 使用方式：
 * // 1. API 调用错误
 * trtc.startLocalVideo().catch(error => {
 *  if (error.code === TRTC.ERROR_CODE.DEVICE_ERROR) {}
 * });
 * // 2. 非 API 调用错误，SDK 内部经过重试后依然无法恢复的错误
 * trtc.on(TRTC.EVENT.ERROR, (error) => {
 *    if (error.code === TRTC.ERROR_CODE.OPERATION_FAILED) {}
 * });
 */
export const ErrorCode = {
  /**
     * 调用接口时传入了不满足 API 要求的参数 <br>
     * 处理建议：请检查传入参数是否符合 API 的规范，例如参数类型是否正确。
     * | extraCode | 描述                                                                                                         |
     * | :----- | :------------------------------------------------------------------------------------------------------------- |
     * | 5001 | 未填必填参数|
     * | 5002 | 参数类型不对|
     * | 5003 | 参数传了空值|
     * | 5004 | 参数原型不对|
     * | 5005 | 参数不在规定的取值范围|
     * | 5006 | 参数要大于等于 0|
     * | 5007 | 参数要大于最小值|
     * | 5008 | 参数要小于最大值|
     * | 5009 | 该 elementId 在页面上找不到，或者查找时不在页面上|
     * | 5010 | 传入的参数不是 HTMLElement 类型|
     * | 5011 | streamId 不符合规范|
     * | 5012 | 传入 roomId 的范围不符合规范 [1, 4294967294] |
     * | 5013 | 传入的 strRoomId 类型不是合法的 string |
     * | 5014 | 当 userId 不是 '*'时，需要传入 streamType|
     * | 5015 | 未填写 roomId 或 strRoomId, roomId 和 strRoomId 必须填写一个 |
     * | 5016 | roomId 必须是数字类型，当前传入了字符串类型，如果需要使用字符串作为房间号，请使用 strRoomId |
     * | 5017 | arraybuffer 的 size 不能为 0，从这些 API 中抛出该错误： {@link TRTC#sendSEIMessage TRTC.sendSEIMessage}, {@link TRTC#sendCustomMessage TRTC.sendCustomMessage} |
     * | 5018 | arraybuffer 的 size 超过允许的范围，从这些 API 中抛出该错误：{@link TRTC#sendSEIMessage TRTC.sendSEIMessage}, {@link TRTC#sendCustomMessage TRTC.sendCustomMessage} |
     * @default 5000
     * @memberof module:ERROR_CODE
     */
  INVALID_PARAMETER: 5000,
  /**
     * 调用接口时，不满足 API 的前提要求。 <br>
     * 处理建议：请根据对应 API 文档检查调用逻辑是否符合 API 的前提要求。例如：1.未进房成功就进行切换角色，2.播放的远端用户和流不存在。
     * | extraCode | 描述                                                                                                         |
     * | :----- | :------------------------------------------------------------------------------------------------------------- |
     * | 5101 | 在未进房的情况下调用 API，例如 startRemoteVideo muteRemoteAudio switchRole 等 API 需在进房后调用 |
     * | 5102 | 远端用户不存在，例如在 startRemoteVideo 时，传入的 userId 对应的远端用户不在房间内。 |
     * | 5103 | 远端流类型不存在，例如在 startRemoteVideo 时，传入的 streamType = TRTC.TYPE.STREAM_TYPE.SUB，但对应的远端用户并没有推屏幕分享。 |
     * | 5104 | 重复调用 API，例如在进房成功后，又调用 enterRoom 接口。 |
     * | 5105 | 在未开启摄像头的情况下，调用 API。例如虚拟背景插件需要在摄像头开启的情况下使用。|
     * | 5106 | 在未开启麦克风的情况下，调用 API。例如 AI 降噪插件需要在麦克风开启的情况下使用。|
     * | 5107 | 观众角色 {@link module:TYPE.ROLE_AUDIENCE TRTC.TYPE.ROLE_AUDIENCE} 不能调用此 API。 |
     * | 5108 | 你需要在 TRTC.create({ enableSEI: true }) 打开 SEI 功能|
     * | 5109 | 你需要在发送 SEI 之前推视频流。 |
     * @default 5100
     * @memberof module:ERROR_CODE
     */
  INVALID_OPERATION: 5100,
  /**
     * 当前环境不支持该功能，表明当前浏览器不支持调用对应 API <br>
     * 处理建议：通常使用 TRTC.isSupported 可感知当前浏览器支持哪些能力。如果浏览器不支持，需要引导用户使用支持该能力的浏览器，参考：[检测浏览器支持性](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-23-advanced-support-detection.html)
     * | extraCode | 描述                                                                                                         |
     * | :----- | :------------------------------------------------------------------------------------------------------------- |
     * | 5201 | 当前页面协议非 HTTPS，不支持音视频采集（推流、连麦）能力 |
     * | 5202 | 当前浏览器不支持 WebRTC 能力，浏览器版本过低 |
     * | 5203 | 浏览器不支持 H264 编码能力 |
     * | 5204 | 浏览器不支持 H264 解码能力 |
     * | 5205 | 浏览器不支持屏幕分享能力   |
     * | 5206 | 浏览器不支持小流编码能力   |
     * | 5207 | 浏览器不支持 SEI 收发能力 |
     * | 5208 | 浏览器不支持 WebGL |
     * | 5209 | 当前版本的浏览器不支持该功能，请升级到最新版本 |
     * | 5210 | 当前版本的浏览器不支持该插件，请升级到最新版本 |
     * @default 5200
     * @memberof module:ERROR_CODE
     */
  ENV_NOT_SUPPORTED: 5200,
  /**
     * 获取设备或者采集音视频出现异常。这些接口出现异常时会抛出该错误码：{@link TRTC#startLocalAudio trtc.startLocalAudio} {@link TRTC#startLocalVideo trtc.startLocalVideo} {@link TRTC#startScreenShare trtc.startScreenShare}。<br><br>
     * 处理建议：引导用户检查设备是否有摄像头及麦克风、系统是否给浏览器授权以及浏览器是否给页面授权。建议增加进房前的设备检测流程，确认麦克风和摄像头是否存在，并且能正常采集，再进行下一步通话操作。通常经过设备检查后都能避免该异常。实现方式请参考: {@tutorial 23-advanced-support-detection}。<br><br>
     * 如果需要区分更详细的异常类别，可以按照以下 extraCode 进行处理：
     * | extraCode  | 描述                                                                                                        |
     * | :----- | :------------------------------------------------------------------------------------------------------------- |
     * | 5301 | 由浏览器抛出的 NotFoundError(DOMException)  <br> 原因：找不到满足请求参数的媒体设备类型（包括：音频、视频、屏幕分享）。例如：PC 没有摄像头，但是请求浏览器获取视频流，则会报此错误。 <br> 处理建议：建议在通话开始前引导用户检查通话所需的摄像头或麦克风等外设。  |
     * | 5302 | 由浏览器抛出的 NotAllowedError(DOMException)  <br>原因：<li>用户拒绝了当前的浏览器实例的采集麦克风、摄像头、屏幕分享请求。</li> <li>系统关闭了浏览器的麦克风、摄像头采集权限。</li><br> 处理建议：<li>提示用户授权摄像头/麦克风访问才可以进行音视频通话。</li><li>若是由于系统关闭了浏览器权限，rtcError 会有  {@link RtcError.handler rtcError.handler} 方法，调用该方法可以跳转到系统权限设置中，方便用户开启权限。</li>|
     * | 5303 | 由浏览器抛出的 NotReadableError(DOMException)  <br> 原因：尽管用户已经授权使用相应的设备，但是由于操作系统上某个硬件、浏览器或者网页层面发生的错误或者其他应用占用了设备导致设备无法被浏览器访问。<br> 处理建议：根据浏览器的报错信息处理，并提示用户：“暂时无法访问摄像头/麦克风，请确保当前没有其他应用请求访问摄像头/麦克风，并重试” |
     * | 5304 | 由浏览器抛出的 OverconstrainedError(DOMException)  <br> 原因：cameraId/microphoneId 参数的值无效 <br> 处理建议：检查 cameraId/microphoneId 是否是通过获取设备信息接口返回的值 |
     * | 5305 | 由浏览器抛出的 InvalidStateError(DOMException)  <br> 原因：当前页面未产生交互，页面不是完全激活的 <br> 处理建议：建议经过用户与页面产生点击交互后，再开启摄像头麦克风 |
     * | 5306 | 由浏览器抛出的 SecurityError(DOMException)  <br> 原因：由于系统安全策略禁止使用设备<br> 处理建议：检查系统是否限制使用设备，并建议经过用户与页面产生点击交互后，再开启摄像头麦克风 |
     * | 5307 | 由浏览器抛出的 AbortError(DOMException)  <br> 原因：由于某些未知原因导致设备无法被使用 <br> 处理建议：建议更换设备或者浏览器，重新检测设备是否正常 |
     * | 5308 | 摄像头采集异常，经过 SDK 重试任然无法恢复采集。 <br> 原因：摄像头异常或者用户手动关闭了浏览器的采集权限导致 <br> 处理建议：提示用户摄像头采集异常，引导用户检查摄像头是否正常、是否有采集权限 |
     * | 5309 | 麦克风采集异常，经过 SDK 重试任然无法恢复采集。 <br> 原因：麦克风异常或者用户手动关闭了浏览器的采集权限导致 <br> 处理建议：提示用户麦克风采集异常，引导用户检查麦克风是否正常、是否有采集权限 |
     *
     * 参考资料：[getUserMedia 异常](<https://developer.mozilla.org/zh-CN/docs/Web/API/MediaDevices/getUserMedia#异常>) 和 [getDisplayMedia 异常](<https://developer.mozilla.org/zh-CN/docs/Web/API/MediaDevices/getDisplayMedia#异常>)
     * @default 5300
     * @memberof module:ERROR_CODE
     * @example
     * trtc.startLocalVideo(...).catch(function(rtcError) {
     *  if(rtcError.code == TRTC.ERROR_CODE.DEVICE_ERROR) {
     *    // 引导用户检查设备
     *    // 以下为可选代码
     *    switch(rtcError.extraCode) {
     *      case 5301:
     *        // 找不到摄像头或者麦克风，引导用户检查麦克风和摄像头是否正常。
     *        break;
     *      case 5302:
     *        if (error.handler) {
     *          // 提示用户系统关闭了浏览器的摄像头、麦克风、屏幕分享采集权限，即将跳转至系统权限设置 APP，请打开相关权限后，重启浏览器重试。
     *        } else {
     *          // 引导用户允许摄像头、麦克风、屏幕分享采集权限
     *        }
     *        break;
     *      // ...
     *    }
     *  }
     * })
     */
  DEVICE_ERROR: 5300,
  /**
     * 收到服务端返回的异常数据时抛出该错误码<br>
     * 以下接口出现异常时会抛出该错误码：`enterRoom`，`startLocalVideo`, `startLocalAudio`, `startScreenShare`, `startRemoteVideo`, `switchRole` <br>
     * 处理建议：服务端异常通常在开发阶段处理，常见的异常有：传入的 userSig 过期，腾讯云账号欠费，未开通TRTC服务等，服务端返回异常数据有以下原因。
     * | extraCode  | 描述                                                                                                           |
     * | :-----  | :------------------------------------------------------------------------------------------------------------- |
     * | 5401 | 该功能需要购买套餐 |
     * | -8      | sdkAppId 不正确，请检查 sdkAppId 是否正确填写        |
     * | -10012  | 未传入 roomId 或者 roomId 不符合规范, 如需使用 string 类型的 roomId，请在调用 trtc.enterRoom 时使用 strRoomId |
     * | -10015  | 服务端获取服务器节点失败 |
     * | -10016  | 服务端内部通信超时，超时时间 3s |
     * | -100006 | 检查权限失败，启用高级权限控制后，请检查 {@link https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/TRTC.html#enterRoom trtc.enterRoom} 携带的 privateMapKey 参数是否正确。请查看 {@link https://cloud.tencent.com/document/product/647/32240 开启高级权限设置 }  |
     * | -100013 | 客户服务欠费, 请登录 {@link https://console.cloud.tencent.com/trtc 实时音视频控制台}，单击您创建的应用，单击【帐号信息】，在帐号信息面板即可确认服务状态 |
     * | -100021 | 服务端过载，进房失败 |
     * | -100022 | 服务器分配失败 |
     * | -100024 | 未开通 TRTC 服务导致进房失败，请到 {@link https://console.cloud.tencent.com/im IM 控制台} 为您的应用开通 TRTC 服务|
     * | -102006 | 流控定义的错误码（add user failed） |
     * | -102010 | 启用高级权限控制后，用户没有创建房间的权限，请查看 {@link https://cloud.tencent.com/document/product/647/32240 开启高级权限设置 } |
     * | -102023 | 请求参数错误（后端接口服务产生的请求参数错误） |
     * | 70001   | userSig 过期，请尝试重新生成。如果是刚生成就过期，请检查有效期填写的是否过小或者误填为 0                       |
     * | 70002   | userSig 长度为 0，请确认签名计算是否正确，访问 sign_src 获取计算签名的傻瓜式源码，核对参数，确保签名计算正确性 |
     * | 70003   | userSig 校验失败，请确认下 userSig 内容是否被截断，例如缓冲区长度不够导致的内容截断                            |
     * | 70004   | userSig 校验失败，请确认下 userSig 内容是否被截断，例如缓冲区长度不够导致的内容截断                             |
     * | 70005   | userSig 校验失败，通过工具来验证生成的 userSig 是否正确                                                        |
     * | 70006   | userSig 校验失败，通过工具来验证生成的 userSig 是否正确                                                        |
     * | 70007   | userSig 校验失败，通过工具来验证生成的 userSig 是否正确                                                        |
     * | 70008   | userSig 校验失败，通过工具来验证生成的 userSig 是否正确                                                        |
     * | 70009   | 用业务公钥验证 userSig 失败，请确认生成的 userSig 使用的私钥和 sdkAppId 是否对应                               |
     * | 70010   | userSig 校验失败，通过工具来验证生成的 userSig 是否正确                                                        |
     * | 70013   | userSig 中 userId 与请求时的 userId 不匹配，请检查登录时填写的 userId 与 userSig 中的是否一致                  |
     * | 70014   | userSig 中 sdkAppId 与请求时的 sdkAppId 不匹配，请检查登录时填写的 sdkAppId 与 userSig 中的是否一致            |
     * | 70015   | 未找到该 sdkAppId 和帐号类型对应的验证方式，请确认是否已进行帐号集成操作                                       |
     * | 70016   | 拉取到的公钥长度为 0，请确认是否已上传公钥，如果是重新上传的公钥需要十分钟后再尝试                             |
     * | 70017   | 内部第三方票据验证超时，请重试，如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                      |
     * | 70018   | 内部验证第三方票据失败                                                                                         |
     * | 70019   | 通过 HTTPS 方式验证的票据字段为空，请正确填写 userSig                                                          |
     * | 70020   | sdkAppId 未找到，请确认是否已在腾讯云上配置                                                                    |
     * | 70052   | userSig 已经失效，请重新生成，再次尝试                                                                         |
     * | 70101   | 请求包信息为空                                                                                                 |
     * | 70102   | 请求包帐号类型错误                                                                                             |
     * | 70103   | 电话号码格式错误                                                                                               |
     * | 70104   | 邮箱格式错误                                                                                                   |
     * | 70105   | TLS 帐号格式错误                                                                                               |
     * | 70106   | 非法帐号格式类型                                                                                               |
     * | 70107   | userId 没有注册                                                                                                |
     * | 70113   | 批量数量不合法                                                                                                 |
     * | 70114   | 安全原因被限制                                                                                                 |
     * | 70115   | uin 不是对应 sdkAppId 的开发者 uin                                                                             |
     * | 70140   | sdkAppId 和 acctype 不匹配                                                                                     |
     * | 70145   | 帐号类型错误                                                                                                   |
     * | 70169   | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
     * | 70201   | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
     * | 70202   | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
     * | 70203   | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
     * | 70204   | sdkAppId 没有对应的 acctype                                                                                    |
     * | 70205   | 查找 acctype 失败，请重试                                                                                      |
     * | 70206   | 请求中批量数量不合法                                                                                           |
     * | 70207   | 内部错误，请重试                                                                                               |
     * | 70208   | 内部错误，请重试                                                                                               |
     * | 70209   | 获取开发者 uin 标志失败                                                                                        |
     * | 70210   | 请求中 uin 为非开发者 uin                                                                                      |
     * | 70211   | 请求中 uin 非法                                                                                                |
     * | 70212   | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
     * | 70213   | 访问内部数据失败，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                            |
     * | 70214   | 验证内部票据失败                                                                                               |
     * | 70221   | 登录状态无效，请使用 UserSig 重新鉴权                                                                          |
     * | 70222   | 内部错误，请重试                                                                                               |
     * | 70225   | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
     * | 70231   | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
     * | 70236   | 验证 user signature 失败                                                                                       |
     * | 70308   | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
     * | 70346   | 票据校验失败。                                                                                                 |
     * | 70347   | 票据因过期原因校验失败                                                                                         |
     * | 70348   | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
     * | 70362   | 内部超时，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
     * | 70401   | 内部错误，请重试                                                                                               |
     * | 70402   | 参数非法。请检查必填字段是否填充，或者字段的填充是否满足协议要求                                               |
     * | 70403   | 发起操作者不是 App 管理员，没有权限操作                                                                        |
     * | 70050   | 因失败且重试次数过多导致被限制，请检查票据是否正确，一分钟之后再试                                             |
     * | 70051   | 帐号已被拉入黑名单，请联系 TLS 帐号支持，QQ：3268519604                                                        |

     * @default 5400
     * @memberof module:ERROR_CODE
     */
  SERVER_ERROR: 5400,
  /**
     * 在满足 API 调用要求的情况下，SDK 经过多次重试仍然无法解决的异常，通常是由于浏览器、网络的问题造成。<br>
     * 以下接口出现异常时会抛出该错误码：`enterRoom`，`startLocalVideo`, `startLocalAudio`, `startScreenShare`, `startRemoteVideo`, `switchRole` <br>
     * 处理建议：
     * - 确认通信必需的域名和端口是否满足您的网络环境要求，参考文档[应对防火墙限制及设置代理](https://cloud.tencent.com/document/product/647/34399#WebRTC-.E9.9C.80.E8.A6.81.E9.85.8D.E7.BD.AE.E5.93.AA.E4.BA.9B.E7.AB.AF.E5.8F.A3.E6.88.96.E5.9F.9F.E5.90.8D.E4.B8.BA.E7.99.BD.E5.90.8D.E5.8D.95.EF.BC.9F)
     * - 其他问题需要联系工程师处理 [在线客服](https://cloud.tencent.com/act/event/Online_service?from=doc_647)
     *
     * | extraCode  | 描述                                                                                                           |
     * | :-----  | :------------------------------------------------------------------------------------------------------------- |
     * | 5501 | 防火墙受限：SDK 经过多次重试后，依然无法建立媒体连接，会导致无法推流及无法拉流。<br/>处理建议：参考教程 [应对防火墙受限](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/tutorial-34-advanced-proxy.html)|
     * | 5502 | 重进房失败：当用户经历超过 30s 的断网后，SDK 会尝试重进房恢复通话，但可能由于 userSig 过期导致重进房失败，此时会抛出该错误。<br/>处理建议：遇到此错误时，您可以使用最新的 userSig 重新调用 {@link TRTC#enterRoom TRTC.enterRoom} 进房 |
     * | 5503 | 重进房失败：当用户经历超过 30s 的断网后，SDK 会尝试重进房恢复通话，但可能由于 userSig 过期导致重进房失败，此时会抛出该错误。<br/>处理建议：遇到此错误时，您可以使用最新的 userSig 重新调用 {@link TRTC#enterRoom TRTC.enterRoom} 进房 |
     * @default 5500
     * @memberof module:ERROR_CODE
     */
  OPERATION_FAILED: 5500,
  /**
     * 中止 API 执行时抛出该错误码。在不满足 API 生命周期的调用或重复调用时 API 会中止执行，避免无意义的操作。<br>例如：连续调用 enterRoom，startLocalXxx等接口，在没有进房就调用退房。<br>
     * 以下接口出现异常时会抛出该错误码：`enterRoom`，`startLocalVideo`, `startLocalAudio`, `startScreenShare`, `startRemoteVideo`, `switchRole` <br>
     * 处理建议：捕获并识别该错误码，然后在业务逻辑规避不必要的调用，或者也可以不做任何处理，因为 SDK 做了无副作用处理，您只需在 catch 时识别该错误码并忽略。
     * @default 5998
     * @memberof module:ERROR_CODE
     */
  OPERATION_ABORT: 5998,
  /**
    * 未知错误或者未被定义的错误<br>
    * 处理建议：联系工程师处理 [在线客服](https://cloud.tencent.com/act/event/Online_service?from=doc_647)
    * @default 5999
    * @memberof module:ERROR_CODE
    */
  UNKNOWN_ERROR: 5999
};
/**
 * @private
 * @example
 * ErrorCodeDictionary[ERROR_CODE.INVALID_PARAMETER] === 'INVALID_PARAMETER'
 * ErrorCodeDictionary.INVALID_PARAMETER === 5000
 */
export var ErrorCodeDictionary;
(function (ErrorCodeDictionary) {
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_PARAMETER = 5000] = 'INVALID_PARAMETER';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_PARAMETER_REQUIRED = 5001] = 'INVALID_PARAMETER_REQUIRED';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_PARAMETER_TYPE = 5002] = 'INVALID_PARAMETER_TYPE';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_PARAMETER_EMPTY = 5003] = 'INVALID_PARAMETER_EMPTY';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_PARAMETER_INSTANCE = 5004] = 'INVALID_PARAMETER_INSTANCE';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_PARAMETER_RANGE = 5005] = 'INVALID_PARAMETER_RANGE';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_PARAMETER_LESS_THAN_ZERO = 5006] = 'INVALID_PARAMETER_LESS_THAN_ZERO';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_PARAMETER_MIN = 5007] = 'INVALID_PARAMETER_MIN';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_PARAMETER_MAX = 5008] = 'INVALID_PARAMETER_MAX';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_ELEMENT_ID = 5009] = 'INVALID_ELEMENT_ID';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_ELEMENT_ID_TYPE = 5010] = 'INVALID_ELEMENT_ID_TYPE';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_STREAM_ID = 5011] = 'INVALID_STREAM_ID';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_ROOM_ID_STRING = 5012] = 'INVALID_ROOM_ID_STRING';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_ROOM_ID_INTEGER = 5013] = 'INVALID_ROOM_ID_INTEGER';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_STREAM_TYPE = 5014] = 'INVALID_STREAM_TYPE';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_ROOM_ID_REQUIED = 5015] = 'INVALID_ROOM_ID_REQUIED';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_ROOM_ID_INTEGER_STRING = 5016] = 'INVALID_ROOM_ID_INTEGER_STRING';
  // INVALID_OPERATION
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_OPERATION = 5100] = 'INVALID_OPERATION';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_OPERATION_NOT_JOINED = 5101] = 'INVALID_OPERATION_NOT_JOINED';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_OPERATION_REMOTE_USER_NOT_EXIST = 5102] = 'INVALID_OPERATION_REMOTE_USER_NOT_EXIST';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_OPERATION_STREAM_TYPE_NOT_EXIST = 5103] = 'INVALID_OPERATION_STREAM_TYPE_NOT_EXIST';
  ErrorCodeDictionary[ErrorCodeDictionary.INVALID_OPERATION_REPEAT_CALL = 5104] = 'INVALID_OPERATION_REPEAT_CALL';
  // ENV_NOT_SUPPORTED
  ErrorCodeDictionary[ErrorCodeDictionary.ENV_NOT_SUPPORTED = 5200] = 'ENV_NOT_SUPPORTED';
  ErrorCodeDictionary[ErrorCodeDictionary.NOT_SUPPORTED_HTTP = 5201] = 'NOT_SUPPORTED_HTTP';
  ErrorCodeDictionary[ErrorCodeDictionary.NOT_SUPPORTED_WEBRTC = 5202] = 'NOT_SUPPORTED_WEBRTC';
  ErrorCodeDictionary[ErrorCodeDictionary.NOT_SUPPORTED_H264_ENCODE = 5203] = 'NOT_SUPPORTED_H264_ENCODE';
  ErrorCodeDictionary[ErrorCodeDictionary.NOT_SUPPORTED_H264_DECODE = 5204] = 'NOT_SUPPORTED_H264_DECODE';
  ErrorCodeDictionary[ErrorCodeDictionary.NOT_SUPPORTED_SCREEN_SHARE = 5205] = 'NOT_SUPPORTED_SCREEN_SHARE';
  ErrorCodeDictionary[ErrorCodeDictionary.NOT_SUPPORTED_SMALL_VIDEO = 5206] = 'NOT_SUPPORTED_SMALL_VIDEO';
  ErrorCodeDictionary[ErrorCodeDictionary.NOT_SUPPORTED_SEI = 5207] = 'NOT_SUPPORTED_SEI';
  // DEVICE
  ErrorCodeDictionary[ErrorCodeDictionary.DEVICE_ERROR = 5300] = 'DEVICE_ERROR';
  ErrorCodeDictionary[ErrorCodeDictionary.DEVICE_NOT_FOUND_ERROR = 5301] = 'DEVICE_NOT_FOUND_ERROR';
  ErrorCodeDictionary[ErrorCodeDictionary.DEVICE_NOT_ALLOWED_ERROR = 5302] = 'DEVICE_NOT_ALLOWED_ERROR';
  ErrorCodeDictionary[ErrorCodeDictionary.DEVICE_NOT_READABLE_ERROR = 5303] = 'DEVICE_NOT_READABLE_ERROR';
  ErrorCodeDictionary[ErrorCodeDictionary.DEVICE_OVERCONSTRAINED_ERROR = 5304] = 'DEVICE_OVERCONSTRAINED_ERROR';
  ErrorCodeDictionary[ErrorCodeDictionary.DEVICE_INVALID_STATE_ERROR = 5305] = 'DEVICE_INVALID_STATE_ERROR';
  ErrorCodeDictionary[ErrorCodeDictionary.DEVICE_SECURITY_ERROR = 5306] = 'DEVICE_SECURITY_ERROR';
  ErrorCodeDictionary[ErrorCodeDictionary.DEVICE_ABORT_ERROR = 5307] = 'DEVICE_ABORT_ERROR';
  ErrorCodeDictionary[ErrorCodeDictionary.CAMERA_RECOVER_FAILED = 5308] = 'CAMERA_RECOVER_FAILED';
  ErrorCodeDictionary[ErrorCodeDictionary.MICROPHONE_RECOVER_FAILED = 5309] = 'MICROPHONE_RECOVER_FAILED';
  // SERVER_ERROR
  ErrorCodeDictionary[ErrorCodeDictionary.SERVER_ERROR = 5400] = 'SERVER_ERROR';
  ErrorCodeDictionary[ErrorCodeDictionary.ACCOUNT_NO_MONEY = -100013] = 'ACCOUNT_NO_MONEY';
  // -100013
  // OPERATION_FAILED
  ErrorCodeDictionary[ErrorCodeDictionary.OPERATION_FAILED = 5500] = 'OPERATION_FAILED';
  ErrorCodeDictionary[ErrorCodeDictionary.FIREWALL_RESTRICTION = 5501] = 'FIREWALL_RESTRICTION';
  ErrorCodeDictionary[ErrorCodeDictionary.REJOIN_FAILED = 5502] = 'REJOIN_FAILED';
  ErrorCodeDictionary[ErrorCodeDictionary.EVENT_HANDLER_ERROR = 5503] = 'EVENT_HANDLER_ERROR';
  ErrorCodeDictionary[ErrorCodeDictionary.VIDEO_CONTEXT_ERROR = 5504] = 'VIDEO_CONTEXT_ERROR';
  // OPERATION_ABORT
  ErrorCodeDictionary[ErrorCodeDictionary.OPERATION_ABORT = 5998] = 'OPERATION_ABORT';
  // UNKNOWN_ERROR
  ErrorCodeDictionary[ErrorCodeDictionary.UNKNOWN_ERROR = 5999] = 'UNKNOWN_ERROR';
}(ErrorCodeDictionary || (ErrorCodeDictionary = {})));
// 需要特殊提示的后端错误码，通过强制弹框提示
export var ServerErrorCode;
(function (ServerErrorCode) {
  // 账号欠费 -100013
  ServerErrorCode[ServerErrorCode.ACCOUNT_NO_MONEY = -100013] = 'ACCOUNT_NO_MONEY';
  // 账号未开通 TRTC -100024
  ServerErrorCode[ServerErrorCode.TRTC_NOT_ACTIVATED = -100024] = 'TRTC_NOT_ACTIVATED';
  // 启用高级权限控制后，用户没有创建房间的权限
  ServerErrorCode[ServerErrorCode.PrivateMapKey = -102010] = 'PrivateMapKey';
}(ServerErrorCode || (ServerErrorCode = {})));
