import { getValueType, isFunction, isString } from 'trtc-js-sdk-core/src/utils/utils';
import { ErrorCodeDictionary } from './error-code';
import { ErrorInfo } from 'trtc-js-sdk-core';
// 根据error code 生成错误信息
// xx操作产生了xx异常，SDK经过xx，仍然无法解决。建议的解决方案/详细解决方案的文档链接
/**
 * 获取 error message
 * 根据 extraCode 匹配对应的 error message
 * error message 可能是一下两种数据类型
 * 1. 函数
 * 不同的异常，传入的参数可能不一样，必传: fnName
 * 2. 字符串
 * @example
 * getErrorMessage({errorName, params})
 */
export function getErrorMessage({ code, params, enableDocLink = false }) {
  let message = '';
  let tempMessage;
  const key = ErrorCodeDictionary[code];
  try {
    tempMessage = ErrorMessage[key];
  }
  catch (error) {
    tempMessage = ErrorMessage.UNKNOWN_ERROR;
  }
  if (isFunction(tempMessage)) {
    message = tempMessage(params);
  }
  else if (isString(tempMessage)) {
    message = tempMessage;
  }
  if (enableDocLink) {
    message += ' doc:';
  }
  // TODO: 一期发布海外，中文提示后续补齐
  return message;
}
export const ErrorMessage = {
  ...ErrorInfo,
  /**
   *
   * #### ##    ## ##     ##    ###    ##       #### ########          ########     ###    ########     ###    ##     ## ######## ######## ######## ########
   *  ##  ###   ## ##     ##   ## ##   ##        ##  ##     ##         ##     ##   ## ##   ##     ##   ## ##   ###   ### ##          ##    ##       ##     ##
   *  ##  ####  ## ##     ##  ##   ##  ##        ##  ##     ##         ##     ##  ##   ##  ##     ##  ##   ##  #### #### ##          ##    ##       ##     ##
   *  ##  ## ## ## ##     ## ##     ## ##        ##  ##     ##         ########  ##     ## ########  ##     ## ## ### ## ######      ##    ######   ########
   *  ##  ##  ####  ##   ##  ######### ##        ##  ##     ##         ##        ######### ##   ##   ######### ##     ## ##          ##    ##       ##   ##
   *  ##  ##   ###   ## ##   ##     ## ##        ##  ##     ##         ##        ##     ## ##    ##  ##     ## ##     ## ##          ##    ##       ##    ##
   * #### ##    ##    ###    ##     ## ######## #### ########  ####### ##        ##     ## ##     ## ##     ## ##     ## ########    ##    ######## ##     ##
   *
  */
  /**
   *  INVALID_PARAMETER 参数错误
   * @param param
   * @returns
   */
  INVALID_PARAMETER({ fnName }) {
    return `the parameters of the '${fnName}' you called does not meet the requirements, please check the API documentation.`;
  },
  INVALID_PARAMETER_REQUIRED({ key, rule, fnName, value }) {
    return `'${key || rule.name}' is a required param when calling ${fnName}(), received: ${value}.`;
  },
  INVALID_PARAMETER_TYPE({ key, rule, fnName, value }) {
    const keyName = `${key || rule.name}`;
    let type = '';
    if (Array.isArray(rule.type)) {
      type = rule.type.join('|');
    }
    else {
      type = rule.type;
    }
    return `'${keyName}' must be type of ${type} when calling ${fnName}(), received type: ${getValueType(value)}.`;
  },
  INVALID_PARAMETER_EMPTY({ key, rule, fnName, value }) {
    return `'${key || rule.name}' cannot be '${value}' when calling ${fnName}().`;
  },
  INVALID_PARAMETER_INSTANCE({ key, rule, fnName, value }) {
    const keyName = `${key || rule.name}`;
    const name = `${rule.instanceOf.name || rule.instanceOf}`;
    return `'${keyName}' must be instanceof ${name} when calling ${fnName}(), received type: ${getValueType(value)}.`;
  },
  INVALID_PARAMETER_RANGE({ key, rule, fnName, value }) {
    return `'${key || rule.name}' must be one of ${rule.values.join('|')} when calling ${fnName}(), received: ${value}.`;
  },
  INVALID_PARAMETER_LESS_THAN_ZERO({ key, rule, fnName }) {
    return `'${key || rule.name}' cannot be less than 0 when calling ${fnName}().`;
  },
  INVALID_PARAMETER_MIN({ key, rule, value }) {
    return `the min value of ${key || rule.name} is ${rule.min}, received: ${value}.`;
  },
  INVALID_PARAMETER_MAX({ key, rule, value }) {
    return `the max value of ${key || rule.name} is ${rule.max}, received: ${value}.`;
  },
  INVALID_ELEMENT_ID({ key, fnName }) {
    return `'${key}' is not found in the document object when calling ${fnName}().`;
  },
  INVALID_ELEMENT_ID_TYPE({ key, fnName, type }) {
    return `the element corresponding to '${key}' must be instanceof HTMLElement when calling ${fnName}(), received: ${type}.`;
  },
  INVALID_STREAM_ID({ key }) {
    return `'${key}' can only consist of uppercase and lowercase english letters (a-zA-Z), numbers (0-9), hyphens and underscores.`;
  },
  INVALID_ROOM_ID_STRING({ key }) {
    return `'${key}' must be a valid string.`;
  },
  INVALID_ROOM_ID_INTEGER({ key }) {
    return `'${key}' must be an integer between [1, 4294967294].`;
  },
  INVALID_ROOM_ID_INTEGER_STRING({ key }) {
    return `'${key}' must be an integer but go a string, use 'parseInt' to convert it or use 'strRoomId' instead.`;
  },
  INVALID_ROOM_ID_REQUIED() {
    return 'at least one of \'roomId\'(between [1, 4294967294]) and \'strRoomId\'(not empty) is required.';
  },
  INVALID_STREAM_TYPE: ({ fnName }) => `'streamType' is required when 'userId' is not '*', calling ${fnName}()`,

  INVALID_IMAGE_URL: 'The \'src\' param must be filled in when the background type is image.',
  // stream
  /**
   *
   * #### ##    ## ##     ##    ###    ##       #### ########           #######  ########  ######## ########     ###    ######## ####  #######  ##    ##
   *  ##  ###   ## ##     ##   ## ##   ##        ##  ##     ##         ##     ## ##     ## ##       ##     ##   ## ##      ##     ##  ##     ## ###   ##
   *  ##  ####  ## ##     ##  ##   ##  ##        ##  ##     ##         ##     ## ##     ## ##       ##     ##  ##   ##     ##     ##  ##     ## ####  ##
   *  ##  ## ## ## ##     ## ##     ## ##        ##  ##     ##         ##     ## ########  ######   ########  ##     ##    ##     ##  ##     ## ## ## ##
   *  ##  ##  ####  ##   ##  ######### ##        ##  ##     ##         ##     ## ##        ##       ##   ##   #########    ##     ##  ##     ## ##  ####
   *  ##  ##   ###   ## ##   ##     ## ##        ##  ##     ##         ##     ## ##        ##       ##    ##  ##     ##    ##     ##  ##     ## ##   ###
   * #### ##    ##    ###    ##     ## ######## #### ########  #######  #######  ##        ######## ##     ## ##     ##    ##    ####  #######  ##    ##
   *
  */
  /**
   *  INVALID_OPERATION 非法且无效操作
   * @param param
   * @returns
   */
  INVALID_OPERATION({ fnName }) {
    return `the API '${fnName}' you called does not meet the requirements, please check the API documentation.`;
  },
  INVALID_OPERATION_NOT_JOINED({ fnName }) {
    return `cannot ${fnName} because you are not enter room yet.`;
  },
  INVALID_OPERATION_REMOTE_USER_NOT_EXIST({ fnName, value }) {
    return `cannot ${fnName} because remote user(userId: ${value.userId}) does not publishing stream.`;
  },
  INVALID_OPERATION_STREAM_TYPE_NOT_EXIST({ fnName, value }) {
    return `cannot ${fnName} because remote user(userId: ${value.userId}) does not publishing ${value.streamType} video.`;
  },
  INVALID_OPERATION_REPEAT_CALL({ fnName }) {
    return `you are already ${fnName}(), cannot repeated call '${fnName}'.`;
  },
  INVALID_OPERATION_NEED_VIDEO_SOURCE({ fnName }) {
    return `cannot call '${fnName}' because an video source is required, please check if the camera is opened.`;
  },
  INVALID_OPERATION_NEED_AUDIO_SOURCE({ fnName }) {
    return `cannot call '${fnName}' because an audio source is required, please check if the microphone is opened.`;
  },
  /**
   *
   * ######## ##    ## ##     ##         ##    ##  #######  ########          ######  ##     ## ########  ########   #######  ########  ######## ######## ########
   * ##       ###   ## ##     ##         ###   ## ##     ##    ##            ##    ## ##     ## ##     ## ##     ## ##     ## ##     ##    ##    ##       ##     ##
   * ##       ####  ## ##     ##         ####  ## ##     ##    ##            ##       ##     ## ##     ## ##     ## ##     ## ##     ##    ##    ##       ##     ##
   * ######   ## ## ## ##     ##         ## ## ## ##     ##    ##             ######  ##     ## ########  ########  ##     ## ########     ##    ######   ##     ##
   * ##       ##  ####  ##   ##          ##  #### ##     ##    ##                  ## ##     ## ##        ##        ##     ## ##   ##      ##    ##       ##     ##
   * ##       ##   ###   ## ##           ##   ### ##     ##    ##            ##    ## ##     ## ##        ##        ##     ## ##    ##     ##    ##       ##     ##
   * ######## ##    ##    ###    ####### ##    ##  #######     ##    #######  ######   #######  ##        ##         #######  ##     ##    ##    ######## ########
   *
  */
  /**
   *  ENV_NOT_SUPPORTED 能力不支持
   * @param param
   * @returns
   */
  ENV_NOT_SUPPORTED({ fnName }) {
    // 当前浏览器不支持您调用的函数的功能。
    // TODO: 增加浏览器版本提示
    return `the current browser does not support the capability of the function '${fnName}' you are calling, please check the API documentation.`;
  },
  NOT_SUPPORTED_WEBRTC: 'the current browser does not support WebRTC capability, please check the SDK documentation.',
  NOT_SUPPORTED_H264_ENCODE: 'this browser does not support H264 encode.',
  NOT_SUPPORTED_H264_DECODE: 'this browser does not support H264 decode.',
  NOT_SUPPORTED_SCREEN_SHARE: 'this browser does not support screen share, please check the browser version.',
  NOT_SUPPORTED_SMALL_VIDEO: 'this browser does not support small video, please check the browser version.',
  NOT_SUPPORTED_SEI: 'this browser does not support SEI, please check the browser version.',
  NOT_SUPPORTED_WEBGL: 'this browser does not support WebGL, please check the browser version.',
  NOT_SUPPORTED_CHROME_VERSION({ fnName, value }) {
    return `cannot call ${fnName} because the browser version is too low, please upgrade to version ${value} or above`;
  },
  /**
   *
   * ########  ######## ##     ## ####  ######  ########         ######## ########  ########   #######  ########
   * ##     ## ##       ##     ##  ##  ##    ## ##               ##       ##     ## ##     ## ##     ## ##     ##
   * ##     ## ##       ##     ##  ##  ##       ##               ##       ##     ## ##     ## ##     ## ##     ##
   * ##     ## ######   ##     ##  ##  ##       ######           ######   ########  ########  ##     ## ########
   * ##     ## ##        ##   ##   ##  ##       ##               ##       ##   ##   ##   ##   ##     ## ##   ##
   * ##     ## ##         ## ##    ##  ##    ## ##               ##       ##    ##  ##    ##  ##     ## ##    ##
   * ########  ########    ###    ####  ######  ######## ####### ######## ##     ## ##     ##  #######  ##     ##
   *
  */
  /**
  * DEVICE_ERROR 系统设备异常
  * @param param
  * @returns
  */
  DEVICE_ERROR({ fnName, error }) {
    return `'${fnName}' got device exception${error ? `, error: ${error.toString()}.` : '.'}`;
  },
  DEVICE_NOT_FOUND_ERROR({ fnName, deviceType = getDeviceTypeFromFnName(fnName), error }) {
    // 没有找到可用的设备，建议进行设备检测，确认设备是否存在且能正常使用
    // getXxxMedia 抛出的原始异常
    return `NotFoundError, no ${deviceType} detected, please check your device and the configuration on '${fnName}'${error ? `, error: ${error.toString()}.` : '.'}`;
  },
  DEVICE_NOT_ALLOWED_ERROR({ fnName, deviceType = getDeviceTypeFromFnName(fnName), error }) {
    // 建议用户允许当前页面获取设备的使用权限
    return `NotAllowedError, you have disabled ${deviceType} access, please allow the current application to use the ${deviceType}${error ? `, error: ${error.toString()}.` : '.'}`;
  },
  DEVICE_NOT_READABLE_ERROR({ fnName, deviceType = getDeviceTypeFromFnName(fnName), error }) {
    // 设备被占用了，请检查设备是否被其他应用预先占用了。
    // a hardware error occurred at the operating system, browser, or Web page level which prevented access to the device.
    return `NotReadableError, the ${deviceType} maybe in use by another APP, please check if the device is pre-occupied by another APP.`;
  },
  DEVICE_OVERCONSTRAINED_ERROR({ fnName, deviceType = getDeviceTypeFromFnName(fnName), error }) {
    // 设备ID不准确，请检查传入的设备ID是否准确。
    return `OverconstrainedError, the device ID is incorrect, please check whether the device ID passed in is correct${error ? `, error: ${error.toString()}.` : '.'}`;
  },
  DEVICE_INVALID_STATE_ERROR({ fnName, deviceType = getDeviceTypeFromFnName(fnName), error }) {
    // 原因：当前页面未产生交互，页面不是完全激活的 <br> 处理建议：建议经过用户与页面产生点击交互后，再开启摄像头麦克风。
    return `InvalidStateError, after the user clicks and interacts with the page, turn on the ${deviceType}${error ? `, error: ${error.toString()}.` : '.'}`;
  },
  DEVICE_SECURITY_ERROR({ fnName, deviceType = getDeviceTypeFromFnName(fnName), error }) {
    // 原因：由于系统安全策略禁止使用设备<br> 处理建议：检查系统安全策略是否限制使用设备，并建议经过用户与页面产生点击交互后，再开启摄像头麦克风。
    return `SecurityError, check whether the system security policy restricts the use of the ${deviceType}, and it is recommended to turn on the ${deviceType} after the user interacts with the page${error ? `, error: ${error.toString()}.` : '.'}`;
  },
  DEVICE_ABORT_ERROR({ fnName, deviceType = getDeviceTypeFromFnName(fnName), error }) {
    // 原因：由于系统内部原因导致设备无法被使用 <br> 处理建议：建议更换设备或者浏览器，重新检测设备是否正常。
    return `AbortError, an unknown exception in the system makes the device unusable, recommended to change the device or browser and re-check whether the device is normal${error ? ` error: ${error.toString()}.` : '.'}`;
  },
  CAMERA_RECOVER_FAILED({ error }) {
    return `camera recover capture failed ${error?.name || ''}: ${error?.originMessage || error?.message}`;
  },
  MICROPHONE_RECOVER_FAILED({ error }) {
    return `microphone recover capture failed ${error?.name || ''}: ${error?.originMessage || error?.message}`;
  },
  /**
   *
   *  #######  ########  ######## ########     ###    ######## ####  #######  ##    ##         ########    ###    #### ##       ######## ########
   * ##     ## ##     ## ##       ##     ##   ## ##      ##     ##  ##     ## ###   ##         ##         ## ##    ##  ##       ##       ##     ##
   * ##     ## ##     ## ##       ##     ##  ##   ##     ##     ##  ##     ## ####  ##         ##        ##   ##   ##  ##       ##       ##     ##
   * ##     ## ########  ######   ########  ##     ##    ##     ##  ##     ## ## ## ##         ######   ##     ##  ##  ##       ######   ##     ##
   * ##     ## ##        ##       ##   ##   #########    ##     ##  ##     ## ##  ####         ##       #########  ##  ##       ##       ##     ##
   * ##     ## ##        ##       ##    ##  ##     ##    ##     ##  ##     ## ##   ###         ##       ##     ##  ##  ##       ##       ##     ##
   *  #######  ##        ######## ##     ## ##     ##    ##    ####  #######  ##    ## ####### ##       ##     ## #### ######## ######## ########
   *
  */
  /**
   * OPERATION_FAILED API 操作失败
   * @param param
   * @returns
   */
  OPERATION_FAILED({ fnName, error }) {
    return `'${fnName}' failed, reason: ${error?.toString()}`;
  },
  FIREWALL_RESTRICTION() {
    return 'media connection failure due to firewall restrictions, please try to change your network.';
  },
  EVENT_HANDLER_ERROR({ eventName }) {
    return `an error was caught on trtc.on('${eventName}', handler), please check your code on 'handler'.`;
  },
  VIDEO_CONTEXT_ERROR({ reason, error }) {
    return `video context error ${reason} ${error?.name || ''} ${error?.message || ''}`;
  },
  /**
   *
   *  ######  ######## ########  ##     ## ######## ########          ######## ########  ########   #######  ########
   * ##    ## ##       ##     ## ##     ## ##       ##     ##         ##       ##     ## ##     ## ##     ## ##     ##
   * ##       ##       ##     ## ##     ## ##       ##     ##         ##       ##     ## ##     ## ##     ## ##     ##
   *  ######  ######   ########  ##     ## ######   ########          ######   ########  ########  ##     ## ########
   *       ## ##       ##   ##    ##   ##  ##       ##   ##           ##       ##   ##   ##   ##   ##     ## ##   ##
   * ##    ## ##       ##    ##    ## ##   ##       ##    ##          ##       ##    ##  ##    ##  ##     ## ##    ##
   *  ######  ######## ##     ##    ###    ######## ##     ## ####### ######## ##     ## ##     ##  #######  ##     ##
   *
  */
  /**
  * SERVER_ERROR 后端返回失败
  * @param param
  * @returns
  */
  SERVER_ERROR({ fnName, error }) {
    return `'${fnName}' got server error: ${error?.toString()}, please check the SDK documentation.`;
  },
  NEED_TO_BUY({ value, url }) {
    return `You need to buy packages for ${value}. Refer to: ${url}`;
  },
  // 特殊后台错误，进行特殊提示
  ACCOUNT_NO_MONEY: ({ fnParams }) => `your TRTC account run out of credit, please recharge.${fnParams.sdkAppId ? ` SDKAppId: ${fnParams.sdkAppId}` : ''}`,
  /**
   *
   *  #######  ########  ######## ########     ###    ######## ####  #######  ##    ##            ###    ########   #######  ########  ########
   * ##     ## ##     ## ##       ##     ##   ## ##      ##     ##  ##     ## ###   ##           ## ##   ##     ## ##     ## ##     ##    ##
   * ##     ## ##     ## ##       ##     ##  ##   ##     ##     ##  ##     ## ####  ##          ##   ##  ##     ## ##     ## ##     ##    ##
   * ##     ## ########  ######   ########  ##     ##    ##     ##  ##     ## ## ## ##         ##     ## ########  ##     ## ########     ##
   * ##     ## ##        ##       ##   ##   #########    ##     ##  ##     ## ##  ####         ######### ##     ## ##     ## ##   ##      ##
   * ##     ## ##        ##       ##    ##  ##     ##    ##     ##  ##     ## ##   ###         ##     ## ##     ## ##     ## ##    ##     ##
   *  #######  ##        ######## ##     ## ##     ##    ##    ####  #######  ##    ## ####### ##     ## ########   #######  ##     ##    ##
   *
  */
  /**
  * SERVER_ERROR 合法但是无效的操作
  * @param param
  * @returns
  */
  OPERATION_ABORT({ fnName }) {
    // TODO: 输出中断操作的原因
    return `'${fnName}' abort`;
  },
  /**
   *
   * ##     ## ##    ## ##    ## ##    ##  #######  ##      ## ##    ##         ######## ########  ########   #######  ########
   * ##     ## ###   ## ##   ##  ###   ## ##     ## ##  ##  ## ###   ##         ##       ##     ## ##     ## ##     ## ##     ##
   * ##     ## ####  ## ##  ##   ####  ## ##     ## ##  ##  ## ####  ##         ##       ##     ## ##     ## ##     ## ##     ##
   * ##     ## ## ## ## #####    ## ## ## ##     ## ##  ##  ## ## ## ##         ######   ########  ########  ##     ## ########
   * ##     ## ##  #### ##  ##   ##  #### ##     ## ##  ##  ## ##  ####         ##       ##   ##   ##   ##   ##     ## ##   ##
   * ##     ## ##   ### ##   ##  ##   ### ##     ## ##  ##  ## ##   ###         ##       ##    ##  ##    ##  ##     ## ##    ##
   *  #######  ##    ## ##    ## ##    ##  #######   ###  ###  ##    ## ####### ######## ##     ## ##     ##  #######  ##     ##
   *
  */
  /**
   * UNKNOWN_ERROR 未知错误
   * @param param
   * @returns
  */
  UNKNOWN_ERROR({ fnName, error }) {
    return `'${fnName}' throw unknown exception${error ? `, error: ${error.toString()}.` : '.'}`;
  }
};
export const ErrorMessageCN = {
  // INVALID_PARAMETER
  INVALID_PARAMETER_REQUIRED({ key, rule, fnName, value }) {
    return `调用 ${fnName}() 方法的时候 '${key || rule.name}' 是必须的参数, 收到的值为: ${value}。`;
  },
  INVALID_PARAMETER_TYPE({ key, rule, fnName, value }) {
    const keyName = `${key || rule.name}`;
    let type = '';
    if (Array.isArray(rule.type)) {
      type = rule.type.join('|');
    }
    else {
      type = rule.type;
    }
    return `调用 ${fnName}() 方法的时候 '${keyName}' 必须是 ${type} 类型, 收到的类型是: ${getValueType(value)}。`;
  },
  INVALID_PARAMETER_EMPTY({ key, rule, fnName, value }) {
    return `调用 ${fnName}() 的时候 '${key || rule.name}' 不能是 '${value}'。`;
  },
  INVALID_PARAMETER_INSTANCE({ key, rule, fnName, value }) {
    const keyName = `${key || rule.name}`;
    const name = `${rule.instanceOf.name || rule.instanceOf}`;
    return `调用 ${fnName}() 的时候 '${keyName}' 原型必须是 ${name} , 收到的是: ${getValueType(value)}。`;
  },
  INVALID_PARAMETER_RANGE({ key, rule, fnName, value }) {
    return `调用 ${fnName}() 的时候 '${key || rule.name}' 必须是 ${rule.values.join('|')} 的其中一种, 收到的是: ${value}。`;
  }
};
function getDeviceTypeFromFnName(fnName) {
  if (!fnName)
    return 'camera';
  const lowercaseFnName = fnName.toLowerCase();
  if (lowercaseFnName.includes('screen'))
    return 'screen share';
  if (lowercaseFnName.includes('audio'))
    return 'microphone';
  return 'camera';
}
