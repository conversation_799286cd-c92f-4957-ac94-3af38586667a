import { BASIC_TYPE, ErrorName, Scene, UserRole } from 'trtc-js-sdk-core';
import { getValueType, isString, isUndefined } from 'trtc-js-sdk-core/src/utils/utils';
import RtcError from './rtc-error';
import { ErrorCode, ErrorCodeDictionary } from './error-code';
import { IS_SEI_SUPPORTED, isScreenCaptureApiAvailable, isUsedInHttpProtocol } from 'trtc-js-sdk-core/src/common/rtc-detection';
const LocalVideoConfigOption = {
    type: BASIC_TYPE.Object,
    properties: {
        cameraId: { type: BASIC_TYPE.String },
        useFrontCamera: { type: BASIC_TYPE.Boolean },
        fillMode: { type: BASIC_TYPE.String, values: ['contain', 'cover', 'fill'] },
        mirror: { type: BASIC_TYPE.Boolean },
        small: {
            properties: {
                width: { type: BASIC_TYPE.Number },
                height: { type: BASIC_TYPE.Number },
                frameRate: { type: BASIC_TYPE.Number },
                bitrate: { type: BASIC_TYPE.Number }
            }
        },
        videoTrack: { instanceOf: MediaStreamTrack }
    }
};
const ScreenShareConfigOption = {
    type: BASIC_TYPE.Object,
    properties: {
        systemAudio: { type: BASIC_TYPE.Boolean },
        fillMode: { type: BASIC_TYPE.String, values: ['contain', 'cover', 'fill'] },
        profile: {
            type: [BASIC_TYPE.String, BASIC_TYPE.Object],
            properties: {
                width: { type: BASIC_TYPE.Number },
                height: { type: BASIC_TYPE.Number },
                frameRate: { type: BASIC_TYPE.Number },
                bitrate: { type: BASIC_TYPE.Number }
            }
        },
        // Electron 屏幕分享需传入自定义采集 track
        videoTrack: { instanceOf: MediaStreamTrack },
        audioTrack: { instanceOf: MediaStreamTrack }
    }
};
const ViewValidateConfig = {
    type: [BASIC_TYPE.String, HTMLElement, null, BASIC_TYPE.Array],
    arrayItem: {
        instanceOf: HTMLElement
    },
    validate(view, key, fnName) {
        // 传入的 elementId 不在 DOM 对象中，报错
        if (isString(view)) {
            const container = document.getElementById(view);
            if (!container) {
                throw new RtcError({
                    code: ErrorCode.INVALID_PARAMETER,
                    extraCode: ErrorCodeDictionary.INVALID_ELEMENT_ID,
                    fnName,
                    messageParams: { key }
                });
            }
            if (!(container instanceof HTMLElement)) {
                throw new RtcError({
                    code: ErrorCode.INVALID_PARAMETER,
                    extraCode: ErrorCodeDictionary.INVALID_ELEMENT_ID_TYPE,
                    fnName,
                    messageParams: { key, type: getValueType(container) }
                });
            }
        }
    }
};
const UserIdValidateConfig = {
    name: 'userId',
    required: true,
    type: BASIC_TYPE.String
};
const LocalAudioConfigOption = {
    type: BASIC_TYPE.Object,
    properties: {
        microphoneId: { type: BASIC_TYPE.String },
        audioTrack: { instanceOf: MediaStreamTrack },
        captureVolume: { type: BASIC_TYPE.Number, min: 0 },
        earMonitorVolume: { type: BASIC_TYPE.Number, min: 0, max: 100 },
        echoCancellation: { type: BASIC_TYPE.Boolean },
        autoGainControl: { type: BASIC_TYPE.Boolean },
        noiseSuppression: { type: BASIC_TYPE.Boolean }
    }
};
function isJoinedValidate(isJoined, fnName) {
    if (!isJoined) {
        throw new RtcError({
            code: ErrorCode.INVALID_OPERATION,
            extraCode: ErrorCodeDictionary.INVALID_OPERATION_NOT_JOINED,
            fnName
        });
    }
}
function remoteUserIdValidate(hasRemoteUserId, fnName, config) {
    if (!hasRemoteUserId) {
        throw new RtcError({
            code: ErrorCode.INVALID_OPERATION,
            extraCode: ErrorCodeDictionary.INVALID_OPERATION_REMOTE_USER_NOT_EXIST,
            fnName,
            messageParams: { value: config }
        });
    }
}
function createValidateConfig(config) {
    return config;
}
const notLessThanZero = {
    type: BASIC_TYPE.Number,
    notLessThanZero: true
};
const TRTCValidateConfig = createValidateConfig({
    create: [
        {
            name: 'RoomConfig',
            instanceOf: Function
        },
        {
            name: 'CreateConfig',
            type: BASIC_TYPE.Object,
            properties: {
                plugins: {
                    type: BASIC_TYPE.Array,
                    arrayItem: {
                        instanceOf: Function
                    }
                }
            }
        }
    ],
    enterRoom: {
        name: 'EnterRoomConfig',
        type: BASIC_TYPE.Object,
        required: true,
        validate(value, key, fnName) {
            if (this._room.isJoined) {
                throw new RtcError({
                    code: ErrorCode.INVALID_OPERATION,
                    extraCode: ErrorCodeDictionary.INVALID_OPERATION_REPEAT_CALL,
                    fnName
                });
            }
            if (value.roomId) {
                if (isString(value.roomId)) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_PARAMETER,
                        extraCode: ErrorCodeDictionary.INVALID_ROOM_ID_INTEGER_STRING,
                        fnName,
                        messageParams: { key }
                    });
                }
                // 且小于等于0xffffffff
                const isValidateInteger = /^[1-9]\d*$/.test(String(value.roomId)) && value.roomId < 0xffffffff;
                if (!isValidateInteger) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_PARAMETER,
                        extraCode: ErrorCodeDictionary.INVALID_ROOM_ID_INTEGER,
                        fnName,
                        messageParams: { key }
                    });
                }
            }
            else if (value.strRoomId) {
                const isValidateString = /^[A-Za-z\d\s!#$%&()+\-:;<=.>?@[\]^_{}|~,]{1,64}$/.test(value.strRoomId);
                if (!isValidateString) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_PARAMETER,
                        extraCode: ErrorCodeDictionary.INVALID_ROOM_ID_STRING,
                        fnName,
                        messageParams: { key }
                    });
                }
            }
            else {
                throw new RtcError({
                    code: ErrorCode.INVALID_PARAMETER,
                    extraCode: ErrorCodeDictionary.INVALID_ROOM_ID_REQUIED,
                    fnName
                });
            }
        },
        properties: {
            sdkAppId: {
                required: true,
                type: BASIC_TYPE.Number,
                allowEmpty: false
            },
            userId: {
                required: true,
                type: BASIC_TYPE.String,
                allowEmpty: false
            },
            userSig: {
                required: true,
                type: BASIC_TYPE.String,
                allowEmpty: false
            },
            scene: {
                type: BASIC_TYPE.String,
                values: [Scene.LIVE, Scene.RTC]
            },
            role: {
                type: BASIC_TYPE.String,
                values: [UserRole.AUDIENCE, UserRole.ANCHOR]
            },
            roomId: {
                type: [BASIC_TYPE.String, BASIC_TYPE.Number]
            },
            strRoomId: {
                type: BASIC_TYPE.String
            },
            proxy: {
                type: [BASIC_TYPE.Object, BASIC_TYPE.String],
                properties: {
                    websocketProxy: { type: BASIC_TYPE.String },
                    turnServer: {
                        type: [BASIC_TYPE.Object, BASIC_TYPE.Array],
                        properties: {
                            url: { required: true, type: BASIC_TYPE.String },
                            username: { type: BASIC_TYPE.String },
                            credential: { type: BASIC_TYPE.String },
                            credentialType: { type: BASIC_TYPE.String, values: ['password'] }
                        }
                    },
                    loggerProxy: { type: BASIC_TYPE.String },
                    webtransportProxy: { type: BASIC_TYPE.String }
                }
            },
            // autoSubscribe: { type: BASIC_TYPE.Boolean },
            enableAutoPlayDialog: { type: BASIC_TYPE.Boolean },
            userDefineRecordId: { type: BASIC_TYPE.String }
            // pureAudioPushMode: {
            //   type: BASIC_TYPE.Number,
            //   values: [1, 2]
            // },
            // enableSEI: { type: BASIC_TYPE.Boolean }
        }
    },
    startLocalVideo: {
        name: 'LocalVideoConfig',
        type: BASIC_TYPE.Object,
        properties: {
            view: ViewValidateConfig,
            publish: {
                type: BASIC_TYPE.Boolean
            },
            option: LocalVideoConfigOption
        },
        validate(value) {
            if (!value?.option?.videoTrack && isUsedInHttpProtocol()) {
                throw new RtcError({
                    code: ErrorCode.ENV_NOT_SUPPORTED,
                    extraCode: ErrorCodeDictionary.NOT_SUPPORTED_HTTP
                });
            }
        }
    },
    updateLocalVideo: {
        name: 'updateLocalVideoConfig',
        type: BASIC_TYPE.Object,
        required: true,
        properties: {
            view: { ...ViewValidateConfig, required: false },
            publish: {
                type: BASIC_TYPE.Boolean
            },
            mute: {
                type: BASIC_TYPE.Boolean
            },
            option: LocalVideoConfigOption
        }
    },
    startLocalAudio: {
        name: 'LocalAudioConfig',
        type: BASIC_TYPE.Object,
        properties: {
            publish: {
                type: BASIC_TYPE.Boolean
            },
            option: LocalAudioConfigOption
        },
        validate(value) {
            if (!value?.option?.audioTrack && isUsedInHttpProtocol()) {
                throw new RtcError({
                    code: ErrorCode.ENV_NOT_SUPPORTED,
                    extraCode: ErrorCodeDictionary.NOT_SUPPORTED_HTTP
                });
            }
        }
    },
    updateLocalAudio: {
        name: 'updateLocalAudioConfig',
        type: BASIC_TYPE.Object,
        required: true,
        properties: {
            publish: {
                type: BASIC_TYPE.Boolean
            },
            mute: {
                type: BASIC_TYPE.Boolean
            },
            option: LocalAudioConfigOption
        }
    },
    startScreenShare: {
        name: 'ScreenShareConfig',
        type: BASIC_TYPE.Object,
        properties: {
            view: ViewValidateConfig,
            publish: {
                type: BASIC_TYPE.Boolean
            },
            option: ScreenShareConfigOption
        },
        validate(value, key, fnName, className, target) {
            if (!value?.option?.videoTrack && isUsedInHttpProtocol()) {
                throw new RtcError({
                    code: ErrorCode.ENV_NOT_SUPPORTED,
                    extraCode: ErrorCodeDictionary.NOT_SUPPORTED_HTTP
                });
            }
            if (!isScreenCaptureApiAvailable()) {
                throw new RtcError({
                    code: ErrorCode.ENV_NOT_SUPPORTED,
                    fnName,
                    extraCode: ErrorCodeDictionary.NOT_SUPPORTED_SCREEN_SHARE
                });
            }
        }
    },
    updateScreenShare: {
        name: 'updateScreenShareConfig',
        type: BASIC_TYPE.Object,
        required: true,
        properties: {
            view: ViewValidateConfig,
            publish: {
                type: BASIC_TYPE.Boolean
            },
            option: ScreenShareConfigOption
        }
    },
    muteRemoteAudio: [UserIdValidateConfig, {
            name: 'mute',
            required: true,
            type: BASIC_TYPE.Boolean
        }],
    setRemoteAudioVolume: [UserIdValidateConfig, {
            name: 'volume',
            required: true,
            type: BASIC_TYPE.Number,
            min: 0
        }],
    startRemoteVideo: {
        name: 'startRemoteVideoConfig',
        type: BASIC_TYPE.Object,
        required: true,
        properties: {
            view: ViewValidateConfig,
            userId: { type: BASIC_TYPE.String, required: true },
            streamType: { values: ["main" /* TRTCStreamType.Main */, "sub" /* TRTCStreamType.Sub */], required: true },
            option: {
                type: BASIC_TYPE.Object,
                properties: {
                    fillMode: { type: BASIC_TYPE.String, values: ['contain', 'cover', 'fill'] },
                    mirror: { type: BASIC_TYPE.Boolean }
                }
            }
        },
        validate(config, key, fnName) {
            isJoinedValidate(this._room.isJoined, fnName);
            const remotePublishedUser = this._room.remotePublishedUserMap.get(config.userId);
            remoteUserIdValidate(!!remotePublishedUser, fnName, config);
            if (remotePublishedUser) {
                if ((config.streamType === "main" /* TRTCStreamType.Main */ && !remotePublishedUser.muteState.videoAvailable) || (config.streamType === "sub" /* TRTCStreamType.Sub */ && !remotePublishedUser.muteState.hasAuxiliary)) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_OPERATION,
                        extraCode: ErrorCodeDictionary.INVALID_OPERATION_STREAM_TYPE_NOT_EXIST,
                        fnName,
                        messageParams: { value: config }
                    });
                }
            }
        }
    },
    updateRemoteVideo: {
        name: 'updateRemoteVideoConfig',
        type: BASIC_TYPE.Object,
        required: true,
        properties: {
            view: { ...ViewValidateConfig, required: false },
            userId: { type: BASIC_TYPE.String, required: true },
            streamType: { values: ["main" /* TRTCStreamType.Main */, "sub" /* TRTCStreamType.Sub */], required: true },
            option: {
                type: BASIC_TYPE.Object,
                properties: {
                    fillMode: { type: BASIC_TYPE.String, values: ['contain', 'cover', 'fill'] },
                    mirror: { type: BASIC_TYPE.Boolean }
                }
            }
        },
        validate(config, key, fnName) {
            isJoinedValidate(this._room.isJoined, fnName);
            const remotePublishedUser = this._room.remotePublishedUserMap.get(config.userId);
            remoteUserIdValidate(!!remotePublishedUser, fnName, config);
            if (remotePublishedUser) {
                if ((config.streamType === "main" /* TRTCStreamType.Main */ && !remotePublishedUser.muteState.videoAvailable) || (config.streamType === "sub" /* TRTCStreamType.Sub */ && !remotePublishedUser.muteState.hasAuxiliary)) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_OPERATION,
                        extraCode: ErrorCodeDictionary.INVALID_OPERATION_STREAM_TYPE_NOT_EXIST,
                        fnName,
                        messageParams: { value: config }
                    });
                }
            }
        }
    },
    stopRemoteVideo: {
        name: 'stopRemoteVideoConfig',
        type: BASIC_TYPE.Object,
        required: true,
        properties: {
            userId: { type: BASIC_TYPE.String, required: true },
            streamType: { values: ["main" /* TRTCStreamType.Main */, "sub" /* TRTCStreamType.Sub */] }
        },
        validate(config, key, fnName) {
            if (config.userId !== '*' && isUndefined(config.streamType)) {
                throw new RtcError({
                    code: ErrorCode.INVALID_PARAMETER,
                    extraCode: ErrorCodeDictionary.INVALID_STREAM_TYPE,
                    fnName
                });
            }
        }
    },
    switchRole: {
        name: 'role',
        required: true,
        values: [UserRole.ANCHOR, UserRole.AUDIENCE],
        validate(value, key, fnName) {
            isJoinedValidate(this._room.isJoined, fnName);
            // if (!this._room.isJoined) {
            //   throw new RtcError({
            //     code: ErrorCode.INVALID_OPERATION,
            //     message: generateErrorMessage({ key: ErrorName.INVALID_OPERATION_SWITCH_ROLE })
            //   });
            // }
            // if (this._room.mode !== Scene.LIVE) {
            //   // role is only valid in live mode.
            //   throw new RtcError({
            //     code: ErrorCode.INVALID_PARAMETER,
            //     message: generateErrorMessage({ key: ErrorName.INVALID_ROLE })
            //   });
            // }
        }
    },
    enableAudioVolumeEvaluation: [
        {
            name: 'interval',
            type: BASIC_TYPE.Number
        },
        {
            name: 'enableInBackground',
            type: BASIC_TYPE.Boolean
        }
    ],
    sendSEIMessage: [
        {
            name: 'buffer',
            required: true,
            instanceOf: ArrayBuffer,
            validate(buffer, key, fnName, className) {
                if (!IS_SEI_SUPPORTED) {
                    throw new RtcError({
                        code: ErrorCode.ENV_NOT_SUPPORTED,
                        fnName,
                        extraCode: ErrorCodeDictionary.NOT_SUPPORTED_SEI
                    });
                }
                if (!this._room.enableSEI) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_OPERATION,
                        messageParams: ({ key: ErrorName.SEI_DISABLED })
                    });
                }
                if (buffer.byteLength > 1000) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_PARAMETER,
                        messageParams: { key: ErrorName.SEI_OVERSIZE, data: buffer.byteLength }
                    });
                }
                if (buffer.byteLength === 0) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_PARAMETER,
                        messageParams: ({ key: ErrorName.SEI_EMPTY })
                    });
                }
                isJoinedValidate(this._room.isJoined, fnName);
                if (!this._room.isMainStreamPublished) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_PARAMETER,
                        messageParams: ({ key: ErrorName.SEI_BEFORE_PUBLISH })
                    });
                }
                // TODO:
                // if (!this.localStream_.hasVideo()) {
                //   throw new RtcError({
                //     code: ErrorCode.INVALID_OPERATION,
                //     messageParams: ({ key: ErrorName.SEI_NOT_VIDEO })
                //   });
                // }
                // 非 h264 编码器，不支持发送 SEI
                // const isH264 = this.uplinkConnection_.isH264;
                // if (!isH264) {
                //   throw new RtcError({
                //     code: ErrorCode.ENV_NOT_SUPPORTED,
                //     messageParams: ({
                //       key: ErrorName.SEI_NOT_SUPPORT,
                //       data: isH264
                //     })
                //   });
                // }
            }
        },
        {
            name: 'options',
            type: BASIC_TYPE.Object,
            properties: {
                seiPayloadType: {
                    type: BASIC_TYPE.Number,
                    values: [5, 243]
                }
            }
        }
    ]
});
export default { TRTC: TRTCValidateConfig };
