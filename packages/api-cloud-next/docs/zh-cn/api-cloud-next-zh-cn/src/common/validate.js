import { createDecorator } from 'trtc-js-sdk-core/src/utils/decorators/middleware';
import { isArray, isUndefined, isFunction, isString, getValueType, isConstructor, getConstructorName, isNumber, isPlainObject, isObject } from 'trtc-js-sdk-core/src/utils/utils';
import RtcError from './rtc-error';
import { ErrorCode, ErrorCodeDictionary } from './error-code';
/**
 * 装饰器：参数校验
 * 可传入多个 Rule，位置与 arguments 一一对应，用于校验 arguments
 * @param {...ValidateRule} configs
 * @export
 * @return {Function}
 * @example
 * @validate({
 *   name: 'clientConfig',
 *   required: true,
 *   type: BASIC_TYPE.Object,
 *   properties: {
 *     sdkAppId: {
 *       required: true,
 *       type: BASIC_TYPE.Number,
 *       allowEmpty: false
 *     },
 *     userId: {
 *       required: true,
 *       type: BASIC_TYPE.String,
 *       allowEmpty: false
 *     },
 *     ...
 *   }
 * })
 * createClient(clientConfig) {
 */
export function validate(...configs) {
    return createDecorator((next, fnName) => function (...args) {
        try {
            doValidate.call(this, configs, args, fnName, this._name);
        }
        catch (error) {
            // loggerManager.error(error);
            return Promise.reject(error);
        }
        return next.apply(this, args);
    });
}
export function validateSync(...configs) {
    return createDecorator((next, fnName) => function (...args) {
        try {
            doValidate.call(this, configs, args, fnName, this._name);
        }
        catch (error) {
            // loggerManager.error(error);
            throw error;
        }
        return next.apply(this, args);
    });
}
/**
 * 执行参数校验
 * @param  {...ValidateRule} configs 参数校验配置信息
 * @param {...any} args 调用接口传的参数值
 * @param {String} fnName 函数名
 */
export function doValidate(configs, args, fnName, className) {
    if (isArray(configs)) {
        // 根据 @validate 传入的配置，逐个校验客户调用接口传入的参数
        for (let i = 0; i < configs.length; i++) {
            // 1. 校验传入接口参数的合法性
            check.call(this, {
                rule: configs[i],
                value: args[i],
                key: configs[i].name,
                fnName,
                className
            });
        }
    }
    else {
        check.call(this, {
            rule: configs,
            value: args[0],
            key: configs.name,
            fnName,
            className
        });
    }
}
/**
 * 根据 rule 校验 value 的合法性，如果校验未通过，则 throw RtcError
 * @param {ValidateRule} rule 参数校验规则
 * @param {*} value 接入侧传入的参数值
 * @param {String} [key] 参数的 key
 * @param {String} fnName 调用的函数名
 */
function check({ rule, value, key, fnName, className }) {
    function genMsg(extraCode) {
        return {
            code: ErrorCode.INVALID_PARAMETER,
            extraCode,
            fnName,
            messageParams: {
                key, rule, value
            }
            // link: { className, fnName }
        };
    }
    // 用户没传指定参数
    if (isUndefined(value)) {
        // 检查必填参数，若配置是必填则报错
        if (rule.required) {
            throw new RtcError(genMsg(ErrorCodeDictionary.INVALID_PARAMETER_REQUIRED));
        }
        else {
            // 非必填参数，且没有默认值，跳过
            if (isUndefined(rule.defaultValue)) {
                // 自定义 validate function
                if (isFunction(rule.validate)) {
                    rule.validate.call(this, value, key, fnName, className, this);
                }
                return;
            }
            value = rule.defaultValue;
        }
    }
    // 检查参数类型 通过数组传入，例如：type: ['boolean', 'string', 'number']
    if (Array.isArray(rule.type)) {
        let isValidated = false;
        for (let i = 0; i < rule.type.length; i++) {
            if (rule.type[i] === null && value === null) {
                isValidated = true;
            }
            if (isFunction(rule.type[i]) && value instanceof rule.type[i]) {
                isValidated = true;
            }
            if (isString(rule.type[i]) && getValueType(value) === rule.type[i].toLowerCase()) {
                isValidated = true;
            }
        }
        if (!isValidated) {
            throw new RtcError({
                code: ErrorCode.INVALID_PARAMETER,
                extraCode: ErrorCodeDictionary.INVALID_PARAMETER_TYPE,
                fnName,
                messageParams: {
                    key,
                    // 将 type 数组中各种类型转成字符串
                    // ['string', null, HTMLElement] => ['string', 'null', 'HTMLElement']
                    rule: {
                        type: rule.type.map((item) => {
                            if (isConstructor(item))
                                return getConstructorName(item);
                            if (isString(item))
                                return item;
                            return getValueType(item);
                        })
                    },
                    value
                }
            });
        }
    }
    else if (!isUndefined(rule.type) && getValueType(value) !== rule.type) {
        // 直接传入类型，例如：type: 'string' 则直接判断类型是否相等即可
        throw new RtcError(genMsg(ErrorCodeDictionary.INVALID_PARAMETER_TYPE));
    }
    // 不允许传空值，例如 0, '', '  ', NaN
    if (rule.allowEmpty === false) {
        const isEmptyNumber = isNumber(value) && (value === 0 || Number.isNaN(value));
        const isEmptyString = isString(value) && value.trim() === '';
        if (isEmptyNumber || isEmptyString) {
            throw new RtcError(genMsg(ErrorCodeDictionary.INVALID_PARAMETER_EMPTY));
        }
    }
    // 要求值必须大于等于0
    if (rule.notLessThanZero && isNumber(value) && value < 0) {
        throw new RtcError(genMsg(ErrorCodeDictionary.INVALID_PARAMETER_LESS_THAN_ZERO));
    }
    // min value
    if (!isUndefined(rule.min) && isNumber(value) && value < rule.min) {
        throw new RtcError(genMsg(ErrorCodeDictionary.INVALID_PARAMETER_MIN));
    }
    // max value
    if (!isUndefined(rule.max) && isNumber(value) && value > rule.max) {
        throw new RtcError(genMsg(ErrorCodeDictionary.INVALID_PARAMETER_MAX));
    }
    // 检查 instanceof
    if (isString(rule.instanceOf)) {
        // 检查是否是 sdk 内部类的实例
        if (!value || value._name !== rule.instanceOf) {
            throw new RtcError(genMsg(ErrorCodeDictionary.INVALID_PARAMETER_INSTANCE));
        }
    }
    else if (isFunction(rule.instanceOf) && !(value instanceof rule.instanceOf)) {
        // 检查是否是全局类的实例，例如 MediaStreamTrack
        throw new RtcError(genMsg(ErrorCodeDictionary.INVALID_PARAMETER_INSTANCE));
    }
    // 检查参数值是否在指定范围内
    if (Array.isArray(rule.values) && !rule.values.includes(value)) {
        throw new RtcError(genMsg(ErrorCodeDictionary.INVALID_PARAMETER_RANGE));
    }
    // 若参数是 Object，则此处校验 Object 所有属性(properties)的合法性
    const { properties } = rule;
    if (isPlainObject(properties) && isObject(value)) {
        Object.keys(properties).forEach(innerKey => {
            check.call(this, {
                rule: properties[innerKey],
                value: value && value[innerKey],
                key: `${innerKey}`,
                fnName,
                className
            });
        });
    }
    // 若参数是 Array，则此处校验 Array 中所有元素的属性的合法性
    const { arrayItem } = rule;
    if (isPlainObject(arrayItem) && isArray(value)) {
        value.forEach((item, index) => {
            check.call(this, {
                rule: arrayItem,
                value: item,
                key: `${key}[${index}]`,
                fnName,
                className
            });
        });
    }
    // 自定义 validate function
    if (isFunction(rule.validate)) {
        rule.validate.call(this, value, key, fnName, className, this);
    }
}
/**
 * 若传入非箭头函数，则函数内的 this 指向对应的实例。
 * @callback ValidateFunction
 * @param {*} paramValue 用户传入的参数值
 * @param {Object} instace 装饰器所在类的实例
 */
/**
 * 参数校验规则
 * @typedef {Object} Rule
 * @property {Boolean} [required=false] 参数是否必填
 * @property {BASIC_TYPE|BASIC_TYPE[]} [type=] 参数类型
 * @property {Array} [values=] 参数取值
 * @property {Function} [instanceOf=] 参数是某个类的实例
 * @property {Boolean} [allowEmpty=true] 是否允许传空值:  0, NaN, '', '  '
 * @property {Boolean} [notLessThanZero=] 数值是否可以小于0
 * @property {ValidateFunction} [validate=] 自定义参数校验。回调函数的第一个参数是待校验的参数值、第二个参数是类的实例
 * @property {Object.<string, ValidateRule>} [properties=] 非必填，Object 类型的参数的属性校验规则
 * @property {Object.<string, ValidateRule>} [arrayItem=] 非必填，Array 类型的参数的 item 的属性校验规则
 */
