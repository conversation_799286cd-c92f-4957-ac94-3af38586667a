{"name": "@rtc-plugin/cdn-streaming", "version": "5.10.1", "description": "TRTC Web SDK 5.x CDN streaming plugin", "main": "./cdn-streaming.esm.js", "module": "./cdn-streaming.esm.js", "repository": {"type": "git", "url": "**************:LiteAVSDK/TRTC_Web.git"}, "homepage": "https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-26-advanced-publish-cdn-stream.html", "keywords": ["webrtc", "TRTC", "rtc", "call", "video call", "audio call", "javascript", "video", "audio", "camera", "microphone", "live streaming", "real-time communication", "cdn", "mix transcode"], "types": "./cdn-streaming.esm.d.ts"}