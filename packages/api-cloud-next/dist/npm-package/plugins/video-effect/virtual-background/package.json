{"name": "@rtc-plugin/virtual-background", "version": "5.10.1", "description": "TRTC Web SDK 5.x virtual background plugin", "main": "./virtual-background.esm.js", "module": "./virtual-background.esm.js", "repository": {"type": "git", "url": "**************:LiteAVSDK/TRTC_Web.git"}, "homepage": "https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-36-advanced-virtual-background.html", "keywords": ["webrtc", "TRTC", "rtc", "call", "video call", "audio call", "javascript", "video", "audio", "camera", "microphone", "live streaming", "real-time communication", "blur background"], "types": "./virtual-background.esm.d.ts"}