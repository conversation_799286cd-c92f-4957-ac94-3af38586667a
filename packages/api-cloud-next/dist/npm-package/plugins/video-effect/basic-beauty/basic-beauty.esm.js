var __defProp=Object.defineProperty,__defNormalProp=(A,g,I)=>g in A?__defProp(A,g,{enumerable:!0,configurable:!0,writable:!0,value:I}):A[g]=I,__publicField=(A,g,I)=>__defNormalProp(A,"symbol"!=typeof g?g+"":g,I);function startValidateRule(A){return{name:"BasicBeautyOptions",type:"object",required:!0,allowEmpty:!1,properties:{beauty:{required:!1,type:"number"},brightness:{required:!1,type:"number"},ruddy:{required:!1,type:"number"}},validate(g,I,B,C){const{RtcError:Q,ErrorCode:E,ErrorCodeDictionary:i}=A.errorModule;if(A.utils.isOverseaSdkAppId(g.sdkAppId))throw new Q({code:E.INVALID_OPERATION,extraCode:i.INVALID_OPERATION,message:"This feature is not yet available in your country or region. If you have any questions, you can go to the community for consultation: https://zhiliao.qq.com/s/cWSPGIIM62CC/c3TPGIIM62CQ"})}}}function stopValidateRule(A){return{name:"StopBasicBeautyOptions",required:!1}}var Module=(()=>{var A,g="undefined"!=typeof document?null==(A=document.currentScript)?void 0:A.src:void 0;return function(A={}){var I,B,C,Q=Object.assign({},A),E=new Promise(((A,g)=>{I=A,B=g})),i=Object.assign({},Q),t="";"undefined"!=typeof document&&document.currentScript&&(t=document.currentScript.src),g&&(t=g),t=t.startsWith("blob:")?"":t.substr(0,t.replace(/[?#].*/,"").lastIndexOf("/")+1);var e,r,o=Q.print||console.log.bind(console),s=Q.printErr||console.error.bind(console);function a(A){if(p(A))return function(A){for(var g=atob(A),I=new Uint8Array(g.length),B=0;B<g.length;++B)I[B]=g.charCodeAt(B);return I}(A.slice(L.length))}Object.assign(Q,i),i=null,Q.arguments&&Q.arguments,Q.thisProgram&&Q.thisProgram,Q.quit&&Q.quit,Q.wasmBinary&&(e=Q.wasmBinary);var n,D,y,c,G,w,h,N,d=!1;var l=[],F=[],u=[];var Y=0,k=null,M=null;function R(A){var g;null==(g=Q.onAbort)||g.call(Q,A),s(A="Aborted("+A+")"),d=!0,A+=". Build with -sASSERTIONS for more info.";var I=new WebAssembly.RuntimeError(A);throw B(I),I}var S,L="data:application/octet-stream;base64,",p=A=>A.startsWith(L);function U(A){return Promise.resolve().then((()=>function(A){if(A==S&&e)return new Uint8Array(e);var g=a(A);if(g)return g;if(C)return C(A);throw"both async and sync fetching of the wasm failed"}(A)))}function f(A,g,I,B){return function(A,g,I){return U(A).then((A=>WebAssembly.instantiate(A,g))).then(I,(A=>{s(`failed to asynchronously prepare wasm: ${A}`),R(A)}))}(g,I,B)}var J=A=>{for(;A.length>0;)A.shift()(Q)};Q.noExitRuntime;class b{constructor(A){this.excPtr=A,this.ptr=A-24}set_type(A){w[this.ptr+4>>2]=A}get_type(){return w[this.ptr+4>>2]}set_destructor(A){w[this.ptr+8>>2]=A}get_destructor(){return w[this.ptr+8>>2]}set_caught(A){A=A?1:0,n[this.ptr+12]=A}get_caught(){return 0!=n[this.ptr+12]}set_rethrown(A){A=A?1:0,n[this.ptr+13]=A}get_rethrown(){return 0!=n[this.ptr+13]}init(A,g){this.set_adjusted_ptr(0),this.set_type(A),this.set_destructor(g)}set_adjusted_ptr(A){w[this.ptr+16>>2]=A}get_adjusted_ptr(){return w[this.ptr+16>>2]}get_exception_ptr(){if(sg(this.get_type()))return w[this.excPtr>>2];var A=this.get_adjusted_ptr();return 0!==A?A:this.excPtr}}var H,K,m,v=A=>{for(var g="",I=A;D[I];)g+=H[D[I++]];return g},Z={},T={},j={},x=A=>{throw new K(A)},P=A=>{throw new m(A)},W=(A,g,I)=>{function B(g){var B=I(g);B.length!==A.length&&P("Mismatched type converter count");for(var C=0;C<A.length;++C)z(A[C],B[C])}A.forEach((function(A){j[A]=g}));var C=new Array(g.length),Q=[],E=0;g.forEach(((A,g)=>{T.hasOwnProperty(A)?C[g]=T[A]:(Q.push(A),Z.hasOwnProperty(A)||(Z[A]=[]),Z[A].push((()=>{C[g]=T[A],++E===Q.length&&B(C)})))})),0===Q.length&&B(C)};function z(A,g,I={}){if(!("argPackAdvance"in g))throw new TypeError("registerType registeredInstance requires argPackAdvance");return function(A,g,I={}){var B=g.name;if(A||x(`type "${B}" must have a positive integer typeid pointer`),T.hasOwnProperty(A)){if(I.ignoreDuplicateRegistrations)return;x(`Cannot register type '${B}' twice`)}if(T[A]=g,delete j[A],Z.hasOwnProperty(A)){var C=Z[A];delete Z[A],C.forEach((A=>A()))}}(A,g,I)}var q,V=A=>{x(A.$$.ptrType.registeredClass.name+" instance already deleted")},X=!1,O=A=>{},$=A=>{A.count.value-=1,0===A.count.value&&(A=>{A.smartPtr?A.smartPtrType.rawDestructor(A.smartPtr):A.ptrType.registeredClass.rawDestructor(A.ptr)})(A)},_=(A,g,I)=>{if(g===I)return A;if(void 0===I.baseClass)return null;var B=_(A,g,I.baseClass);return null===B?null:I.downcast(B)},AA={},gA=()=>Object.keys(EA).length,IA=()=>{var A=[];for(var g in EA)EA.hasOwnProperty(g)&&A.push(EA[g]);return A},BA=[],CA=()=>{for(;BA.length;){var A=BA.pop();A.$$.deleteScheduled=!1,A.delete()}},QA=A=>{q=A,BA.length&&q&&q(CA)},EA={},iA=(A,g)=>(g=((A,g)=>{for(void 0===g&&x("ptr should not be undefined");A.baseClass;)g=A.upcast(g),A=A.baseClass;return g})(A,g),EA[g]),tA=(A,g)=>(g.ptrType&&g.ptr||P("makeClassHandle requires ptr and ptrType"),!!g.smartPtrType!==!!g.smartPtr&&P("Both smartPtrType and smartPtr must be specified"),g.count={value:1},rA(Object.create(A,{$$:{value:g,writable:!0}})));function eA(A){var g=this.getPointee(A);if(!g)return this.destructor(A),null;var I=iA(this.registeredClass,g);if(void 0!==I){if(0===I.$$.count.value)return I.$$.ptr=g,I.$$.smartPtr=A,I.clone();var B=I.clone();return this.destructor(A),B}function C(){return this.isSmartPointer?tA(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:g,smartPtrType:this,smartPtr:A}):tA(this.registeredClass.instancePrototype,{ptrType:this,ptr:A})}var Q,E=this.registeredClass.getActualType(g),i=AA[E];if(!i)return C.call(this);Q=this.isConst?i.constPointerType:i.pointerType;var t=_(g,this.registeredClass,Q.registeredClass);return null===t?C.call(this):this.isSmartPointer?tA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:t,smartPtrType:this,smartPtr:A}):tA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:t})}var rA=A=>"undefined"==typeof FinalizationRegistry?(rA=A=>A,A):(X=new FinalizationRegistry((A=>{$(A.$$)})),O=A=>X.unregister(A),(rA=A=>{var g=A.$$;if(!!g.smartPtr){var I={$$:g};X.register(A,I,A)}return A})(A));function oA(){}var sA=(A,g)=>Object.defineProperty(g,"name",{value:A}),aA=(A,g,I)=>{if(void 0===A[g].overloadTable){var B=A[g];A[g]=function(...B){return A[g].overloadTable.hasOwnProperty(B.length)||x(`Function '${I}' called with an invalid number of arguments (${B.length}) - expects one of (${A[g].overloadTable})!`),A[g].overloadTable[B.length].apply(this,B)},A[g].overloadTable=[],A[g].overloadTable[B.argCount]=B}};function nA(A,g,I,B,C,Q,E,i){this.name=A,this.constructor=g,this.instancePrototype=I,this.rawDestructor=B,this.baseClass=C,this.getActualType=Q,this.upcast=E,this.downcast=i,this.pureVirtualFunctions=[]}var DA=(A,g,I)=>{for(;g!==I;)g.upcast||x(`Expected null or instance of ${I.name}, got an instance of ${g.name}`),A=g.upcast(A),g=g.baseClass;return A};function yA(A,g){if(null===g)return this.isReference&&x(`null is not a valid ${this.name}`),0;g.$$||x(`Cannot pass "${TA(g)}" as a ${this.name}`),g.$$.ptr||x(`Cannot pass deleted object as a pointer of type ${this.name}`);var I=g.$$.ptrType.registeredClass;return DA(g.$$.ptr,I,this.registeredClass)}function cA(A,g){var I;if(null===g)return this.isReference&&x(`null is not a valid ${this.name}`),this.isSmartPointer?(I=this.rawConstructor(),null!==A&&A.push(this.rawDestructor,I),I):0;g&&g.$$||x(`Cannot pass "${TA(g)}" as a ${this.name}`),g.$$.ptr||x(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&g.$$.ptrType.isConst&&x(`Cannot convert argument of type ${g.$$.smartPtrType?g.$$.smartPtrType.name:g.$$.ptrType.name} to parameter type ${this.name}`);var B=g.$$.ptrType.registeredClass;if(I=DA(g.$$.ptr,B,this.registeredClass),this.isSmartPointer)switch(void 0===g.$$.smartPtr&&x("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:g.$$.smartPtrType===this?I=g.$$.smartPtr:x(`Cannot convert argument of type ${g.$$.smartPtrType?g.$$.smartPtrType.name:g.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:I=g.$$.smartPtr;break;case 2:if(g.$$.smartPtrType===this)I=g.$$.smartPtr;else{var C=g.clone();I=this.rawShare(I,vA.toHandle((()=>C.delete()))),null!==A&&A.push(this.rawDestructor,I)}break;default:x("Unsupporting sharing policy")}return I}function GA(A,g){if(null===g)return this.isReference&&x(`null is not a valid ${this.name}`),0;g.$$||x(`Cannot pass "${TA(g)}" as a ${this.name}`),g.$$.ptr||x(`Cannot pass deleted object as a pointer of type ${this.name}`),g.$$.ptrType.isConst&&x(`Cannot convert argument of type ${g.$$.ptrType.name} to parameter type ${this.name}`);var I=g.$$.ptrType.registeredClass;return DA(g.$$.ptr,I,this.registeredClass)}function wA(A){return this.fromWireType(w[A>>2])}function hA(A,g,I,B,C,Q,E,i,t,e,r){this.name=A,this.registeredClass=g,this.isReference=I,this.isConst=B,this.isSmartPointer=C,this.pointeeType=Q,this.sharingPolicy=E,this.rawGetPointee=i,this.rawConstructor=t,this.rawShare=e,this.rawDestructor=r,C||void 0!==g.baseClass?this.toWireType=cA:B?(this.toWireType=yA,this.destructorFunction=null):(this.toWireType=GA,this.destructorFunction=null)}var NA,dA,lA=[],FA=A=>{var g=lA[A];return g||(A>=lA.length&&(lA.length=A+1),lA[A]=g=NA.get(A)),g},uA=(A,g,I=[])=>A.includes("j")?((A,g,I)=>(A=A.replace(/p/g,"i"),(0,Q["dynCall_"+A])(g,...I)))(A,g,I):FA(g)(...I),YA=(A,g)=>{var I,B,C=(A=v(A)).includes("j")?(I=A,B=g,(...A)=>uA(I,B,A)):FA(g);return"function"!=typeof C&&x(`unknown function pointer with signature ${A}: ${g}`),C},kA=A=>{var g=rg(A),I=v(g);return og(g),I},MA=(A,g)=>{var I=[],B={};throw g.forEach((function A(g){B[g]||T[g]||(j[g]?j[g].forEach(A):(I.push(g),B[g]=!0))})),new dA(`${A}: `+I.map(kA).join([", "]))},RA=(A,g)=>{for(var I=[],B=0;B<A;B++)I.push(w[g+4*B>>2]);return I},SA=A=>{for(;A.length;){var g=A.pop();A.pop()(g)}};function LA(A,g,I,B,C,Q){var E=g.length;E<2&&x("argTypes array size mismatch! Must at least get return value and 'this' types!");var i=null!==g[1]&&null!==I,t=function(A){for(var g=1;g<A.length;++g)if(null!==A[g]&&void 0===A[g].destructorFunction)return!0;return!1}(g),e="void"!==g[0].name,r=E-2,o=new Array(r),s=[],a=[];return sA(A,(function(...I){var Q;I.length!==r&&x(`function ${A} called with ${I.length} arguments, expected ${r}`),a.length=0,s.length=i?2:1,s[0]=C,i&&(Q=g[1].toWireType(a,this),s[1]=Q);for(var E=0;E<r;++E)o[E]=g[E+2].toWireType(a,I[E]),s.push(o[E]);return function(A){if(t)SA(a);else for(var I=i?1:2;I<g.length;I++){var B=1===I?Q:o[I-2];null!==g[I].destructorFunction&&g[I].destructorFunction(B)}if(e)return g[0].fromWireType(A)}(B(...s))}))}var pA,UA,fA,JA=(A,g,I)=>(A instanceof Object||x(`${I} with invalid "this": ${A}`),A instanceof g.registeredClass.constructor||x(`${I} incompatible with "this" of type ${A.constructor.name}`),A.$$.ptr||x(`cannot call emscripten binding method ${I} on deleted object`),DA(A.$$.ptr,A.$$.ptrType.registeredClass,g.registeredClass)),bA=[],HA=[],KA=A=>{A>9&&0==--HA[A+1]&&(HA[A]=void 0,bA.push(A))},mA=()=>HA.length/2-5-bA.length,vA={toValue:A=>(A||x("Cannot use deleted val. handle = "+A),HA[A]),toHandle:A=>{switch(A){case void 0:return 2;case null:return 4;case!0:return 6;case!1:return 8;default:{const g=bA.pop()||HA.length;return HA[g]=A,HA[g+1]=1,g}}}},ZA={name:"emscripten::val",fromWireType:A=>{var g=vA.toValue(A);return KA(A),g},toWireType:(A,g)=>vA.toHandle(g),argPackAdvance:8,readValueFromPointer:wA,destructorFunction:null},TA=A=>{if(null===A)return"null";var g=typeof A;return"object"===g||"array"===g||"function"===g?A.toString():""+A},jA=(A,g)=>{switch(g){case 4:return function(A){return this.fromWireType(h[A>>2])};case 8:return function(A){return this.fromWireType(N[A>>3])};default:throw new TypeError(`invalid float width (${g}): ${A}`)}},xA=(A,g,I)=>{switch(g){case 1:return I?A=>n[A]:A=>D[A];case 2:return I?A=>y[A>>1]:A=>c[A>>1];case 4:return I?A=>G[A>>2]:A=>w[A>>2];default:throw new TypeError(`invalid integer width (${g}): ${A}`)}},PA="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,WA=(A,g,I)=>{for(var B=g+I,C=g;A[C]&&!(C>=B);)++C;if(C-g>16&&A.buffer&&PA)return PA.decode(A.subarray(g,C));for(var Q="";g<C;){var E=A[g++];if(128&E){var i=63&A[g++];if(192!=(224&E)){var t=63&A[g++];if((E=224==(240&E)?(15&E)<<12|i<<6|t:(7&E)<<18|i<<12|t<<6|63&A[g++])<65536)Q+=String.fromCharCode(E);else{var e=E-65536;Q+=String.fromCharCode(55296|e>>10,56320|1023&e)}}else Q+=String.fromCharCode((31&E)<<6|i)}else Q+=String.fromCharCode(E)}return Q},zA="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,qA=(A,g)=>{for(var I=A,B=I>>1,C=B+g/2;!(B>=C)&&c[B];)++B;if((I=B<<1)-A>32&&zA)return zA.decode(D.subarray(A,I));for(var Q="",E=0;!(E>=g/2);++E){var i=y[A+2*E>>1];if(0==i)break;Q+=String.fromCharCode(i)}return Q},VA=(A,g,I)=>{if(null!=I||(I=2147483647),I<2)return 0;for(var B=g,C=(I-=2)<2*A.length?I/2:A.length,Q=0;Q<C;++Q){var E=A.charCodeAt(Q);y[g>>1]=E,g+=2}return y[g>>1]=0,g-B},XA=A=>2*A.length,OA=(A,g)=>{for(var I=0,B="";!(I>=g/4);){var C=G[A+4*I>>2];if(0==C)break;if(++I,C>=65536){var Q=C-65536;B+=String.fromCharCode(55296|Q>>10,56320|1023&Q)}else B+=String.fromCharCode(C)}return B},$A=(A,g,I)=>{if(null!=I||(I=2147483647),I<4)return 0;for(var B=g,C=B+I-4,Q=0;Q<A.length;++Q){var E=A.charCodeAt(Q);if(E>=55296&&E<=57343)E=65536+((1023&E)<<10)|1023&A.charCodeAt(++Q);if(G[g>>2]=E,(g+=4)+4>C)break}return G[g>>2]=0,g-B},_A=A=>{for(var g=0,I=0;I<A.length;++I){var B=A.charCodeAt(I);B>=55296&&B<=57343&&++I,g+=4}return g},Ag=(A,g)=>{var I=T[A];return void 0===I&&x(`${g} has unknown type ${kA(A)}`),I},gg=(A,g,I)=>{var B=[],C=A.toWireType(B,I);return B.length&&(w[g>>2]=vA.toHandle(B)),C},Ig={},Bg=[],Cg=Reflect.construct,Qg=[null,[],[]];(()=>{for(var A=new Array(256),g=0;g<256;++g)A[g]=String.fromCharCode(g);H=A})(),K=Q.BindingError=class extends Error{constructor(A){super(A),this.name="BindingError"}},m=Q.InternalError=class extends Error{constructor(A){super(A),this.name="InternalError"}},Object.assign(oA.prototype,{isAliasOf(A){if(!(this instanceof oA))return!1;if(!(A instanceof oA))return!1;var g=this.$$.ptrType.registeredClass,I=this.$$.ptr;A.$$=A.$$;for(var B=A.$$.ptrType.registeredClass,C=A.$$.ptr;g.baseClass;)I=g.upcast(I),g=g.baseClass;for(;B.baseClass;)C=B.upcast(C),B=B.baseClass;return g===B&&I===C},clone(){if(this.$$.ptr||V(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var A,g=rA(Object.create(Object.getPrototypeOf(this),{$$:{value:(A=this.$$,{count:A.count,deleteScheduled:A.deleteScheduled,preservePointerOnDelete:A.preservePointerOnDelete,ptr:A.ptr,ptrType:A.ptrType,smartPtr:A.smartPtr,smartPtrType:A.smartPtrType})}}));return g.$$.count.value+=1,g.$$.deleteScheduled=!1,g},delete(){this.$$.ptr||V(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&x("Object already scheduled for deletion"),O(this),$(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted(){return!this.$$.ptr},deleteLater(){return this.$$.ptr||V(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&x("Object already scheduled for deletion"),BA.push(this),1===BA.length&&q&&q(CA),this.$$.deleteScheduled=!0,this}}),Q.getInheritedInstanceCount=gA,Q.getLiveInheritedInstances=IA,Q.flushPendingDeletes=CA,Q.setDelayFunction=QA,Object.assign(hA.prototype,{getPointee(A){return this.rawGetPointee&&(A=this.rawGetPointee(A)),A},destructor(A){var g;null==(g=this.rawDestructor)||g.call(this,A)},argPackAdvance:8,readValueFromPointer:wA,fromWireType:eA}),dA=Q.UnboundTypeError=(pA=Error,(fA=sA(UA="UnboundTypeError",(function(A){this.name=UA,this.message=A;var g=new Error(A).stack;void 0!==g&&(this.stack=this.toString()+"\n"+g.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(pA.prototype),fA.prototype.constructor=fA,fA.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},fA),HA.push(0,1,void 0,1,null,1,!0,1,!1,1),Q.count_emval_handles=mA;var Eg,ig={w:(A,g,I)=>{throw new b(A).init(g,I),A},q:()=>{R("")},p:(A,g,I,B,C)=>{},u:(A,g,I,B)=>{z(A,{name:g=v(g),fromWireType:function(A){return!!A},toWireType:function(A,g){return g?I:B},argPackAdvance:8,readValueFromPointer:function(A){return this.fromWireType(D[A])},destructorFunction:null})},y:(A,g,I,B,C,E,i,t,e,r,o,s,a)=>{o=v(o),E=YA(C,E),t&&(t=YA(i,t)),r&&(r=YA(e,r)),a=YA(s,a);var n=(A=>{if(void 0===A)return"_unknown";var g=(A=A.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return g>=48&&g<=57?`_${A}`:A})(o);((A,g,I)=>{Q.hasOwnProperty(A)?((void 0===I||void 0!==Q[A].overloadTable&&void 0!==Q[A].overloadTable[I])&&x(`Cannot register public name '${A}' twice`),aA(Q,A,A),Q.hasOwnProperty(I)&&x(`Cannot register multiple overloads of a function with the same number of arguments (${I})!`),Q[A].overloadTable[I]=g):(Q[A]=g,void 0!==I&&(Q[A].numArguments=I))})(n,(function(){MA(`Cannot construct ${o} due to unbound types`,[B])})),W([A,g,I],B?[B]:[],(g=>{var I,C,i;g=g[0],i=B?(C=g.registeredClass).instancePrototype:oA.prototype;var e=sA(o,(function(...A){if(Object.getPrototypeOf(this)!==s)throw new K("Use 'new' to construct "+o);if(void 0===D.constructor_body)throw new K(o+" has no accessible constructor");var g=D.constructor_body[A.length];if(void 0===g)throw new K(`Tried to invoke ctor of ${o} with invalid number of parameters (${A.length}) - expected (${Object.keys(D.constructor_body).toString()}) parameters instead!`);return g.apply(this,A)})),s=Object.create(i,{constructor:{value:e}});e.prototype=s;var D=new nA(o,e,s,a,C,E,t,r);D.baseClass&&(null!=(I=D.baseClass).__derivedClasses||(I.__derivedClasses=[]),D.baseClass.__derivedClasses.push(D));var y=new hA(o,D,!0,!1,!1),c=new hA(o+"*",D,!1,!1,!1),G=new hA(o+" const*",D,!1,!0,!1);return AA[A]={pointerType:c,constPointerType:G},((A,g,I)=>{Q.hasOwnProperty(A)||P("Replacing nonexistent public symbol"),void 0!==Q[A].overloadTable&&void 0!==I?Q[A].overloadTable[I]=g:(Q[A]=g,Q[A].argCount=I)})(n,e),[y,c,G]}))},x:(A,g,I,B,C,Q)=>{var E=RA(g,I);C=YA(B,C),W([],[A],(A=>{var I=`constructor ${(A=A[0]).name}`;if(void 0===A.registeredClass.constructor_body&&(A.registeredClass.constructor_body=[]),void 0!==A.registeredClass.constructor_body[g-1])throw new K(`Cannot register multiple constructors with identical number of parameters (${g-1}) for class '${A.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return A.registeredClass.constructor_body[g-1]=()=>{MA(`Cannot construct ${A.name} due to unbound types`,E)},W([],E,(B=>(B.splice(1,0,null),A.registeredClass.constructor_body[g-1]=LA(I,B,null,C,Q),[]))),[]}))},h:(A,g,I,B,C,Q,E,i,t)=>{var e=RA(I,B);g=(A=>{const g=(A=A.trim()).indexOf("(");return-1!==g?A.substr(0,g):A})(g=v(g)),Q=YA(C,Q),W([],[A],(A=>{var B=`${(A=A[0]).name}.${g}`;function C(){MA(`Cannot call ${B} due to unbound types`,e)}g.startsWith("@@")&&(g=Symbol[g.substring(2)]),i&&A.registeredClass.pureVirtualFunctions.push(g);var t=A.registeredClass.instancePrototype,r=t[g];return void 0===r||void 0===r.overloadTable&&r.className!==A.name&&r.argCount===I-2?(C.argCount=I-2,C.className=A.name,t[g]=C):(aA(t,g,B),t[g].overloadTable[I-2]=C),W([],e,(C=>{var i=LA(B,C,A,Q,E);return void 0===t[g].overloadTable?(i.argCount=I-2,t[g]=i):t[g].overloadTable[I-2]=i,[]})),[]}))},k:(A,g,I,B,C,Q,E,i,t,e)=>{g=v(g),C=YA(B,C),W([],[A],(A=>{var B=`${(A=A[0]).name}.${g}`,r={get(){MA(`Cannot access ${B} due to unbound types`,[I,E])},enumerable:!0,configurable:!0};return r.set=t?()=>MA(`Cannot access ${B} due to unbound types`,[I,E]):A=>x(B+" is a read-only property"),Object.defineProperty(A.registeredClass.instancePrototype,g,r),W([],t?[I,E]:[I],(I=>{var E=I[0],r={get(){var g=JA(this,A,B+" getter");return E.fromWireType(C(Q,g))},enumerable:!0};if(t){t=YA(i,t);var o=I[1];r.set=function(g){var I=JA(this,A,B+" setter"),C=[];t(e,I,o.toWireType(C,g)),SA(C)}}return Object.defineProperty(A.registeredClass.instancePrototype,g,r),[]})),[]}))},t:A=>z(A,ZA),o:(A,g,I)=>{z(A,{name:g=v(g),fromWireType:A=>A,toWireType:(A,g)=>g,argPackAdvance:8,readValueFromPointer:jA(g,I),destructorFunction:null})},g:(A,g,I,B,C)=>{g=v(g),-1===C&&(C=4294967295);var Q=A=>A;if(0===B){var E=32-8*I;Q=A=>A<<E>>>E}var i=g.includes("unsigned");z(A,{name:g,fromWireType:Q,toWireType:i?function(A,g){return this.name,g>>>0}:function(A,g){return this.name,g},argPackAdvance:8,readValueFromPointer:xA(g,I,0!==B),destructorFunction:null})},a:(A,g,I)=>{var B=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][g];function C(A){var g=w[A>>2],I=w[A+4>>2];return new B(n.buffer,I,g)}z(A,{name:I=v(I),fromWireType:C,argPackAdvance:8,readValueFromPointer:C},{ignoreDuplicateRegistrations:!0})},n:(A,g)=>{var I="std::string"===(g=v(g));z(A,{name:g,fromWireType(A){var g,B,C,Q=w[A>>2],E=A+4;if(I)for(var i=E,t=0;t<=Q;++t){var e=E+t;if(t==Q||0==D[e]){var r=(C=e-i,(B=i)?WA(D,B,C):"");void 0===g?g=r:(g+=String.fromCharCode(0),g+=r),i=e+1}}else{var o=new Array(Q);for(t=0;t<Q;++t)o[t]=String.fromCharCode(D[E+t]);g=o.join("")}return og(A),g},toWireType(A,g){var B;g instanceof ArrayBuffer&&(g=new Uint8Array(g));var C="string"==typeof g;C||g instanceof Uint8Array||g instanceof Uint8ClampedArray||g instanceof Int8Array||x("Cannot pass non-string to std::string"),B=I&&C?(A=>{for(var g=0,I=0;I<A.length;++I){var B=A.charCodeAt(I);B<=127?g++:B<=2047?g+=2:B>=55296&&B<=57343?(g+=4,++I):g+=3}return g})(g):g.length;var Q=eg(4+B+1),E=Q+4;if(w[Q>>2]=B,I&&C)((A,g,I,B)=>{if(!(B>0))return 0;for(var C=I,Q=I+B-1,E=0;E<A.length;++E){var i=A.charCodeAt(E);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&A.charCodeAt(++E)),i<=127){if(I>=Q)break;g[I++]=i}else if(i<=2047){if(I+1>=Q)break;g[I++]=192|i>>6,g[I++]=128|63&i}else if(i<=65535){if(I+2>=Q)break;g[I++]=224|i>>12,g[I++]=128|i>>6&63,g[I++]=128|63&i}else{if(I+3>=Q)break;g[I++]=240|i>>18,g[I++]=128|i>>12&63,g[I++]=128|i>>6&63,g[I++]=128|63&i}}g[I]=0})(g,D,E,B+1);else if(C)for(var i=0;i<B;++i){var t=g.charCodeAt(i);t>255&&(og(E),x("String has UTF-16 code units that do not fit in 8 bits")),D[E+i]=t}else for(i=0;i<B;++i)D[E+i]=g[i];return null!==A&&A.push(og,Q),Q},argPackAdvance:8,readValueFromPointer:wA,destructorFunction(A){og(A)}})},l:(A,g,I)=>{var B,C,Q,E;I=v(I),2===g?(B=qA,C=VA,E=XA,Q=A=>c[A>>1]):4===g&&(B=OA,C=$A,E=_A,Q=A=>w[A>>2]),z(A,{name:I,fromWireType:A=>{for(var I,C=w[A>>2],E=A+4,i=0;i<=C;++i){var t=A+4+i*g;if(i==C||0==Q(t)){var e=B(E,t-E);void 0===I?I=e:(I+=String.fromCharCode(0),I+=e),E=t+g}}return og(A),I},toWireType:(A,B)=>{"string"!=typeof B&&x(`Cannot pass non-string to C++ string type ${I}`);var Q=E(B),i=eg(4+Q+g);return w[i>>2]=Q/g,C(B,i+4,Q+g),null!==A&&A.push(og,i),i},argPackAdvance:8,readValueFromPointer:wA,destructorFunction(A){og(A)}})},v:(A,g)=>{z(A,{isVoid:!0,name:g=v(g),argPackAdvance:0,fromWireType:()=>{},toWireType:(A,g)=>{}})},s:(A,g,I)=>D.copyWithin(A,g,g+I),j:(A,g,I)=>(A=vA.toValue(A),g=Ag(g,"emval::as"),gg(g,I,A)),e:(A,g,I,B,C)=>{var Q,E;return(A=Bg[A])(g=vA.toValue(g),g[I=void 0===(E=Ig[Q=I])?v(Q):E],B,C)},c:KA,f:(A,g,I)=>{var B=((A,g)=>{for(var I=new Array(A),B=0;B<A;++B)I[B]=Ag(w[g+4*B>>2],"parameter "+B);return I})(A,g),C=B.shift();A--;var Q,E,i=new Array(A),t=`methodCaller<(${B.map((A=>A.name)).join(", ")}) => ${C.name}>`;return Q=sA(t,((g,Q,E,t)=>{for(var e=0,r=0;r<A;++r)i[r]=B[r].readValueFromPointer(t+e),e+=B[r].argPackAdvance;var o=1===I?Cg(Q,i):Q.apply(g,i);return gg(C,E,o)})),E=Bg.length,Bg.push(Q),E},d:A=>{A>9&&(HA[A+1]+=1)},b:A=>{var g=vA.toValue(A);SA(g),KA(A)},i:(A,g)=>{var I=(A=Ag(A,"_emval_take_value")).readValueFromPointer(g);return vA.toHandle(I)},r:A=>{D.length;R("OOM")},m:(A,g,I,B)=>{for(var C,Q,E,i=0,t=0;t<I;t++){var e=w[g>>2],r=w[g+4>>2];g+=8;for(var a=0;a<r;a++)C=A,Q=D[e+a],E=void 0,E=Qg[C],0===Q||10===Q?((1===C?o:s)(WA(E,0)),E.length=0):E.push(Q);i+=r}return w[B>>2]=i,0}},tg=function(){var A,g={a:ig};function I(A,g){var I,B;return tg=A.exports,r=tg.z,I=r.buffer,Q.HEAP8=n=new Int8Array(I),Q.HEAP16=y=new Int16Array(I),Q.HEAPU8=D=new Uint8Array(I),Q.HEAPU16=c=new Uint16Array(I),Q.HEAP32=G=new Int32Array(I),Q.HEAPU32=w=new Uint32Array(I),Q.HEAPF32=h=new Float32Array(I),Q.HEAPF64=N=new Float64Array(I),NA=tg.C,B=tg.A,F.unshift(B),function(){var A;if(Y--,null==(A=Q.monitorRunDependencies)||A.call(Q,Y),0==Y&&(null!==k&&(clearInterval(k),k=null),M)){var g=M;M=null,g()}}(),tg}if(Y++,null==(A=Q.monitorRunDependencies)||A.call(Q,Y),Q.instantiateWasm)try{return Q.instantiateWasm(g,I)}catch(A){s(`Module.instantiateWasm callback failed with error: ${A}`),B(A)}return S||(S="data:application/octet-stream;base64,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"),f(0,S,g,(function(A){I(A.instance)})).catch(B),{}}(),eg=A=>(eg=tg.B)(A),rg=A=>(rg=tg.D)(A),og=A=>(og=tg.E)(A),sg=A=>(sg=tg.F)(A);Q.dynCall_jiji=(A,g,I,B,C)=>(Q.dynCall_jiji=tg.G)(A,g,I,B,C),Q._vertexShaderSource=9872;function ag(){function A(){Eg||(Eg=!0,Q.calledRun=!0,d||(J(F),I(Q),Q.onRuntimeInitialized&&Q.onRuntimeInitialized(),function(){if(Q.postRun)for("function"==typeof Q.postRun&&(Q.postRun=[Q.postRun]);Q.postRun.length;)A=Q.postRun.shift(),u.unshift(A);var A;J(u)}()))}Y>0||(!function(){if(Q.preRun)for("function"==typeof Q.preRun&&(Q.preRun=[Q.preRun]);Q.preRun.length;)A=Q.preRun.shift(),l.unshift(A);var A;J(l)}(),Y>0||(Q.setStatus?(Q.setStatus("Running..."),setTimeout((function(){setTimeout((function(){Q.setStatus("")}),1),A()}),1)):A()))}if(M=function A(){Eg||ag(),Eg||(M=A)},Q.preInit)for("function"==typeof Q.preInit&&(Q.preInit=[Q.preInit]);Q.preInit.length>0;)Q.preInit.pop()();return ag(),E}})(),allin1_default=Module,bbSeq=0,_BasicBeauty=class A{constructor(A){this.core=A,__publicField(this,"seq"),__publicField(this,"_core"),__publicField(this,"log"),__publicField(this,"beautyParams"),bbSeq+=1,this.seq=bbSeq,this._core=A,this.log=A.log.createChild({id:`${this.getAlias()}${bbSeq}`}),this.log.info("created")}getName(){return A.Name}getAlias(){return"bb"}getValidateRule(A){switch(A){case"start":case"update":return startValidateRule(this._core);case"stop":return stopValidateRule(this._core)}}getGroup(){return"w"}async start(A){this._core.room.videoManager.Wasm||(this._core.room.videoManager.Wasm=await allin1_default()),this._core.room.videoManager.renderMode="webgl";const g=this._core.utils.isUndefined(A.beauty)?.5:A.beauty,I=this._core.utils.isUndefined(A.brightness)?.5:A.brightness,B=this._core.utils.isUndefined(A.ruddy)?.5:A.ruddy;return this._core.room.videoManager.setBeautyParams({beauty:g,brightness:I,ruddy:B})}async update(A){const g=this._core.utils.isUndefined(A.beauty)?.5:A.beauty,I=this._core.utils.isUndefined(A.brightness)?.5:A.brightness,B=this._core.utils.isUndefined(A.ruddy)?.5:A.ruddy;return this._core.room.videoManager.setBeautyParams({beauty:g,brightness:I,ruddy:B})}async stop(){return this._core.room.videoManager.renderMode="auto",this._core.room.videoManager.stopBeauty()}destroy(){this._core.room.videoManager.renderMode="auto"}};__publicField(_BasicBeauty,"Name","BasicBeauty");var BasicBeauty=_BasicBeauty,index_default=BasicBeauty;export{index_default as default};export{BasicBeauty};