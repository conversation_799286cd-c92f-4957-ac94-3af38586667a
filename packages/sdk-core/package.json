{"name": "trtc-js-sdk-core", "version": "5.0.0", "description": "TRTC Web SDK Core", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsc", "lint": "./node_modules/.bin/eslint ./src", "fix": "./node_modules/.bin/eslint ./src --fix"}, "author": "israeli", "license": "ISC", "devDependencies": {"@tensorflow-models/body-segmentation": "^1.0.2", "@tensorflow/tfjs-backend-webgl": "4.12.0-rc.0", "@tensorflow/tfjs-converter": "4.12.0-rc.0", "@tensorflow/tfjs-core": "^4.11.0", "@types/audioworklet": "^0.0.23", "@types/blueimp-md5": "^2.18.0", "@types/dom-mediacapture-transform": "^0.1.1", "@types/dom-webcodecs": "^0.1.11", "@types/wav-encoder": "^1.3.0", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.21.0", "eslint-config-tencent": "^1.0.4", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.7.1", "typescript": "~5.0.4"}, "dependencies": {"@mediapipe/selfie_segmentation": "^0.1.**********", "@tensorflow/tfjs-tflite": "0.0.1-alpha.10", "@trtc/js-sdk-types": "workspace:*", "@types/sdp-transform": "^2.4.5", "afsm": "2.4.9", "core-js": "^3.30.0", "eventemitter3": "4.0.7", "fastrx": "^3.3.1", "jv4-decoder": "^1.1.5", "oput": "^1.2.2", "sdp-transform": "^2.14.2"}}