import { ClientType, ClientTypeRequest, ClientTypeResponse, ClientTypeResponseMap, JoinRoomReq, ServerType, ServerTypeMap, ServerTypeResponse, SignalState } from './signal-types';
import type { SignalChannel } from './worker/signal-channel';

export const enum ConnectionEvent {
  TRACK_ADDED = 'track-added',
  TRACK_UPDATED = 'track-updated',
  TRACK_SUBSCRIBED = 'track-subscribed',
  STREAM_ADDED = 'stream-added',
  STREAM_REMOVED = 'stream-removed',
  STREAM_UPDATED = 'stream-updated',
  STREAM_PUBLISHED = 'stream-published',
  STREAM_SUBSCRIBED = 'stream-subscribed',
  STREAM_UNSUBSCRIBED = 'stream-unsubscribed',
  STATE_CHANGED = 'state-changed',
  ERROR = 'error',
  CONNECTION_STATE_CHANGED = 'connection-state-changed',
  FIREWALL_RESTRICTION = 'firewall-restriction',
  SEI_MESSAGE = 'sei-message',
  CLOSED = 'closed',
}

export const enum ConnectionState {
  CONNECTED = 'CONNECTED',
  DISCONNECTED = 'DISCONNECTED',
  CONNECTING = 'CONNECTING',
  RECONNECTED = 'RECONNECTED',
  RECONNECTING = 'RECONNECTING',
}
export const enum CMDToWorker {
  INSTALL_SCRIPT = 'installScript',
  LOG = 'log',
  JOIN_ROOM = 'joinRoom',
  DECODE_AUDIO = 'decodeAudio',
  PUBLISH = 'publish',
  SUBSCRIBE = 'subscribe',
  CHANGETYPE = 'changetype',
  PULL_VIDEO = 'pullVideo',
  PULL_AUX_VIDEO = 'pullAuxVideo',
  PULL_AUDIO = 'pullAudio',
  GET_AUDIO_ENERGY = 'getAudioEnergy',
  SYNC_USER_LIST = 'syncUserList',
  PUSH_AUDIO = 'pushAudio',
  SET_AUDIOWORKLET_PORT = 'setAudioWorkletPort',
  PUSH_VIDEO = 'pushVideo',
  ADDEVENTS = 'addEventMsgs',
  GET_LOCALVIDEOSTATS = 'getLocalVideoStats',
  GET_LOCALAUDIOSTATS = 'getLocalAudioStats',
  PUBLISH_CDN = 'publishCdn',
  PUBLISH_TX = 'publishTx',
  STOP_PUBLISH_CDN = 'stopPublishCdn',
  STOP_PUBLISH_TX = 'stopPublishTx',
  START_MCU_MIX = 'startMcuMix',
  STOP_MCU_MIX = 'stopMcuMix',
  WAIT_KEYFRAME = 'waitKeyFrame',
  SWITCH_ROLE = 'switchRole',
  PEER_REQUEST_PLI = 'peerRequestPLI',
}
export type CMDFromWorkerType<T> = T extends keyof CMDFromWorkerMap ? CMDFromWorkerMap[T] : never;
export type ToWorkerType<T extends CMDToWorker | ClientType> = {
  cmd: T,
  data?: T extends keyof CMDToWorkerMap ? CMDToWorkerMap[T] : ClientTypeRequest<T>;
};

export type FromWorkerType<T extends CMDFromWorker | ClientType | ServerType> = {
  cmd: T;
  data: T extends keyof CMDFromWorkerMap ? CMDFromWorkerMap[T] : T extends keyof ClientTypeResponseMap ? ClientTypeResponseMap[T] : T extends keyof ServerTypeMap ? ServerTypeMap[T] : never;
  code: number;
  message?: string;
};

export type CMDToWorkerMap = {
  [CMDToWorker.INSTALL_SCRIPT]: {
    name: string;
    script: string;
  },
  [CMDToWorker.JOIN_ROOM]: {
    id: number;
    userId: string;
    sdkAppId: number;
    data: JoinRoomReq;
    assetsPath?: string;
    env: string;
    audioWorkletPort?: MessagePort;
  },
};
export type CMDFromWorkerMap = {
  [CMDFromWorker.SIGNAL_STATE_UPDATED]: SignalState;
};
export const enum CMDFromWorker {
  PEER_JOIN = 'peer-join',
  PEER_LEAVE = 'peer-leave',
  SIGNAL_CONNECTION_STATE_CHANGED = 'signal-connection-state-changed',
  MEDIA_CONNECTION_STATE_CHANGED = 'media-connection-state-changed',
  BANNED = 'banned',
  NETWORK_QUALITY = 'network-quality',
  AUDIO_VOLUME = 'audio-volume',
  SEI_MESSAGE = 'sei-message',
  ERROR = 'error',
  REMOTE_PUBLISH_STATE_CHANGED = 'remote-publish-state-changed',
  REMOTE_PUBLISHED = 'remote-published',
  REMOTE_UNPUBLISHED = 'remote-unpublished',
  FIREWALL_RESTRICTION = ConnectionEvent.FIREWALL_RESTRICTION,
  HEARTBEAT_REPORT = 'heartbeat-report',
  CUSTOM_MESSAGE = 'custom-message',
  LAYER_DATA = 'layerData', // voov 鼠标信息
  FIRST_VIDEO_FRAME = 'first-video-frame',
  DUMP = 'dump',
  SWITCH_ROLE = 'switch-role',
  JOINED = 'join-success',
  REJOINED = 'rejoined',
  MY_PUBLISH_FLAG = 'my-flag',
  SIGNAL_STATE_UPDATED = 'signal-state-updated',
}

export const enum TopLevelCmdFromWorker {
  READY = 'ready',
  ERROR = 'error',
  UPDATE_AFSM = 'updateAFSM',
  LOG = 'log',
  GLOG = 'glog',
}
export type PostMessageParamType = Parameters<typeof MessagePort.prototype.postMessage>;
export type JoinRoomParamToWorker = {
  id: number;
  userId: string;
  sdkAppId: number;
  clientType: string;
  joinReqData: JoinRoomReq;
  assetsPath: string;
  env: string;
  audioWorkletPort?: MessagePort;
};
export type JoinRoomResultFromWorker = { err: number; data: any; };

export class WorkerHandler {
  constructor(public signalChannel: SignalChannel) {
  }
  async init() {

  }
  info(...args: any[]) {
    this.signalChannel.log.info(...args);
  }
  warn(...args: any[]) {
    this.signalChannel.log.warn(...args);
  }
  error(...args: any[]) {
    this.signalChannel.log.error(...args);
  }
}

export type WorkerGlobal = {
  registerSignalHandler: (opt: { prototype: any; }, ...Handler: typeof WorkerHandler[]) => void;
};
export const enum MediaType {
  NULL,
  BIG_VIDEO = 0x01,
  SMALL_VIDEO = 0x02,
  AUX_VIDEO = 0x04,
  AUDIO = 0x08,
  VIDEO_MUTED = 0x10,
  AUDIO_MUTED = 0x40,
  MUTE_ALL = 0x70,
  CROSS_ROOM = 0x80
}

