import { ChangeState, FSM } from 'afsm';
import { ConnectionState, CMDFromWorker } from '../worker-types';
import { Signal } from './signal';
import { logger } from './util';

type ConnectionStateType = {
  [key in ConnectionState]: []
};

export abstract class Connection extends Signal<{ [CMDFromWorker.SIGNAL_CONNECTION_STATE_CHANGED]: [{ prevState: ConnectionState, state: ConnectionState; }]; } & ConnectionStateType> {
  proxy!: string;
  proxy_cloud!: string[];
  proxy_unified?: string;
  env!: string;
  connectParams!: URLSearchParams;
  selectDomain = 0;
  selectCloudDomain = 0;
  domains!: string[];
  abortCtrl = new AbortController();
  get aborted() {
    return this.abortCtrl.signal.aborted;
  }
  abort = this.abortCtrl.abort.bind(this.abortCtrl);
  connectCost = 0; // 连接耗时
  closeInfo: WebTransportCloseInfo;
  constructor(options: {
    sdkAppId: number, userId: string, userSig: string, tinyId?: string;
    env?: string, proxy?: string, proxy_cloud?: string[], proxy_unified?: string;
  }) {
    super({
      logger: logger.createLogger({
        id: 'c',
        userId: options.userId,
        sdkAppId: options.sdkAppId
      })
    });
    this.env = options.env || '';
    this.connectParams = new URLSearchParams({ sdkAppId: options.sdkAppId.toString(), userSig: options.userSig, userId: options.userId });
    if (options.tinyId) this.connectParams.append('tinyId', options.tinyId);
  }

  abstract doConnect(): Promise<unknown>;

  @ChangeState(FSM.INIT, ConnectionState.CONNECTED)
  connect() {
    return this.doConnect();
  }

  @ChangeState([], ConnectionState.DISCONNECTED, { sync: true })
  disconnect(closeInfo: WebTransportCloseInfo) {
    this.closeInfo = closeInfo;
  }

  @ChangeState([], 'closed', { sync: true })
  close() {
  }
}
