import { ChangeState, FSM } from 'afsm';
import { ConnectionState, CMDFromWorker } from '../worker-types';
import { fromEvent, Observable, pipe, subscribe, take, takeUntil, zip } from 'fastrx';
import { SIGNAL_RECONNECTION_COUNT } from '../common/constants';
import { mcTimeout } from '../utils/rx';
import { getReconnectionTimeout } from '../utils';
import { Signal } from './signal';
import { logger } from './util';

type ConnectionStateType = {
  [key in ConnectionState]: []
};

export abstract class Connection extends Signal<{ [CMDFromWorker.SIGNAL_CONNECTION_STATE_CHANGED]: [{ prevState: ConnectionState, state: ConnectionState; }]; } & ConnectionStateType> {
  proxy!: string;
  proxy_cloud!: string[];
  proxy_unified?: string;
  env!: string;
  connectParams!: URLSearchParams;
  selectDomain = 0;
  selectCloudDomain = 0;
  domains!: string[];
  tinyId!: string;
  abortCtrl = new AbortController();
  get aborted() {
    return this.abortCtrl.signal.aborted;
  }
  abort = this.abortCtrl.abort.bind(this.abortCtrl);
  connectCost = 0; // 连接耗时
  constructor(options: {
    sdkAppId: number, userId: string, userSig: string, tinyId?: string;
    env?: string, proxy?: string, proxy_cloud?: string[], proxy_unified?: string;
  }) {
    super({
      logger: logger.createLogger({
        id: 'c',
        userId: options.userId,
        sdkAppId: options.sdkAppId
      })
    });
    this.env = options.env || '';
    this.connectParams = new URLSearchParams({ sdkAppId: options.sdkAppId.toString(), userSig: options.userSig, userId: options.userId });
    if (options.tinyId) this.connectParams.append('tinyId', options.tinyId);
  }
  @ChangeState(ConnectionState.DISCONNECTED, ConnectionState.RECONNECTED)
  async reconnect() {
    this.emit(CMDFromWorker.SIGNAL_CONNECTION_STATE_CHANGED, {
      prevState: ConnectionState.DISCONNECTED,
      state: ConnectionState.RECONNECTING
    });
    return this.doConnect();
  }
  reconnectedOB = fromEvent(this, ConnectionState.RECONNECTED);
  reconnectOB: Observable<Error> = sink => {
    this.reconnect().then(
      () => {
        this.emit(CMDFromWorker.SIGNAL_CONNECTION_STATE_CHANGED, {
          prevState: ConnectionState.RECONNECTING,
          state: ConnectionState.CONNECTED
        });
      },
      err => {
        sink.next(err);
        this.selectDomain = 1 - this.selectDomain;// 切换域名
      }
    );
  };
  retry(remainRetryCount = 0) {
    if (remainRetryCount > SIGNAL_RECONNECTION_COUNT) {
      this.close();
      return;
    }
    const timeout = mcTimeout(getReconnectionTimeout(remainRetryCount));
    pipe(
      remainRetryCount ? zip(
        timeout,
        this.reconnectOB
      ) : timeout,
      take(1),
      takeUntil(this.reconnectedOB), // 连接成功后终止定时器
      subscribe(err => {
        if (typeof err === 'number') return;
        if (this.state === ConnectionState.DISCONNECTED) this.retry(remainRetryCount + 1);
      })
    );
  }
  abstract doConnect(): Promise<unknown>;
  @ChangeState(FSM.INIT, ConnectionState.CONNECTED)
  async connect() {
    await  this.doConnect();
    this.emit(CMDFromWorker.SIGNAL_CONNECTION_STATE_CHANGED, {
      prevState: ConnectionState.DISCONNECTED,
      state: ConnectionState.CONNECTED
    });
    // let promise = this.wFSM.connect();
    // const downgrade = (selectDomain: 0 | 1, wt = false) => (err: any) => {
    //   if (this.aborted || typeof err === 'number') throw err;
    //   this.selectDomain = selectDomain;
    //   this.wFSM = wt ? new WebTransportFSM(this) : new WebSocketFSM(this);
    //   console.warn(err);
    //   console.warn('downgrade to', wt ? 'WebTransport' : 'WebSocket', this.selectDomain ? '备用域名' : '主域名');
    //   const p = this.wFSM.connect();
    //   return p;
    // };
    // // 前面使用wt协议建联失败，尝试使用ws协议建联
    // if (flag.useWt && (this.proxy_ws || !this.proxy_wt)) {
    //   promise = promise.catch(downgrade(0));
    // }
    // if (!this.proxy_wt && !this.proxy_ws) {
    //   promise = promise.catch(downgrade(1, flag.useWt));
    //   // 前面使用wt协议建联失败，尝试使用ws协议建联
    //   if (flag.useWt) {
    //     promise = promise.catch(downgrade(1));
    //   }
    // }
    // return promise;
  }
  @ChangeState([], ConnectionState.DISCONNECTED, { sync: true })
  disconnect(err: string) {
    this.emit(CMDFromWorker.SIGNAL_CONNECTION_STATE_CHANGED, {
      prevState: ConnectionState.CONNECTED,
      state: ConnectionState.DISCONNECTED
    });
    this.retry();
  }
  @ChangeState([], 'closed', { sync: true })
  close() {
    // this.userManager.clear();
    // this.port.postMessage(null);
    // this.port.close();
  }
}
