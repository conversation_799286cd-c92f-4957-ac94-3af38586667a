import { pipe, interval, map, share, takeWhile, takeUntil, fromEvent, subscribe } from 'fastrx';
import { ConnectionState } from '../worker-types';
import { abortable } from '../utils/rx';
import { Connection } from './connection';
import { ServerType } from '../signal-types';
const tick20msOb = pipe(interval(20), map(() => performance.now()), share());
const WebTransportEmptyError = new Error('WebTransport send empty datagram');
class Sent {
  chunk: Map<number, Uint8Array<ArrayBuffer>> = new Map();
  timestamp: number = performance.now();
  groupId: number;
  resentCount = 0;
  constructor(public wt: WebTransportConnection) {
    this.groupId = wt.groupid;
    wt.groupid = (wt.groupid + 1) & 0x7f;
    if (wt.sentCache.has(this.groupId)) {
      // 清除上一个相同groupid残留的缓存
      wt.sentCache.get(this.groupId)!.chunk.clear();
    }
    wt.sentCache.set(this.groupId, this);
    pipe(
      tick20msOb,
      takeWhile(() => this.chunk.size > 0 && this.resentCount < 20),
      takeUntil(fromEvent(wt, ConnectionState.DISCONNECTED)),
      subscribe(d => {
        if (d - this.timestamp > (2 * wt.rtt) + 10) {
          this.chunk.forEach((x, seq) => this.wt.send(x.buffer));
          wt.log.debug('resent', this.groupId, this.chunk.size, 'after', d - this.timestamp, 'ms', ++this.resentCount);
          this.timestamp = d;
        }
      }, err => wt.log.error(err), () => {
        // console.log("all sent", this.groupId, this.chunk.size);
        this.wt.sentCache.delete(this.groupId);
      })
    );
  }
  ack(seq: number) {
    this.chunk.delete(seq);
    // console.log("WebTransport ACK", this.groupId, seq, this.chunk.size,[...this.chunk.keys()]);
  }
}
export class WebTransportConnection extends Connection {
  wt?: WebTransport;
  writer?: WritableStreamDefaultWriter<ArrayBufferView | ArrayBufferLike>;
  dw?: WritableStreamDefaultWriter<ArrayBufferView | ArrayBufferLike>;
  sentCache: Map<number, Sent> = new Map();
  groupid = 0;
  mtu = 1000;
  get domain() {
    if (this.env) return `${this.env}.rtc.qq.com:8080`;
    if (this.proxy) return this.proxy;
    if (this.domains.length) return `${this.domains[this.selectDomain]}:80`;
    return 'wasm.rtc.qq.com:80';
  }
  get url() {
    return `https://${this.domain}/v3/wt?${this.connectParams}`;
  }
  async doConnect() {
    const start = performance.now();
    const wt = new WebTransport(this.url);
    wt.ready.then(() => {
      this.connectCost = performance.now() - start;
      this.log.info('WebTransport connected:', this.url, 'cost:', this.connectCost, 'ms');
    });
    const abortSignal = this.abortCtrl.signal;
    const { value, stream } = await Promise.race([
      (async () => {
        await abortable(wt.ready, abortSignal);
        const stream = await abortable(wt.createBidirectionalStream(), abortSignal);
        const reader: ReadableStreamDefaultReader<Uint8Array> = stream.readable.getReader();
        this.writer = stream.writable.getWriter();
        // this.wt.datagrams.incomingMaxAge = 30000;
        // this.wt.datagrams.incomingHighWaterMark = 10000;
        // this.wt.datagrams.outgoingMaxAge = 30000;
        // this.wt.datagrams.outgoingHighWaterMark = 10000;
        this.dw = wt.datagrams.writable.getWriter();
        return abortable(reader.read(), abortSignal).then(({ value }) => {
          reader.releaseLock();
          return { value, stream };
        });
      })(),
      new Promise<{ value: Uint8Array; stream: WebTransportBidirectionalStream; }>((resolve, reject) => setTimeout(() => reject(new Error('WebTransport connect timeout')), 1000))
    ]);
    const result = JSON.parse(String.fromCharCode(...value!));
    this.log.info('WebTransport connect result:', result);
    if (result.err !== 0) throw result.err;
    // this.verify(result.verify, result.rebuild);
    this.wt = wt;
    Promise.race([
      wt.datagrams.readable.pipeTo(new WritableStream(this.createOputReceiver('d'))),
      stream.readable.pipeTo(new WritableStream(this.createOputReceiver('s'))),
      wt.closed
    ]).then((closeInfo: WebTransportCloseInfo | void) => this.disconnect(closeInfo || { reason: 'completed' }), err => this.disconnect({ reason: err?.message }));
  }

  [ServerType[ServerType.ACK]]({ data }: { data: Uint8Array; }) {
    const id = data[0] & 0x7f;
    const sent = this.sentCache.get(id);
    if (!sent) {
      // console.debug("WebTransport ACK not found", id, data[1]);
      return;
    }
    sent.ack(data[1]);
  }

  async sendData(data: Uint8Array | ArrayBuffer) {
    if (!this.wt || !this.dw) return;
    if (data instanceof ArrayBuffer) {
      if (!data.byteLength) {
        throw WebTransportEmptyError;
      }
    } else {
      if (!data.length) {
        throw WebTransportEmptyError;
      }
      data = data.slice(0);
    }
    // if (!unreliable) {
    //   return this.writer.write(data).catch(this.catchError).then(this.writeDone);
    // }
    // await this.dw.ready;
    return this.dw.write(data).catch(this.catchError);
  }
  catchError(err: Error) {
    // console.log("WebTransport send error:", err);
  }
  close() {
    this.wt?.close();
    return super.close();
  }
}
