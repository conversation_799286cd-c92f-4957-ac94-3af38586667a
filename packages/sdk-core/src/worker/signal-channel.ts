/* eslint-disable no-restricted-syntax */
import { ChangeState, FSM } from 'afsm';
import { fromPort, logger } from './util';
import * as fastrx from 'fastrx';
import { pipe, combineLatest, fromEvent, subscribe, Observable, take, takeUntil, zip, expand } from 'fastrx';
import { abortable, mcInterval, mcTimeout } from '../utils/rx';
import { Connection } from './connection';
import { ClientType, ServerType, ServerTypeMap, SignalState } from '../signal-types';
import { WebSocketConnection } from './connection-ws';
import { CMDFromWorker, CMDToWorker, CMDToWorkerMap, ConnectionState, ToWorkerType } from '../worker-types';
import Logger from '../utils/log/logger';
import { ServerResponse } from './signal';
import { SIGNAL_RECONNECTION_COUNT } from '../common/constants';
import { getReconnectionTimeout } from '../utils';
import { UserManager } from '../manager/user-manager';
import { BasePublishedUser } from '..';
import { UserList } from './user-list';
import { WebTransportConnection } from './connection-wt';
export const signalChannelMap = new Map<number, SignalChannel>();
export const handlerFactory = new Map<string, { new(signalChannel: SignalChannel): any; }>();

export class SignalChannel extends FSM {
  id = 0;
  assetsPath = '';
  rx = { ...fastrx, mcInterval, mcTimeout, abortable };
  handlers = this.createHandlersObject(UserList);
  log: Logger;
  connection: Connection;
  tinyId = 0; // 用于标识当前信令通道的唯一 ID
  userManager: UserManager<BasePublishedUser>;// 用户管理器
  signalState: SignalState = {
    signalInfo: { clientIp: '', relayIp: '' },
    connectionState: ConnectionState.DISCONNECTED,
    isOnline: false
  };
  // 重连相关属性
  selectDomain = 0;
  reconnectedOB: Observable<ConnectionState> = fromEvent(this, ConnectionState.RECONNECTED);
  reconnectOB: Observable<Error> = (sink: { next: (value: Error) => void; }) => {
    this.reconnect().then(
      () => { },
      err => {
        sink.next(err);
        this.selectDomain = 1 - this.selectDomain; // 切换域名
      }
    );
  };
  createHandlersObject(...classes: { new(signalChannel: SignalChannel): any; }[]) {
    const handlers = {};
    classes.forEach(Handler => {
      Object.defineProperty(handlers, Handler.name, {
        enumerable: true,
        value: new Handler(this)
      });
    });
    return Object.create(handlers);
  }
  constructor(
    public options: CMDToWorkerMap[CMDToWorker.JOIN_ROOM],
    public port: MessagePort
  ) {
    super(`signalChannel${options.id}`, 'SignalChannel');
    this.id = options.id;
    this.assetsPath = options.assetsPath || '';
    this.log = logger.createLogger({ id: `sc${this.id}`, userId: options.userId, sdkAppId: options.sdkAppId });
    this.userManager = new UserManager<BasePublishedUser>(options.userId, this.log, 'tinyId');
    // 初始化时为当前实例创建所有已注册的 handler
    handlerFactory.forEach((Handler, name) => this.handlers[name] = new Handler(this));
    Promise.all([...this.forAllHandlers('init')]).then(() => this.joinRoom())
      .then(res => self.postMessage({ id: options.id, payload: res }))
      .catch(err => self.postMessage({ id: options.id, payload: { err } }));
    port.onmessage = (evt: MessageEvent<ToWorkerType<ClientType | CMDToWorker>>) => {
      if (evt.data.cmd === ClientType.LEAVE_ROOM) {
        this.leaveRoom();
        return;
      }
      // 所有处理器共享一个portOb
      const portObs: any[] = evt.ports.map(x => fromPort(x));
      if (typeof evt.data.data !== 'undefined') portObs.unshift(evt.data.data);
      let stopSendToServer = false;
      const cmd = ClientType[evt.data.cmd] || CMDToWorker[evt.data.cmd];
      for (name in this.handlers) {
        const handler = this.handlers[name];
        stopSendToServer = handler[cmd]?.call(handler, ...portObs);
      }
      if (!stopSendToServer) {
        // 如果没有处理器阻止发送，则将数据发送到服务器
        this.sendSignal(evt.data.cmd as ClientType, evt.data.data).then(res => {
          evt.ports[0].postMessage(res);
          evt.ports[0].close();
        });
      }
    };
    signalChannelMap.set(options.id, this);
  }
  * forAllHandlers(method: string, ...args: any[]) {
    for (const name in this.handlers) {
      const handler = this.handlers[name];
      if (typeof handler[method] === 'function') yield handler.call(...args);
    }
  }
  downloadAsset(assetName: string) {
    return fetch(`${this.assetsPath}${assetName}`)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to fetch asset: ${assetName}`);
        }
        return response.arrayBuffer();
      });
  }
  @ChangeState(FSM.INIT, ConnectionState.CONNECTED)
  async joinRoom() {
    // TODO: 进房，重进房，协议切换
    this.connection = new WebSocketConnection({ sdkAppId: this.options.sdkAppId, userId: this.options.userId, userSig: this.options.data.userSig, env: this.options.env });
    this.connection.onmessage = evt => this.onReceive(evt);
    this.connection.on(CMDFromWorker.SIGNAL_CONNECTION_STATE_CHANGED, (event: { state: ConnectionState; }) => {
      // 连接断开时启动重连
      if (event.state === ConnectionState.DISCONNECTED) {
        this.retry();
      }
    });

    await this.connection.connect();
    this.sendSignal(ClientType.JOIN_ROOM, this.options.data);
    return new Promise((resolve, reject) => {
      pipe(combineLatest(fromEvent(this, ServerType[ServerType.SIGNAL_CHANNEL_SETUP_RESULT]), fromEvent(this, ClientType[ClientType.JOIN_ROOM])), subscribe(([signalChannelSettupResult, joinResult]: [ServerResponse<ServerType.SIGNAL_CHANNEL_SETUP_RESULT>, ServerResponse<ClientType.JOIN_ROOM>]) => {
        if (signalChannelSettupResult.err || joinResult.err) {
          this.log.error('Join room failed', signalChannelSettupResult.err, joinResult.err);
          reject({ code: signalChannelSettupResult.err || joinResult.err, message: signalChannelSettupResult.data || joinResult.data });
        } else {
          this.log.info('Join room success', signalChannelSettupResult, joinResult);
          resolve(joinResult);
        }
      }));
    });
  }
  onReceive<T extends keyof ServerTypeMap>(evt: ServerResponse<T>) {
    this.log.info('SignalChannel onReceive', evt);
    const commandName = ServerType[evt.type] || ClientType[evt.type];
    let stopSendToMainThread = false;
    for (const name in this.handlers) {
      const handler = this.handlers[name];
      stopSendToMainThread = handler[commandName]?.call(handler, evt);
    }
    if (!stopSendToMainThread) {
      this.sendToMainThread(evt);
    }
    this.emit(commandName, evt.data);
  }
  @ChangeState([], FSM.INIT)
  leaveRoom() {
    this.sendSignal(ClientType.LEAVE_ROOM);
    signalChannelMap.delete(this.id);
    this.port.close();
  }
  sendSignal(cmd: ClientType, data: any = {}) {
    return this.connection.sendReq(cmd, data);
  }
  sendData(data: Uint8Array) {
    this.connection.sendData(data);
  }
  sendToMainThread(data: any) {
    if (this.port) {
      this.log.debug('SignalChannel sendToMainThread', data);
      this.port.postMessage(data);
    } else {
      this.log.warn('SignalChannel port is closed, cannot send message to main thread');
    }
  }

  updateSignalState(signalState: Partial<SignalState>) {
    this.signalState = { ...this.signalState, ...signalState };
    this.sendToMainThread({ cmd: CMDFromWorker.SIGNAL_STATE_UPDATED, data: this.signalState });
  }

  // 重连相关方法
  @ChangeState(ConnectionState.DISCONNECTED, ConnectionState.RECONNECTED)
  reconnect() {
    return this.connection.connect();
  }

  retry(remainRetryCount = 0) {
    if (remainRetryCount > SIGNAL_RECONNECTION_COUNT) {
      this.leaveRoom();
      return;
    }
    const timeout = mcTimeout(getReconnectionTimeout(remainRetryCount));
    pipe(
      remainRetryCount ? zip(
        timeout,
        this.reconnectOB
      ) : timeout,
      take(1),
      takeUntil(this.reconnectedOB), // 连接成功后终止定时器
      subscribe(err => {
        if (typeof err === 'number') return;
        if (this.state === ConnectionState.DISCONNECTED) this.retry(remainRetryCount + 1);
      })
    );
  }
}
export function registerSignalHandler(opt: { prototype: any; }, ...Handlers: { new(signalChannel: SignalChannel): any; }[]) {
  for (const Handler of Handlers) {
    const handlerName = Handler.name;
    handlerFactory.set(handlerName, Handler);

    signalChannelMap.forEach(signalChannel => {
      signalChannel.handlers[handlerName] = new Handler(signalChannel);
      signalChannel.handlers[handlerName].init?.();
    });
  }
}
