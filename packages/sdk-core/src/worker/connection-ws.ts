import { abortable } from '../utils/rx';
import { ConnectionState } from '../worker-types';
import { Connection } from './connection';

export class WebSocketConnection extends Connection {
  ws?: WebSocket;
  get domain() {
    if (this.env) return `${this.env}.rtc.qq.com`;
    if (this.proxy_cloud.length) return this.proxy_cloud[this.selectCloudDomain].replace('wss://', '');
    if (this.proxy) return this.proxy;
    if (this.domains.length) return this.domains[this.selectDomain];
    return 'wasm.rtc.qq.com';
  }
  get url() {
    // const realHost = this.domains?.[this.selectDomain] || 'wasm.rtc.qq.com';
    return `wss://${this.domain}/v3/ws?${this.connectParams}`;
  }
  async doConnect() {
    const start = performance.now();
    this.log.info('websocket connecting', this.url);
    const ws = new WebSocket(this.url);
    ws.binaryType = 'arraybuffer';
    return abortable((resolve, reject) => {
      ws.onerror = evt => {
        ws.onmessage = null;
        this.log.error('websocket onerror', evt);
        reject(evt.type);
      };
      ws.onclose = e => {
        ws.onmessage = null;
        reject(e);
        this.log.info('websocket onclose', e);
        super.disconnect(e.reason);
      };
      ws.onopen = () => {
        this.connectCost = performance.now() - start;
        this.log.info('websocket onopen', { cost: this.connectCost });
        this.ws = ws;
        resolve(ws);
      };
      const oput = this.createOputReceiver('w');
      ws.onmessage = evt => oput.write(evt.data);
      // ws.onmessage = evt => {
      //   try {
      //     const data = JSON.parse(String(evt.data));
      //     this.log.info('websocket verify', data);
      //     if (data.err !== 0) {
      //       throw data.err;
      //     }
      //     this.verify(data.verify, data.rebuild);
      //     this.ws = ws;
      //     resolve(ws);
      //     const oput = this.createOputReceiver('w');
      //     ws.onmessage = evt => oput.write(evt.data);
      //   } catch (e) {
      //     reject(e);
      //   }
      // };
    }, this.abortCtrl.signal);
  }
  close() {
    this.ws?.close(1000);
    return super.close();
  }
  sendData(data: Uint8Array | ArrayBuffer) {
    if (this.state === ConnectionState.CONNECTED) {
      this.ws?.send(data);
    }
  }
}
